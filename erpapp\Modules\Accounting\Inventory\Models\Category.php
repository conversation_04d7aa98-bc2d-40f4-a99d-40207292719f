<?php
namespace Modules\Accounting\Inventory\Models;

/**
 * Category Model
 * نموذج فئات المنتجات
 */
class Category {
    /**
     * Database connection
     *
     * @var \PDO
     */
    protected $db;

    /**
     * Constructor
     */
    public function __construct() {
        global $db;
        $this->db = $db;
    }

    /**
     * الحصول على جميع الفئات للشركة
     *
     * @param int $company_id معرف الشركة
     * @param bool $active_only الحصول على الفئات النشطة فقط
     * @return array
     */
    public function getAll($company_id, $active_only = true) {
        $where_clause = 'company_id = :company_id';
        if ($active_only) {
            $where_clause .= ' AND is_active = 1';
        }

        $sql = "
            SELECT c.*, 
                   parent.category_name_ar as parent_name_ar,
                   parent.category_name_en as parent_name_en,
                   COUNT(p.product_id) as product_count
            FROM inventory_categories c
            LEFT JOIN inventory_categories parent ON c.parent_category_id = parent.category_id
            LEFT JOIN inventory_products p ON c.category_id = p.category_id AND p.is_active = 1
            WHERE {$where_clause}
            GROUP BY c.category_id
            ORDER BY c.display_order ASC, c.category_name_ar ASC
        ";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':company_id', $company_id);
        $stmt->execute();
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على فئة بواسطة المعرف
     *
     * @param int $category_id معرف الفئة
     * @param int $company_id معرف الشركة
     * @return array|false
     */
    public function getById($category_id, $company_id) {
        $sql = "
            SELECT c.*, 
                   parent.category_name_ar as parent_name_ar,
                   parent.category_name_en as parent_name_en,
                   creator.UserName as created_by_name,
                   updater.UserName as updated_by_name
            FROM inventory_categories c
            LEFT JOIN inventory_categories parent ON c.parent_category_id = parent.category_id
            LEFT JOIN users creator ON c.created_by = creator.UserID
            LEFT JOIN users updater ON c.updated_by = updater.UserID
            WHERE c.category_id = :category_id AND c.company_id = :company_id
        ";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':category_id', $category_id);
        $stmt->bindParam(':company_id', $company_id);
        $stmt->execute();
        return $stmt->fetch(\PDO::FETCH_ASSOC);
    }

    /**
     * إنشاء فئة جديدة
     *
     * @param array $data بيانات الفئة
     * @return int|false معرف الفئة الجديدة أو false في حالة الفشل
     */
    public function create($data) {
        $sql = "
            INSERT INTO inventory_categories (
                company_id, module_code, parent_category_id, category_code, category_name_ar, 
                category_name_en, description_ar, description_en, image_url, display_order, 
                is_active, created_by
            ) VALUES (
                :company_id, :module_code, :parent_category_id, :category_code, :category_name_ar,
                :category_name_en, :description_ar, :description_en, :image_url, :display_order,
                :is_active, :created_by
            )
        ";

        $stmt = $this->db->prepare($sql);
        
        // تعيين القيم الافتراضية
        $data['module_code'] = $data['module_code'] ?? 'inventory';
        $data['is_active'] = $data['is_active'] ?? 1;
        $data['display_order'] = $data['display_order'] ?? 0;

        $stmt->bindParam(':company_id', $data['company_id']);
        $stmt->bindParam(':module_code', $data['module_code']);
        $stmt->bindParam(':parent_category_id', $data['parent_category_id']);
        $stmt->bindParam(':category_code', $data['category_code']);
        $stmt->bindParam(':category_name_ar', $data['category_name_ar']);
        $stmt->bindParam(':category_name_en', $data['category_name_en']);
        $stmt->bindParam(':description_ar', $data['description_ar']);
        $stmt->bindParam(':description_en', $data['description_en']);
        $stmt->bindParam(':image_url', $data['image_url']);
        $stmt->bindParam(':display_order', $data['display_order']);
        $stmt->bindParam(':is_active', $data['is_active']);
        $stmt->bindParam(':created_by', $data['created_by']);

        if ($stmt->execute()) {
            return $this->db->lastInsertId();
        }

        return false;
    }

    /**
     * تحديث فئة
     *
     * @param int $category_id معرف الفئة
     * @param array $data بيانات الفئة
     * @param int $company_id معرف الشركة
     * @return bool
     */
    public function update($category_id, $data, $company_id) {
        $sql = "
            UPDATE inventory_categories SET
                parent_category_id = :parent_category_id,
                category_code = :category_code,
                category_name_ar = :category_name_ar,
                category_name_en = :category_name_en,
                description_ar = :description_ar,
                description_en = :description_en,
                image_url = :image_url,
                display_order = :display_order,
                is_active = :is_active,
                updated_by = :updated_by,
                updated_at = NOW()
            WHERE category_id = :category_id AND company_id = :company_id
        ";

        $stmt = $this->db->prepare($sql);
        
        $stmt->bindParam(':category_id', $category_id);
        $stmt->bindParam(':company_id', $company_id);
        $stmt->bindParam(':parent_category_id', $data['parent_category_id']);
        $stmt->bindParam(':category_code', $data['category_code']);
        $stmt->bindParam(':category_name_ar', $data['category_name_ar']);
        $stmt->bindParam(':category_name_en', $data['category_name_en']);
        $stmt->bindParam(':description_ar', $data['description_ar']);
        $stmt->bindParam(':description_en', $data['description_en']);
        $stmt->bindParam(':image_url', $data['image_url']);
        $stmt->bindParam(':display_order', $data['display_order']);
        $stmt->bindParam(':is_active', $data['is_active']);
        $stmt->bindParam(':updated_by', $data['updated_by']);

        return $stmt->execute();
    }

    /**
     * حذف فئة
     *
     * @param int $category_id معرف الفئة
     * @param int $company_id معرف الشركة
     * @return bool
     */
    public function delete($category_id, $company_id) {
        // التحقق من عدم وجود منتجات في هذه الفئة
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as product_count 
            FROM inventory_products 
            WHERE category_id = :category_id AND company_id = :company_id
        ");
        $stmt->bindParam(':category_id', $category_id);
        $stmt->bindParam(':company_id', $company_id);
        $stmt->execute();
        $result = $stmt->fetch(\PDO::FETCH_ASSOC);

        if ($result['product_count'] > 0) {
            return false; // لا يمكن حذف فئة تحتوي على منتجات
        }

        // التحقق من عدم وجود فئات فرعية
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as subcategory_count 
            FROM inventory_categories 
            WHERE parent_category_id = :category_id AND company_id = :company_id
        ");
        $stmt->bindParam(':category_id', $category_id);
        $stmt->bindParam(':company_id', $company_id);
        $stmt->execute();
        $result = $stmt->fetch(\PDO::FETCH_ASSOC);

        if ($result['subcategory_count'] > 0) {
            return false; // لا يمكن حذف فئة تحتوي على فئات فرعية
        }

        $stmt = $this->db->prepare("
            DELETE FROM inventory_categories 
            WHERE category_id = :category_id AND company_id = :company_id
        ");
        $stmt->bindParam(':category_id', $category_id);
        $stmt->bindParam(':company_id', $company_id);

        return $stmt->execute();
    }

    /**
     * التحقق من وجود كود الفئة
     *
     * @param string $category_code كود الفئة
     * @param int $company_id معرف الشركة
     * @param int|null $exclude_id معرف الفئة المستثناة (للتحديث)
     * @return bool
     */
    public function isCategoryCodeExists($category_code, $company_id, $exclude_id = null) {
        $sql = "SELECT COUNT(*) as count FROM inventory_categories WHERE category_code = :category_code AND company_id = :company_id";
        $params = [':category_code' => $category_code, ':company_id' => $company_id];

        if ($exclude_id) {
            $sql .= " AND category_id != :exclude_id";
            $params[':exclude_id'] = $exclude_id;
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetch(\PDO::FETCH_ASSOC);

        return $result['count'] > 0;
    }

    /**
     * الحصول على الفئات الرئيسية فقط
     *
     * @param int $company_id معرف الشركة
     * @return array
     */
    public function getMainCategories($company_id) {
        $sql = "
            SELECT * FROM inventory_categories 
            WHERE company_id = :company_id 
            AND parent_category_id IS NULL 
            AND is_active = 1
            ORDER BY display_order ASC, category_name_ar ASC
        ";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':company_id', $company_id);
        $stmt->execute();
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على الفئات الفرعية لفئة معينة
     *
     * @param int $parent_id معرف الفئة الرئيسية
     * @param int $company_id معرف الشركة
     * @return array
     */
    public function getSubCategories($parent_id, $company_id) {
        $sql = "
            SELECT * FROM inventory_categories 
            WHERE company_id = :company_id 
            AND parent_category_id = :parent_id 
            AND is_active = 1
            ORDER BY display_order ASC, category_name_ar ASC
        ";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':company_id', $company_id);
        $stmt->bindParam(':parent_id', $parent_id);
        $stmt->execute();
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على الفئات في شكل هرمي
     *
     * @param int $company_id معرف الشركة
     * @return array
     */
    public function getHierarchical($company_id) {
        $categories = $this->getAll($company_id);
        return $this->buildHierarchy($categories);
    }

    /**
     * بناء الهيكل الهرمي للفئات
     *
     * @param array $categories قائمة الفئات
     * @param int|null $parent_id معرف الفئة الرئيسية
     * @return array
     */
    private function buildHierarchy($categories, $parent_id = null) {
        $hierarchy = [];
        
        foreach ($categories as $category) {
            if ($category['parent_category_id'] == $parent_id) {
                $category['children'] = $this->buildHierarchy($categories, $category['category_id']);
                $hierarchy[] = $category;
            }
        }
        
        return $hierarchy;
    }
}
