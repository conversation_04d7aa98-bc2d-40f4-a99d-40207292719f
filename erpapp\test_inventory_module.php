<?php
/**
 * Test Inventory Module Loading - اختبار تحميل وحدة المخزون
 */

echo "<h1>اختبار تحميل وحدة المخزون</h1>";

// تحميل النظام
require_once 'loader.php';

echo "<h2>1. فحص ملفات الوحدة:</h2>";

$files_to_check = [
    'App/Modules/Inventory/Module.php',
    'App/Modules/Inventory/Controllers/InventoryController.php',
    'App/Modules/Inventory/Controllers/ProductController.php',
    'App/Modules/Inventory/Models/Product.php',
    'App/Modules/Inventory/Services/InventoryService.php',
    'App/Modules/Inventory/Views/dashboard/index.php'
];

foreach ($files_to_check as $file) {
    $full_path = BASE_PATH . '/' . $file;
    $exists = file_exists($full_path);
    $color = $exists ? 'green' : 'red';
    $status = $exists ? '✅ موجود' : '❌ غير موجود';
    echo "<p style='color: $color;'>$file - $status</p>";
}

echo "<h2>2. اختبار تحميل الفئات:</h2>";

try {
    // محاولة تحميل وحدة المخزون
    $inventoryClass = 'App\Modules\Inventory\Module';
    echo "<p>فحص فئة: $inventoryClass</p>";
    
    if (class_exists($inventoryClass)) {
        echo "<p style='color: green;'>✅ فئة وحدة المخزون موجودة</p>";
        
        $inventoryModule = new $inventoryClass();
        echo "<p style='color: green;'>✅ تم إنشاء كائن وحدة المخزون</p>";
        
        // اختبار تسجيل المسارات
        $inventoryModule->registerRoutes();
        echo "<p style='color: green;'>✅ تم تسجيل مسارات وحدة المخزون</p>";
        
    } else {
        echo "<p style='color: red;'>❌ فئة وحدة المخزون غير موجودة</p>";
    }
    
    // محاولة تحميل Controller
    $controllerClass = 'App\Modules\Inventory\Controllers\InventoryController';
    echo "<p>فحص فئة: $controllerClass</p>";
    
    if (class_exists($controllerClass)) {
        echo "<p style='color: green;'>✅ فئة InventoryController موجودة</p>";
        
        $controller = new $controllerClass();
        echo "<p style='color: green;'>✅ تم إنشاء كائن InventoryController</p>";
    } else {
        echo "<p style='color: red;'>❌ فئة InventoryController غير موجودة</p>";
    }
    
    // محاولة تحميل ProductController
    $productControllerClass = 'App\Modules\Inventory\Controllers\ProductController';
    echo "<p>فحص فئة: $productControllerClass</p>";
    
    if (class_exists($productControllerClass)) {
        echo "<p style='color: green;'>✅ فئة ProductController موجودة</p>";
        
        $productController = new $productControllerClass();
        echo "<p style='color: green;'>✅ تم إنشاء كائن ProductController</p>";
    } else {
        echo "<p style='color: red;'>❌ فئة ProductController غير موجودة</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}

echo "<h2>3. اختبار المسارات المسجلة:</h2>";

try {
    // فحص المسارات المسجلة
    $moduleRouter = \App\Core\ModuleRouter::getInstance();
    $routes = $moduleRouter->getRoutes();
    
    $inventoryRoutes = array_filter($routes, function($route) {
        return strpos($route['route'], '/inventory') !== false;
    });
    
    echo "<p>عدد مسارات المخزون المسجلة: " . count($inventoryRoutes) . "</p>";
    
    if (count($inventoryRoutes) > 0) {
        echo "<h4>المسارات المسجلة:</h4>";
        echo "<ul>";
        foreach ($inventoryRoutes as $route) {
            $methods = is_array($route['methods']) ? implode(', ', $route['methods']) : $route['methods'];
            $handler = is_array($route['handler']) ? 
                $route['handler']['controller'] . "@" . $route['handler']['action'] : 
                $route['handler'];
            echo "<li>$methods " . $route['route'] . " → " . $handler . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>❌ لا توجد مسارات مسجلة للمخزون</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في فحص المسارات: " . $e->getMessage() . "</p>";
}

echo "<h2>4. اختبار مطابقة المسار:</h2>";

try {
    $moduleRouter = \App\Core\ModuleRouter::getInstance();
    
    if ($moduleRouter->match('/inventory', 'GET')) {
        echo "<p style='color: green;'>✅ مسار /inventory يتطابق!</p>";
        
        $params = $moduleRouter->getParams();
        echo "<h4>معاملات المسار:</h4>";
        echo "<pre>" . print_r($params, true) . "</pre>";
        
    } else {
        echo "<p style='color: red;'>❌ مسار /inventory لا يتطابق</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في اختبار المسار: " . $e->getMessage() . "</p>";
}

echo "<h2>5. اختبار تحميل الوحدات:</h2>";

try {
    echo "<p>اختبار دالة load_modules()...</p>";
    
    // إعادة تحميل الوحدات
    load_modules();
    echo "<p style='color: green;'>✅ تم تحميل الوحدات بنجاح</p>";
    
    // فحص المسارات مرة أخرى
    $moduleRouter = \App\Core\ModuleRouter::getInstance();
    $routes = $moduleRouter->getRoutes();
    
    $inventoryRoutes = array_filter($routes, function($route) {
        return strpos($route['route'], '/inventory') !== false;
    });
    
    echo "<p>عدد مسارات المخزون بعد إعادة التحميل: " . count($inventoryRoutes) . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في تحميل الوحدات: " . $e->getMessage() . "</p>";
}

echo "<h2>6. معلومات إضافية:</h2>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Current Directory: " . getcwd() . "</p>";
echo "<p>BASE_PATH: " . (defined('BASE_PATH') ? BASE_PATH : 'غير محدد') . "</p>";

// فحص الفئات المحملة
echo "<h3>الفئات المحملة من Modules:</h3>";
$classes = get_declared_classes();
$moduleClasses = array_filter($classes, function($class) {
    return strpos($class, 'App\\Modules\\') === 0;
});

if (count($moduleClasses) > 0) {
    echo "<ul>";
    foreach ($moduleClasses as $class) {
        echo "<li>$class</li>";
    }
    echo "</ul>";
} else {
    echo "<p>لا توجد فئات محملة من App\\Modules</p>";
}

echo "<h2>7. اختبار مباشر للمسار:</h2>";
echo "<p><a href='/inventory' target='_blank'>اختبار مسار /inventory</a></p>";
echo "<p><a href='/inventory/products' target='_blank'>اختبار مسار /inventory/products</a></p>";
echo "<p><a href='/inventory/dashboard' target='_blank'>اختبار مسار /inventory/dashboard</a></p>";
?>
