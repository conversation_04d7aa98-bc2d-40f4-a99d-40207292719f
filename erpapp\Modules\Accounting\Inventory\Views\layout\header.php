<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'وحدة إدارة المخزون' ?> - نظام ERP</title>

    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- DataTables CSS (RTL) -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">

    <!-- Toastr CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }

        .page-title {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 0;
        }

        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .card-header {
            border-radius: 10px 10px 0 0 !important;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
        }

        .btn {
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        .table {
            border-radius: 8px;
            overflow: hidden;
        }

        .table thead th {
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.875rem;
            letter-spacing: 0.5px;
        }

        .badge {
            font-size: 0.75rem;
            font-weight: 500;
            padding: 0.375rem 0.75rem;
        }

        .text-gray-300 {
            color: #d1d3e2 !important;
        }

        .text-gray-800 {
            color: #5a5c69 !important;
        }

        .border-left-primary {
            border-left: 0.25rem solid #4e73df !important;
        }

        .border-left-success {
            border-left: 0.25rem solid #1cc88a !important;
        }

        .border-left-info {
            border-left: 0.25rem solid #36b9cc !important;
        }

        .border-left-warning {
            border-left: 0.25rem solid #f6c23e !important;
        }

        .border-left-danger {
            border-left: 0.25rem solid #e74a3b !important;
        }

        .shadow {
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
        }

        .progress {
            border-radius: 10px;
            height: 0.5rem;
        }

        .progress-bar {
            border-radius: 10px;
        }

        .form-control, .form-select {
            border-radius: 6px;
            border: 1px solid #d1d3e2;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #4e73df;
            box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
        }

        .modal-content {
            border-radius: 10px;
            border: none;
        }

        .modal-header {
            border-radius: 10px 10px 0 0;
        }

        .alert {
            border-radius: 8px;
            border: none;
        }

        /* Inventory specific styles */
        .inventory-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .inventory-card .card-body {
            position: relative;
            overflow: hidden;
        }

        .inventory-card .card-body::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: rgba(255, 255, 255, 0.1);
            transform: rotate(45deg);
            transition: all 0.3s ease;
        }

        .inventory-card:hover .card-body::before {
            top: -60%;
            right: -60%;
        }

        .stock-status-available {
            background-color: #28a745;
        }

        .stock-status-low {
            background-color: #ffc107;
        }

        .stock-status-out {
            background-color: #dc3545;
        }

        .stock-status-reorder {
            background-color: #fd7e14;
        }

        /* Animation classes */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in-right {
            animation: slideInRight 0.5s ease-out;
        }

        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .page-title {
                font-size: 1.5rem;
            }

            .btn-group {
                flex-direction: column;
                width: 100%;
            }

            .btn-group .btn {
                margin-bottom: 0.5rem;
            }
        }

        /* Print styles */
        @media print {
            .btn, .card-header, .modal, .alert {
                display: none !important;
            }

            .card {
                border: 1px solid #000 !important;
                box-shadow: none !important;
            }

            body {
                background-color: white !important;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Breadcrumb -->
    <nav aria-label="breadcrumb" class="bg-white shadow-sm border-bottom mb-4">
        <div class="container-fluid">
            <ol class="breadcrumb py-3 mb-0">
                <li class="breadcrumb-item">
                    <a href="<?= base_url('dashboard') ?>" class="text-decoration-none">
                        <i class="fas fa-home me-1"></i> الرئيسية
                    </a>
                </li>
                <li class="breadcrumb-item">
                    <a href="<?= base_url('inventory') ?>" class="text-decoration-none">
                        <i class="fas fa-boxes me-1"></i> المخزون
                    </a>
                </li>
                <?php if (isset($breadcrumb)): ?>
                    <?php foreach ($breadcrumb as $item): ?>
                        <?php if (isset($item['url'])): ?>
                            <li class="breadcrumb-item">
                                <a href="<?= $item['url'] ?>" class="text-decoration-none">
                                    <?php if (isset($item['icon'])): ?>
                                        <i class="<?= $item['icon'] ?> me-1"></i>
                                    <?php endif; ?>
                                    <?= $item['title'] ?>
                                </a>
                            </li>
                        <?php else: ?>
                            <li class="breadcrumb-item active" aria-current="page">
                                <?php if (isset($item['icon'])): ?>
                                    <i class="<?= $item['icon'] ?> me-1"></i>
                                <?php endif; ?>
                                <?= $item['title'] ?>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                <?php endif; ?>
            </ol>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="fade-in">
