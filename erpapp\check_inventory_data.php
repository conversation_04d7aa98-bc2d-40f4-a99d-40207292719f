<?php
/**
 * فحص بيانات المخزون في قاعدة البيانات
 */

require_once 'loader.php';

echo "<h1>فحص بيانات المخزون</h1>";

try {
    global $db;
    
    echo "<h2>1. فحص الجداول:</h2>";
    
    // فحص وجود الجداول
    $tables = [
        'inventory_products',
        'inventory_categories', 
        'inventory_units',
        'inventory_warehouses',
        'inventory_stock',
        'inventory_movements'
    ];
    
    foreach ($tables as $table) {
        $sql = "SHOW TABLES LIKE '$table'";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $exists = $stmt->fetch();
        
        $color = $exists ? 'green' : 'red';
        $status = $exists ? '✅ موجود' : '❌ غير موجود';
        echo "<p style='color: $color;'>جدول $table - $status</p>";
    }
    
    echo "<h2>2. عدد السجلات في كل جدول:</h2>";
    
    foreach ($tables as $table) {
        try {
            $sql = "SELECT COUNT(*) FROM $table";
            $stmt = $db->prepare($sql);
            $stmt->execute();
            $count = $stmt->fetchColumn();
            
            $color = $count > 0 ? 'green' : 'orange';
            echo "<p style='color: $color;'>$table: $count سجل</p>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>$table: خطأ - " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>3. فحص بيانات الشركة رقم 1:</h2>";
    
    // فحص المنتجات للشركة 1
    $sql = "SELECT COUNT(*) FROM inventory_products WHERE company_id = 1";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $products_count = $stmt->fetchColumn();
    echo "<p>عدد المنتجات للشركة 1: <strong>$products_count</strong></p>";
    
    // فحص الفئات للشركة 1
    $sql = "SELECT COUNT(*) FROM inventory_categories WHERE company_id = 1";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $categories_count = $stmt->fetchColumn();
    echo "<p>عدد الفئات للشركة 1: <strong>$categories_count</strong></p>";
    
    // فحص وحدات القياس للشركة 1
    $sql = "SELECT COUNT(*) FROM inventory_units WHERE company_id = 1";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $units_count = $stmt->fetchColumn();
    echo "<p>عدد وحدات القياس للشركة 1: <strong>$units_count</strong></p>";
    
    // فحص المخازن للشركة 1
    $sql = "SELECT COUNT(*) FROM inventory_warehouses WHERE company_id = 1";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $warehouses_count = $stmt->fetchColumn();
    echo "<p>عدد المخازن للشركة 1: <strong>$warehouses_count</strong></p>";
    
    echo "<h2>4. فحص البيانات التجريبية:</h2>";
    
    // فحص إذا كانت البيانات التجريبية موجودة
    if ($products_count == 0) {
        echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;'>";
        echo "<h3>💡 لا توجد بيانات تجريبية</h3>";
        echo "<p>لرؤية البيانات الحقيقية، يمكنك:</p>";
        echo "<ol>";
        echo "<li><strong>تشغيل ملف البيانات التجريبية:</strong> inventory_data_company_4.sql</li>";
        echo "<li><strong>إضافة منتجات يدوياً</strong> من خلال واجهة النظام</li>";
        echo "<li><strong>استيراد بيانات</strong> من نظام آخر</li>";
        echo "</ol>";
        echo "</div>";
        
        echo "<h3>📝 نموذج لإدراج بيانات تجريبية:</h3>";
        echo "<textarea style='width: 100%; height: 200px; font-family: monospace;'>";
        echo "-- إدراج وحدة قياس\n";
        echo "INSERT INTO inventory_units (company_id, unit_code, unit_name_ar, unit_symbol_ar, is_base_unit, created_by) \n";
        echo "VALUES (1, 'PIECE', 'قطعة', 'قطعة', 1, 1);\n\n";
        
        echo "-- إدراج فئة\n";
        echo "INSERT INTO inventory_categories (company_id, category_code, category_name_ar, created_by) \n";
        echo "VALUES (1, 'ELECTRONICS', 'إلكترونيات', 1);\n\n";
        
        echo "-- إدراج مخزن\n";
        echo "INSERT INTO inventory_warehouses (company_id, warehouse_code, warehouse_name_ar, created_by) \n";
        echo "VALUES (1, 'MAIN', 'المخزن الرئيسي', 1);\n\n";
        
        echo "-- إدراج منتج\n";
        echo "INSERT INTO inventory_products (company_id, product_code, product_name_ar, category_id, unit_id, cost_price, selling_price, created_by) \n";
        echo "VALUES (1, 'LAPTOP001', 'لابتوب ديل', 1, 1, 2000.00, 2500.00, 1);\n";
        echo "</textarea>";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
        echo "<h3>✅ توجد بيانات في قاعدة البيانات</h3>";
        echo "<p>البيانات المعروضة في لوحة التحكم هي <strong>بيانات حقيقية</strong> من قاعدة البيانات.</p>";
        echo "</div>";
        
        // عرض بعض البيانات الموجودة
        echo "<h3>📋 عينة من البيانات الموجودة:</h3>";
        
        if ($products_count > 0) {
            $sql = "SELECT product_code, product_name_ar, cost_price, selling_price FROM inventory_products WHERE company_id = 1 LIMIT 5";
            $stmt = $db->prepare($sql);
            $stmt->execute();
            $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h4>المنتجات:</h4>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>كود المنتج</th><th>اسم المنتج</th><th>سعر التكلفة</th><th>سعر البيع</th></tr>";
            foreach ($products as $product) {
                echo "<tr>";
                echo "<td>" . $product['product_code'] . "</td>";
                echo "<td>" . $product['product_name_ar'] . "</td>";
                echo "<td>" . number_format($product['cost_price'], 2) . " ر.س</td>";
                echo "<td>" . number_format($product['selling_price'], 2) . " ر.س</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    echo "<h2>5. اختبار الاتصال بقاعدة البيانات:</h2>";
    
    $sql = "SELECT NOW() as current_time, DATABASE() as database_name";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $info = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<p>✅ <strong>الاتصال بقاعدة البيانات يعمل</strong></p>";
    echo "<p>قاعدة البيانات: <strong>" . $info['database_name'] . "</strong></p>";
    echo "<p>الوقت الحالي: <strong>" . $info['current_time'] . "</strong></p>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h3>❌ خطأ في الاتصال بقاعدة البيانات</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='/inventory'>← العودة إلى وحدة المخزون</a></p>";
?>
