-- ===================================
-- جداول وحدة المخزون (Inventory Module)
-- ===================================

-- جدول فئات المنتجات
CREATE TABLE `inventory_categories` (
  `category_id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL,
  `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'inventory',
  `parent_category_id` int DEFAULT NULL,
  `category_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `category_name_ar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `category_name_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `description_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `description_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `image_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `display_order` int NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`category_id`),
  UNIQUE KEY `company_category_code_unique` (`company_id`, `category_code`),
  KEY `company_id` (`company_id`),
  KEY `parent_category_id` (`parent_category_id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  KEY `module_code` (`module_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول وحدات القياس
CREATE TABLE `inventory_units` (
  `unit_id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL,
  `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'inventory',
  `unit_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `unit_name_ar` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `unit_name_en` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `unit_symbol_ar` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `unit_symbol_en` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `base_unit_id` int DEFAULT NULL,
  `conversion_factor` decimal(10,4) DEFAULT '1.0000',
  `is_base_unit` tinyint(1) NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`unit_id`),
  UNIQUE KEY `company_unit_code_unique` (`company_id`, `unit_code`),
  KEY `company_id` (`company_id`),
  KEY `base_unit_id` (`base_unit_id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  KEY `module_code` (`module_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول المخازن
CREATE TABLE `inventory_warehouses` (
  `warehouse_id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL,
  `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'inventory',
  `warehouse_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `warehouse_name_ar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `warehouse_name_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `address` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `manager_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `capacity` decimal(15,2) DEFAULT NULL,
  `current_usage` decimal(15,2) DEFAULT '0.00',
  `warehouse_type` enum('main','branch','virtual','external') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'main',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`warehouse_id`),
  UNIQUE KEY `company_warehouse_code_unique` (`company_id`, `warehouse_code`),
  KEY `company_id` (`company_id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  KEY `module_code` (`module_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول المنتجات
CREATE TABLE `inventory_products` (
  `product_id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL,
  `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'inventory',
  `product_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `barcode` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `product_name_ar` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `product_name_en` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `description_ar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `description_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `category_id` int NOT NULL,
  `unit_id` int NOT NULL,
  `product_type` enum('product','service','digital') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'product',
  `track_inventory` tinyint(1) NOT NULL DEFAULT '1',
  `cost_price` decimal(15,2) DEFAULT '0.00',
  `selling_price` decimal(15,2) DEFAULT '0.00',
  `min_stock_level` decimal(15,2) DEFAULT '0.00',
  `max_stock_level` decimal(15,2) DEFAULT NULL,
  `reorder_point` decimal(15,2) DEFAULT '0.00',
  `weight` decimal(10,3) DEFAULT NULL,
  `dimensions` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `image_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `tax_rate` decimal(5,2) DEFAULT '0.00',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`product_id`),
  UNIQUE KEY `company_product_code_unique` (`company_id`, `product_code`),
  UNIQUE KEY `company_barcode_unique` (`company_id`, `barcode`),
  KEY `company_id` (`company_id`),
  KEY `category_id` (`category_id`),
  KEY `unit_id` (`unit_id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  KEY `module_code` (`module_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول أرصدة المخزون
CREATE TABLE `inventory_stock` (
  `stock_id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL,
  `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'inventory',
  `product_id` int NOT NULL,
  `warehouse_id` int NOT NULL,
  `quantity_on_hand` decimal(15,3) NOT NULL DEFAULT '0.000',
  `quantity_reserved` decimal(15,3) NOT NULL DEFAULT '0.000',
  `quantity_available` decimal(15,3) NOT NULL DEFAULT '0.000',
  `average_cost` decimal(15,2) DEFAULT '0.00',
  `last_cost` decimal(15,2) DEFAULT '0.00',
  `last_movement_date` timestamp NULL DEFAULT NULL,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`stock_id`),
  UNIQUE KEY `company_product_warehouse_unique` (`company_id`, `product_id`, `warehouse_id`),
  KEY `company_id` (`company_id`),
  KEY `product_id` (`product_id`),
  KEY `warehouse_id` (`warehouse_id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  KEY `module_code` (`module_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول حركات المخزون
CREATE TABLE `inventory_movements` (
  `movement_id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL,
  `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'inventory',
  `movement_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `product_id` int NOT NULL,
  `warehouse_id` int NOT NULL,
  `movement_type` enum('in','out','transfer','adjustment') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `movement_reason` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `quantity` decimal(15,3) NOT NULL,
  `unit_cost` decimal(15,2) DEFAULT '0.00',
  `total_cost` decimal(15,2) DEFAULT '0.00',
  `reference_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `reference_id` int DEFAULT NULL,
  `reference_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `movement_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`movement_id`),
  UNIQUE KEY `company_movement_number_unique` (`company_id`, `movement_number`),
  KEY `company_id` (`company_id`),
  KEY `product_id` (`product_id`),
  KEY `warehouse_id` (`warehouse_id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  KEY `module_code` (`module_code`),
  KEY `reference_type_id` (`reference_type`, `reference_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول التحويلات بين المخازن
CREATE TABLE `inventory_transfers` (
  `transfer_id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL,
  `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'inventory',
  `transfer_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `from_warehouse_id` int NOT NULL,
  `to_warehouse_id` int NOT NULL,
  `transfer_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` enum('pending','in_transit','completed','cancelled') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'pending',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `total_items` int DEFAULT '0',
  `total_quantity` decimal(15,3) DEFAULT '0.000',
  `total_value` decimal(15,2) DEFAULT '0.00',
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `approved_by` int DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`transfer_id`),
  UNIQUE KEY `company_transfer_number_unique` (`company_id`, `transfer_number`),
  KEY `company_id` (`company_id`),
  KEY `from_warehouse_id` (`from_warehouse_id`),
  KEY `to_warehouse_id` (`to_warehouse_id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  KEY `approved_by` (`approved_by`),
  KEY `module_code` (`module_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول تفاصيل التحويلات
CREATE TABLE `inventory_transfer_items` (
  `transfer_item_id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL,
  `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'inventory',
  `transfer_id` int NOT NULL,
  `product_id` int NOT NULL,
  `quantity_requested` decimal(15,3) NOT NULL,
  `quantity_sent` decimal(15,3) DEFAULT '0.000',
  `quantity_received` decimal(15,3) DEFAULT '0.000',
  `unit_cost` decimal(15,2) DEFAULT '0.00',
  `total_cost` decimal(15,2) DEFAULT '0.00',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`transfer_item_id`),
  KEY `company_id` (`company_id`),
  KEY `transfer_id` (`transfer_id`),
  KEY `product_id` (`product_id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  KEY `module_code` (`module_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول تسعير المنتجات
CREATE TABLE `inventory_product_prices` (
  `price_id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL,
  `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'inventory',
  `product_id` int NOT NULL,
  `price_type` enum('cost','selling','wholesale','retail','special') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `price` decimal(15,2) NOT NULL,
  `currency` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'SAR',
  `min_quantity` decimal(15,3) DEFAULT '1.000',
  `max_quantity` decimal(15,3) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`price_id`),
  KEY `company_id` (`company_id`),
  KEY `product_id` (`product_id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  KEY `module_code` (`module_code`),
  KEY `price_type_active` (`price_type`, `is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول الجرد الدوري
CREATE TABLE `inventory_audits` (
  `audit_id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL,
  `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'inventory',
  `audit_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `audit_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `warehouse_id` int NOT NULL,
  `audit_date` date NOT NULL,
  `status` enum('planned','in_progress','completed','cancelled') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'planned',
  `audit_type` enum('full','partial','cycle') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'full',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `total_items_counted` int DEFAULT '0',
  `total_discrepancies` int DEFAULT '0',
  `total_value_difference` decimal(15,2) DEFAULT '0.00',
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `completed_by` int DEFAULT NULL,
  `completed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`audit_id`),
  UNIQUE KEY `company_audit_number_unique` (`company_id`, `audit_number`),
  KEY `company_id` (`company_id`),
  KEY `warehouse_id` (`warehouse_id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  KEY `completed_by` (`completed_by`),
  KEY `module_code` (`module_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول تفاصيل الجرد
CREATE TABLE `inventory_audit_items` (
  `audit_item_id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL,
  `module_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'inventory',
  `audit_id` int NOT NULL,
  `product_id` int NOT NULL,
  `system_quantity` decimal(15,3) NOT NULL DEFAULT '0.000',
  `counted_quantity` decimal(15,3) DEFAULT NULL,
  `difference_quantity` decimal(15,3) DEFAULT '0.000',
  `unit_cost` decimal(15,2) DEFAULT '0.00',
  `difference_value` decimal(15,2) DEFAULT '0.00',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `counted_by` int DEFAULT NULL,
  `counted_at` timestamp NULL DEFAULT NULL,
  `created_by` int NOT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`audit_item_id`),
  KEY `company_id` (`company_id`),
  KEY `audit_id` (`audit_id`),
  KEY `product_id` (`product_id`),
  KEY `counted_by` (`counted_by`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  KEY `module_code` (`module_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ===================================
-- العلاقات الخارجية (Foreign Keys)
-- ===================================

-- علاقات جدول الفئات
ALTER TABLE `inventory_categories`
  ADD CONSTRAINT `fk_inv_categories_company` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_categories_parent` FOREIGN KEY (`parent_category_id`) REFERENCES `inventory_categories` (`category_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_inv_categories_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_categories_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`UserID`);

-- علاقات جدول وحدات القياس
ALTER TABLE `inventory_units`
  ADD CONSTRAINT `fk_inv_units_company` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_units_base_unit` FOREIGN KEY (`base_unit_id`) REFERENCES `inventory_units` (`unit_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_inv_units_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_units_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`UserID`);

-- علاقات جدول المخازن
ALTER TABLE `inventory_warehouses`
  ADD CONSTRAINT `fk_inv_warehouses_company` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_warehouses_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_warehouses_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`UserID`);

-- علاقات جدول المنتجات
ALTER TABLE `inventory_products`
  ADD CONSTRAINT `fk_inv_products_company` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_products_category` FOREIGN KEY (`category_id`) REFERENCES `inventory_categories` (`category_id`),
  ADD CONSTRAINT `fk_inv_products_unit` FOREIGN KEY (`unit_id`) REFERENCES `inventory_units` (`unit_id`),
  ADD CONSTRAINT `fk_inv_products_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_products_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`UserID`);

-- علاقات جدول أرصدة المخزون
ALTER TABLE `inventory_stock`
  ADD CONSTRAINT `fk_inv_stock_company` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_stock_product` FOREIGN KEY (`product_id`) REFERENCES `inventory_products` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_stock_warehouse` FOREIGN KEY (`warehouse_id`) REFERENCES `inventory_warehouses` (`warehouse_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_stock_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_stock_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`UserID`);

-- علاقات جدول حركات المخزون
ALTER TABLE `inventory_movements`
  ADD CONSTRAINT `fk_inv_movements_company` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_movements_product` FOREIGN KEY (`product_id`) REFERENCES `inventory_products` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_movements_warehouse` FOREIGN KEY (`warehouse_id`) REFERENCES `inventory_warehouses` (`warehouse_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_movements_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_movements_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`UserID`);

-- علاقات جدول التحويلات
ALTER TABLE `inventory_transfers`
  ADD CONSTRAINT `fk_inv_transfers_company` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_transfers_from_warehouse` FOREIGN KEY (`from_warehouse_id`) REFERENCES `inventory_warehouses` (`warehouse_id`),
  ADD CONSTRAINT `fk_inv_transfers_to_warehouse` FOREIGN KEY (`to_warehouse_id`) REFERENCES `inventory_warehouses` (`warehouse_id`),
  ADD CONSTRAINT `fk_inv_transfers_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_transfers_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_transfers_approved_by` FOREIGN KEY (`approved_by`) REFERENCES `users` (`UserID`);

-- علاقات جدول تفاصيل التحويلات
ALTER TABLE `inventory_transfer_items`
  ADD CONSTRAINT `fk_inv_transfer_items_company` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_transfer_items_transfer` FOREIGN KEY (`transfer_id`) REFERENCES `inventory_transfers` (`transfer_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_transfer_items_product` FOREIGN KEY (`product_id`) REFERENCES `inventory_products` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_transfer_items_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_transfer_items_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`UserID`);

-- علاقات جدول تسعير المنتجات
ALTER TABLE `inventory_product_prices`
  ADD CONSTRAINT `fk_inv_prices_company` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_prices_product` FOREIGN KEY (`product_id`) REFERENCES `inventory_products` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_prices_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_prices_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`UserID`);

-- علاقات جدول الجرد
ALTER TABLE `inventory_audits`
  ADD CONSTRAINT `fk_inv_audits_company` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_audits_warehouse` FOREIGN KEY (`warehouse_id`) REFERENCES `inventory_warehouses` (`warehouse_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_audits_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_audits_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_audits_completed_by` FOREIGN KEY (`completed_by`) REFERENCES `users` (`UserID`);

-- علاقات جدول تفاصيل الجرد
ALTER TABLE `inventory_audit_items`
  ADD CONSTRAINT `fk_inv_audit_items_company` FOREIGN KEY (`company_id`) REFERENCES `companies` (`CompanyID`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_audit_items_audit` FOREIGN KEY (`audit_id`) REFERENCES `inventory_audits` (`audit_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_audit_items_product` FOREIGN KEY (`product_id`) REFERENCES `inventory_products` (`product_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_inv_audit_items_counted_by` FOREIGN KEY (`counted_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_audit_items_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`UserID`),
  ADD CONSTRAINT `fk_inv_audit_items_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`UserID`);

-- ===================================
-- بيانات أساسية للاختبار
-- ===================================

-- ملاحظة: يجب تشغيل هذه البيانات بعد التأكد من وجود شركة ومستخدم في النظام
-- أو استبدال القيم بالقيم الصحيحة من قاعدة البيانات

-- للحصول على أول شركة ومستخدم متاحين:
-- SELECT CompanyID FROM companies LIMIT 1;
-- SELECT UserID FROM users LIMIT 1;

-- إدراج وحدات قياس أساسية (استبدل @company_id و @user_id بالقيم الصحيحة)
-- SET @company_id = (SELECT CompanyID FROM companies LIMIT 1);
-- SET @user_id = (SELECT UserID FROM users LIMIT 1);

-- إدراج وحدات قياس أساسية
-- INSERT INTO `inventory_units` (`company_id`, `unit_code`, `unit_name_ar`, `unit_name_en`, `unit_symbol_ar`, `unit_symbol_en`, `is_base_unit`, `created_by`) VALUES
-- (@company_id, 'PIECE', 'قطعة', 'Piece', 'قطعة', 'pcs', 1, @user_id),
-- (@company_id, 'KG', 'كيلوجرام', 'Kilogram', 'كجم', 'kg', 1, @user_id),
-- (@company_id, 'LITER', 'لتر', 'Liter', 'لتر', 'L', 1, @user_id),
-- (@company_id, 'METER', 'متر', 'Meter', 'م', 'm', 1, @user_id),
-- (@company_id, 'BOX', 'صندوق', 'Box', 'صندوق', 'box', 0, @user_id),
-- (@company_id, 'CARTON', 'كرتون', 'Carton', 'كرتون', 'carton', 0, @user_id);

-- إدراج فئات أساسية
-- INSERT INTO `inventory_categories` (`company_id`, `category_code`, `category_name_ar`, `category_name_en`, `display_order`, `created_by`) VALUES
-- (@company_id, 'ELECTRONICS', 'إلكترونيات', 'Electronics', 1, @user_id),
-- (@company_id, 'FURNITURE', 'أثاث', 'Furniture', 2, @user_id),
-- (@company_id, 'OFFICE_SUPPLIES', 'مستلزمات مكتبية', 'Office Supplies', 3, @user_id),
-- (@company_id, 'FOOD_BEVERAGE', 'أطعمة ومشروبات', 'Food & Beverage', 4, @user_id);

-- إدراج مخزن أساسي
-- INSERT INTO `inventory_warehouses` (`company_id`, `warehouse_code`, `warehouse_name_ar`, `warehouse_name_en`, `address`, `warehouse_type`, `created_by`) VALUES
-- (@company_id, 'MAIN_WH', 'المخزن الرئيسي', 'Main Warehouse', 'الرياض، المملكة العربية السعودية', 'main', @user_id);