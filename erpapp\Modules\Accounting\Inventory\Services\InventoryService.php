<?php
namespace Modules\Accounting\Inventory\Services;

use Modules\Accounting\Inventory\Models\Product;
use Modules\Accounting\Inventory\Models\Category;
use Modules\Accounting\Inventory\Models\Unit;
use Modules\Accounting\Inventory\Models\Warehouse;
use Modules\Accounting\Inventory\Models\Stock;

/**
 * Inventory Service
 * خدمة إدارة المخزون
 */
class InventoryService {
    
    private $productModel;
    private $categoryModel;
    private $unitModel;
    private $warehouseModel;
    private $stockModel;

    public function __construct() {
        $this->productModel = new Product();
        $this->categoryModel = new Category();
        $this->unitModel = new Unit();
        $this->warehouseModel = new Warehouse();
        $this->stockModel = new Stock();
    }

    /**
     * إنشاء منتج جديد مع رصيد أولي
     *
     * @param array $product_data بيانات المنتج
     * @param array $initial_stock الرصيد الأولي
     * @return array النتيجة
     */
    public function createProductWithStock($product_data, $initial_stock = []) {
        try {
            // إنشاء المنتج
            $product_id = $this->productModel->create($product_data);
            
            if (!$product_id) {
                return ['success' => false, 'message' => 'فشل في إنشاء المنتج'];
            }

            // إضافة الرصيد الأولي إذا تم تحديده
            if (!empty($initial_stock) && $product_data['track_inventory']) {
                foreach ($initial_stock as $stock_item) {
                    $stock_data = [
                        'company_id' => $product_data['company_id'],
                        'product_id' => $product_id,
                        'warehouse_id' => $stock_item['warehouse_id'],
                        'quantity_on_hand' => $stock_item['quantity'],
                        'average_cost' => $stock_item['cost'] ?? $product_data['cost_price'],
                        'last_cost' => $stock_item['cost'] ?? $product_data['cost_price'],
                        'last_movement_date' => date('Y-m-d H:i:s'),
                        'created_by' => $product_data['created_by']
                    ];

                    $this->stockModel->createOrUpdate($stock_data);
                }
            }

            return [
                'success' => true, 
                'message' => 'تم إنشاء المنتج بنجاح',
                'product_id' => $product_id
            ];

        } catch (Exception $e) {
            error_log("Error in createProductWithStock: " . $e->getMessage());
            return ['success' => false, 'message' => 'حدث خطأ أثناء إنشاء المنتج'];
        }
    }

    /**
     * نقل المخزون بين المخازن
     *
     * @param int $product_id معرف المنتج
     * @param int $from_warehouse_id المخزن المصدر
     * @param int $to_warehouse_id المخزن الهدف
     * @param float $quantity الكمية
     * @param int $company_id معرف الشركة
     * @param int $user_id معرف المستخدم
     * @return array النتيجة
     */
    public function transferStock($product_id, $from_warehouse_id, $to_warehouse_id, $quantity, $company_id, $user_id) {
        try {
            // التحقق من توفر الكمية في المخزن المصدر
            $source_stock = $this->stockModel->getStock($product_id, $from_warehouse_id, $company_id);
            
            if (!$source_stock || $source_stock['quantity_available'] < $quantity) {
                return ['success' => false, 'message' => 'الكمية المطلوبة غير متوفرة في المخزن المصدر'];
            }

            // خصم من المخزن المصدر
            $deduct_success = $this->stockModel->adjustQuantity(
                $product_id, 
                $from_warehouse_id, 
                $company_id, 
                -$quantity, 
                $source_stock['average_cost'], 
                $user_id
            );

            if (!$deduct_success) {
                return ['success' => false, 'message' => 'فشل في خصم الكمية من المخزن المصدر'];
            }

            // إضافة إلى المخزن الهدف
            $add_success = $this->stockModel->adjustQuantity(
                $product_id, 
                $to_warehouse_id, 
                $company_id, 
                $quantity, 
                $source_stock['average_cost'], 
                $user_id
            );

            if (!$add_success) {
                // إعادة الكمية إلى المخزن المصدر في حالة الفشل
                $this->stockModel->adjustQuantity(
                    $product_id, 
                    $from_warehouse_id, 
                    $company_id, 
                    $quantity, 
                    $source_stock['average_cost'], 
                    $user_id
                );
                
                return ['success' => false, 'message' => 'فشل في إضافة الكمية إلى المخزن الهدف'];
            }

            return ['success' => true, 'message' => 'تم نقل المخزون بنجاح'];

        } catch (Exception $e) {
            error_log("Error in transferStock: " . $e->getMessage());
            return ['success' => false, 'message' => 'حدث خطأ أثناء نقل المخزون'];
        }
    }

    /**
     * حساب قيمة المخزون الإجمالية
     *
     * @param int $company_id معرف الشركة
     * @param array $filters فلاتر إضافية
     * @return array النتيجة
     */
    public function calculateInventoryValue($company_id, $filters = []) {
        try {
            $stocks = $this->stockModel->getAll($company_id, $filters);
            
            $total_value = 0;
            $total_quantity = 0;
            $categories_value = [];
            $warehouses_value = [];

            foreach ($stocks as $stock) {
                $item_value = $stock['quantity_on_hand'] * $stock['average_cost'];
                $total_value += $item_value;
                $total_quantity += $stock['quantity_on_hand'];

                // تجميع حسب الفئة
                $category = $stock['category_name_ar'] ?? 'غير محدد';
                if (!isset($categories_value[$category])) {
                    $categories_value[$category] = 0;
                }
                $categories_value[$category] += $item_value;

                // تجميع حسب المخزن
                $warehouse = $stock['warehouse_name_ar'] ?? 'غير محدد';
                if (!isset($warehouses_value[$warehouse])) {
                    $warehouses_value[$warehouse] = 0;
                }
                $warehouses_value[$warehouse] += $item_value;
            }

            return [
                'success' => true,
                'total_value' => $total_value,
                'total_quantity' => $total_quantity,
                'categories_value' => $categories_value,
                'warehouses_value' => $warehouses_value,
                'items_count' => count($stocks)
            ];

        } catch (Exception $e) {
            error_log("Error in calculateInventoryValue: " . $e->getMessage());
            return ['success' => false, 'message' => 'حدث خطأ أثناء حساب قيمة المخزون'];
        }
    }

    /**
     * تحديث أسعار المنتجات بالجملة
     *
     * @param array $price_updates قائمة التحديثات
     * @param int $company_id معرف الشركة
     * @param int $user_id معرف المستخدم
     * @return array النتيجة
     */
    public function bulkUpdatePrices($price_updates, $company_id, $user_id) {
        try {
            $updated_count = 0;
            $errors = [];

            foreach ($price_updates as $update) {
                $product_id = $update['product_id'];
                $new_cost_price = $update['cost_price'] ?? null;
                $new_selling_price = $update['selling_price'] ?? null;

                if ($new_cost_price === null && $new_selling_price === null) {
                    continue;
                }

                $product_data = [];
                if ($new_cost_price !== null) {
                    $product_data['cost_price'] = $new_cost_price;
                }
                if ($new_selling_price !== null) {
                    $product_data['selling_price'] = $new_selling_price;
                }
                $product_data['updated_by'] = $user_id;

                $success = $this->productModel->update($product_id, $product_data, $company_id);
                
                if ($success) {
                    $updated_count++;
                } else {
                    $errors[] = "فشل في تحديث المنتج رقم: $product_id";
                }
            }

            return [
                'success' => true,
                'updated_count' => $updated_count,
                'errors' => $errors,
                'message' => "تم تحديث $updated_count منتج بنجاح"
            ];

        } catch (Exception $e) {
            error_log("Error in bulkUpdatePrices: " . $e->getMessage());
            return ['success' => false, 'message' => 'حدث خطأ أثناء تحديث الأسعار'];
        }
    }

    /**
     * تحليل المخزون وإنتاج تقرير شامل
     *
     * @param int $company_id معرف الشركة
     * @return array التقرير
     */
    public function generateInventoryAnalysis($company_id) {
        try {
            // إحصائيات عامة
            $products = $this->productModel->getAll($company_id);
            $categories = $this->categoryModel->getAll($company_id);
            $warehouses = $this->warehouseModel->getAll($company_id);
            $stocks = $this->stockModel->getAll($company_id);

            // تحليل الأرصدة
            $out_of_stock = $this->stockModel->getAll($company_id, ['out_of_stock' => true]);
            $need_reorder = $this->productModel->getProductsNeedReorder($company_id);

            // حساب القيم
            $inventory_value = $this->calculateInventoryValue($company_id);

            // تحليل الحركة (سيتم إضافته لاحقاً مع نموذج الحركات)
            $movement_analysis = [
                'total_movements_this_month' => 0,
                'total_in_movements' => 0,
                'total_out_movements' => 0
            ];

            return [
                'success' => true,
                'summary' => [
                    'total_products' => count($products),
                    'active_products' => count(array_filter($products, function($p) { return $p['is_active']; })),
                    'total_categories' => count($categories),
                    'total_warehouses' => count($warehouses),
                    'total_stock_items' => count($stocks)
                ],
                'alerts' => [
                    'out_of_stock_count' => count($out_of_stock),
                    'need_reorder_count' => count($need_reorder),
                    'out_of_stock_products' => array_slice($out_of_stock, 0, 10),
                    'need_reorder_products' => array_slice($need_reorder, 0, 10)
                ],
                'valuation' => $inventory_value,
                'movement_analysis' => $movement_analysis,
                'generated_at' => date('Y-m-d H:i:s')
            ];

        } catch (Exception $e) {
            error_log("Error in generateInventoryAnalysis: " . $e->getMessage());
            return ['success' => false, 'message' => 'حدث خطأ أثناء تحليل المخزون'];
        }
    }

    /**
     * التحقق من صحة بيانات المنتج
     *
     * @param array $data بيانات المنتج
     * @param int $company_id معرف الشركة
     * @param int|null $exclude_id معرف المنتج المستثنى (للتحديث)
     * @return array النتيجة
     */
    public function validateProductData($data, $company_id, $exclude_id = null) {
        $errors = [];

        // التحقق من الحقول المطلوبة
        if (empty($data['product_code'])) {
            $errors[] = 'كود المنتج مطلوب';
        }

        if (empty($data['product_name_ar'])) {
            $errors[] = 'اسم المنتج بالعربية مطلوب';
        }

        if (empty($data['category_id'])) {
            $errors[] = 'فئة المنتج مطلوبة';
        }

        if (empty($data['unit_id'])) {
            $errors[] = 'وحدة القياس مطلوبة';
        }

        // التحقق من عدم تكرار الكود
        if (!empty($data['product_code'])) {
            if ($this->productModel->isProductCodeExists($data['product_code'], $company_id, $exclude_id)) {
                $errors[] = 'كود المنتج موجود مسبقاً';
            }
        }

        // التحقق من عدم تكرار الباركود
        if (!empty($data['barcode'])) {
            if ($this->productModel->isBarcodeExists($data['barcode'], $company_id, $exclude_id)) {
                $errors[] = 'الباركود موجود مسبقاً';
            }
        }

        // التحقق من صحة الأرقام
        $numeric_fields = ['cost_price', 'selling_price', 'min_stock_level', 'max_stock_level', 'reorder_point', 'weight', 'tax_rate'];
        foreach ($numeric_fields as $field) {
            if (!empty($data[$field]) && !is_numeric($data[$field])) {
                $errors[] = "الحقل $field يجب أن يكون رقماً";
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
}
