<?php
namespace App\Core;

/**
 * نظام العرض
 */
class View
{
    /**
     * عرض قالب
     *
     * @param string $view مسار القالب
     * @param array $data البيانات المراد تمريرها للقالب
     * @return void
     */
    public static function render($view, $data = [])
    {
        // استخراج البيانات
        extract($data);

        // تحديد مسار القالب
        $viewPath = self::findViewPath($view);

        if (!$viewPath) {
            throw new \Exception("View not found: {$view}");
        }

        // تحميل القالب
        ob_start();
        include $viewPath;
        $content = ob_get_clean();

        // تحميل القالب الرئيسي إذا لم يكن هناك طلب AJAX
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || $_SERVER['HTTP_X_REQUESTED_WITH'] !== 'XMLHttpRequest') {
            // البحث عن القالب الرئيسي في المسارات المختلفة
            $layoutPath = self::findLayoutPath();

            if ($layoutPath) {
                include $layoutPath;
            } else {
                echo $content;
            }
        } else {
            echo $content;
        }
    }

    /**
     * البحث عن مسار القالب
     *
     * @param string $view مسار القالب
     * @return string|false
     */
    private static function findViewPath($view)
    {
        // التعامل مع الصيغة Module::view
        if (strpos($view, '::') !== false) {
            $parts = explode('::', $view);
            $module = $parts[0];
            $viewName = $parts[1];

            // البحث في وحدات النظام الأساسي أولاً
            $systemPath = BASE_PATH . '/App/System/' . $module . '/Views/' . $viewName . '.php';
            if (file_exists($systemPath)) {
                return $systemPath;
            }

            // البحث في وحدات App/Modules
            $appModulePath = BASE_PATH . '/App/Modules/' . $module . '/Views/' . $viewName . '.php';
            if (file_exists($appModulePath)) {
                return $appModulePath;
            }

            // البحث في وحدات الأعمال (المسار القديم)
            $modulePath = BASE_PATH . '/Modules/' . $module . '/Views/' . $viewName . '.php';
            if (file_exists($modulePath)) {
                return $modulePath;
            }

            // البحث في الوحدات الفرعية (مثل Accounting.Sales::view)
            if (strpos($module, '.') !== false) {
                $moduleParts = explode('.', $module);
                $mainModule = $moduleParts[0];
                $subModule = $moduleParts[1];

                $subModulePath = BASE_PATH . '/Modules/' . $mainModule . '/' . $subModule . '/Views/' . $viewName . '.php';
                if (file_exists($subModulePath)) {
                    return $subModulePath;
                }
            }
        }

        // التعامل مع الصيغة Module/view
        $parts = explode('/', $view);
        if (count($parts) >= 2) {
            $module = $parts[0];
            $viewName = $parts[1];

            // البحث في وحدات النظام الأساسي أولاً
            $systemPath = BASE_PATH . '/App/System/' . $module . '/Views/' . $viewName . '.php';
            if (file_exists($systemPath)) {
                return $systemPath;
            }

            // البحث في وحدات App/Modules
            $appModulePath = BASE_PATH . '/App/Modules/' . $module . '/Views/' . $viewName . '.php';
            if (file_exists($appModulePath)) {
                return $appModulePath;
            }

            // البحث في وحدات الأعمال (المسار القديم)
            $modulePath = BASE_PATH . '/Modules/' . $module . '/Views/' . $viewName . '.php';
            if (file_exists($modulePath)) {
                return $modulePath;
            }
        }

        // البحث في مجلد Notifications
        if (strpos($view, 'Notifications::') === 0) {
            $notificationView = str_replace('Notifications::', '', $view);
            $notificationPath = BASE_PATH . '/App/Notifications/' . $notificationView . '.php';
            if (file_exists($notificationPath)) {
                return $notificationPath;
            }
        }

        // البحث في مسار العروض القديم
        $oldViewPath = BASE_PATH . '/App/Views/' . $view . '.php';
        if (file_exists($oldViewPath)) {
            return $oldViewPath;
        }

        // البحث في مسار الموارد
        $resourceViewPath = BASE_PATH . '/resources/views/' . $view . '.php';
        if (file_exists($resourceViewPath)) {
            return $resourceViewPath;
        }

        return false;
    }

    /**
     * البحث عن مسار القالب الرئيسي
     *
     * @return string|false
     */
    private static function findLayoutPath()
    {
        // مسار Layout الأساسي
        $layoutsPath = BASE_PATH . '/App/Layouts/main.php';

        return file_exists($layoutsPath) ? $layoutsPath : false;
    }
}
