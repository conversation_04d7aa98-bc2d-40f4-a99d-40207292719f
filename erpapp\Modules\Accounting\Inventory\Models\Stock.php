<?php
namespace Modules\Accounting\Inventory\Models;

/**
 * Stock Model
 * نموذج أرصدة المخزون
 */
class Stock {
    /**
     * Database connection
     *
     * @var \PDO
     */
    protected $db;

    /**
     * Constructor
     */
    public function __construct() {
        global $db;
        $this->db = $db;
    }

    /**
     * الحصول على أرصدة المخزون للشركة
     *
     * @param int $company_id معرف الشركة
     * @param array $filters فلاتر البحث
     * @return array
     */
    public function getAll($company_id, $filters = []) {
        $where_conditions = ['s.company_id = :company_id'];
        $params = [':company_id' => $company_id];

        // فلتر حسب المخزن
        if (!empty($filters['warehouse_id'])) {
            $where_conditions[] = 's.warehouse_id = :warehouse_id';
            $params[':warehouse_id'] = $filters['warehouse_id'];
        }

        // فلتر حسب الفئة
        if (!empty($filters['category_id'])) {
            $where_conditions[] = 'p.category_id = :category_id';
            $params[':category_id'] = $filters['category_id'];
        }

        // فلتر المنتجات التي تحتاج إعادة طلب
        if (isset($filters['need_reorder']) && $filters['need_reorder']) {
            $where_conditions[] = 's.quantity_available <= p.reorder_point';
        }

        // فلتر المنتجات النافدة
        if (isset($filters['out_of_stock']) && $filters['out_of_stock']) {
            $where_conditions[] = 's.quantity_on_hand <= 0';
        }

        // فلتر البحث النصي
        if (!empty($filters['search'])) {
            $where_conditions[] = '(p.product_name_ar LIKE :search OR p.product_name_en LIKE :search OR p.product_code LIKE :search)';
            $params[':search'] = '%' . $filters['search'] . '%';
        }

        $where_clause = implode(' AND ', $where_conditions);

        $sql = "
            SELECT s.*, 
                   p.product_code, p.product_name_ar, p.product_name_en, p.reorder_point, p.min_stock_level,
                   c.category_name_ar, c.category_name_en,
                   u.unit_name_ar, u.unit_symbol_ar,
                   w.warehouse_name_ar, w.warehouse_name_en,
                   (s.quantity_on_hand * s.average_cost) as total_value,
                   CASE 
                       WHEN s.quantity_on_hand <= 0 THEN 'نافد'
                       WHEN s.quantity_available <= p.reorder_point THEN 'يحتاج إعادة طلب'
                       WHEN s.quantity_available <= p.min_stock_level THEN 'مخزون منخفض'
                       ELSE 'متوفر'
                   END as stock_status
            FROM inventory_stock s
            LEFT JOIN inventory_products p ON s.product_id = p.product_id
            LEFT JOIN inventory_categories c ON p.category_id = c.category_id
            LEFT JOIN inventory_units u ON p.unit_id = u.unit_id
            LEFT JOIN inventory_warehouses w ON s.warehouse_id = w.warehouse_id
            WHERE {$where_clause}
            ORDER BY p.product_name_ar ASC
        ";

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على رصيد منتج في مخزن معين
     *
     * @param int $product_id معرف المنتج
     * @param int $warehouse_id معرف المخزن
     * @param int $company_id معرف الشركة
     * @return array|false
     */
    public function getStock($product_id, $warehouse_id, $company_id) {
        $sql = "
            SELECT s.*, 
                   p.product_code, p.product_name_ar, p.product_name_en,
                   w.warehouse_name_ar, w.warehouse_name_en,
                   u.unit_name_ar, u.unit_symbol_ar
            FROM inventory_stock s
            LEFT JOIN inventory_products p ON s.product_id = p.product_id
            LEFT JOIN inventory_warehouses w ON s.warehouse_id = w.warehouse_id
            LEFT JOIN inventory_units u ON p.unit_id = u.unit_id
            WHERE s.product_id = :product_id 
            AND s.warehouse_id = :warehouse_id 
            AND s.company_id = :company_id
        ";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':product_id', $product_id);
        $stmt->bindParam(':warehouse_id', $warehouse_id);
        $stmt->bindParam(':company_id', $company_id);
        $stmt->execute();
        return $stmt->fetch(\PDO::FETCH_ASSOC);
    }

    /**
     * إنشاء أو تحديث رصيد المخزون
     *
     * @param array $data بيانات الرصيد
     * @return bool
     */
    public function createOrUpdate($data) {
        // التحقق من وجود الرصيد
        $existing = $this->getStock($data['product_id'], $data['warehouse_id'], $data['company_id']);

        if ($existing) {
            return $this->updateStock($data['product_id'], $data['warehouse_id'], $data['company_id'], $data);
        } else {
            return $this->createStock($data);
        }
    }

    /**
     * إنشاء رصيد جديد
     *
     * @param array $data بيانات الرصيد
     * @return bool
     */
    private function createStock($data) {
        $sql = "
            INSERT INTO inventory_stock (
                company_id, module_code, product_id, warehouse_id, quantity_on_hand,
                quantity_reserved, quantity_available, average_cost, last_cost,
                last_movement_date, created_by
            ) VALUES (
                :company_id, :module_code, :product_id, :warehouse_id, :quantity_on_hand,
                :quantity_reserved, :quantity_available, :average_cost, :last_cost,
                :last_movement_date, :created_by
            )
        ";

        $stmt = $this->db->prepare($sql);
        
        // تعيين القيم الافتراضية
        $data['module_code'] = $data['module_code'] ?? 'inventory';
        $data['quantity_reserved'] = $data['quantity_reserved'] ?? 0.000;
        $data['quantity_available'] = $data['quantity_on_hand'] - $data['quantity_reserved'];
        $data['average_cost'] = $data['average_cost'] ?? 0.00;
        $data['last_cost'] = $data['last_cost'] ?? 0.00;

        $stmt->bindParam(':company_id', $data['company_id']);
        $stmt->bindParam(':module_code', $data['module_code']);
        $stmt->bindParam(':product_id', $data['product_id']);
        $stmt->bindParam(':warehouse_id', $data['warehouse_id']);
        $stmt->bindParam(':quantity_on_hand', $data['quantity_on_hand']);
        $stmt->bindParam(':quantity_reserved', $data['quantity_reserved']);
        $stmt->bindParam(':quantity_available', $data['quantity_available']);
        $stmt->bindParam(':average_cost', $data['average_cost']);
        $stmt->bindParam(':last_cost', $data['last_cost']);
        $stmt->bindParam(':last_movement_date', $data['last_movement_date']);
        $stmt->bindParam(':created_by', $data['created_by']);

        return $stmt->execute();
    }

    /**
     * تحديث رصيد موجود
     *
     * @param int $product_id معرف المنتج
     * @param int $warehouse_id معرف المخزن
     * @param int $company_id معرف الشركة
     * @param array $data بيانات الرصيد
     * @return bool
     */
    private function updateStock($product_id, $warehouse_id, $company_id, $data) {
        $sql = "
            UPDATE inventory_stock SET
                quantity_on_hand = :quantity_on_hand,
                quantity_reserved = :quantity_reserved,
                quantity_available = :quantity_available,
                average_cost = :average_cost,
                last_cost = :last_cost,
                last_movement_date = :last_movement_date,
                updated_by = :updated_by,
                updated_at = NOW()
            WHERE product_id = :product_id 
            AND warehouse_id = :warehouse_id 
            AND company_id = :company_id
        ";

        $stmt = $this->db->prepare($sql);
        
        // حساب الكمية المتاحة
        $data['quantity_available'] = $data['quantity_on_hand'] - ($data['quantity_reserved'] ?? 0);

        $stmt->bindParam(':product_id', $product_id);
        $stmt->bindParam(':warehouse_id', $warehouse_id);
        $stmt->bindParam(':company_id', $company_id);
        $stmt->bindParam(':quantity_on_hand', $data['quantity_on_hand']);
        $stmt->bindParam(':quantity_reserved', $data['quantity_reserved']);
        $stmt->bindParam(':quantity_available', $data['quantity_available']);
        $stmt->bindParam(':average_cost', $data['average_cost']);
        $stmt->bindParam(':last_cost', $data['last_cost']);
        $stmt->bindParam(':last_movement_date', $data['last_movement_date']);
        $stmt->bindParam(':updated_by', $data['updated_by']);

        return $stmt->execute();
    }

    /**
     * تحديث الكمية (إضافة أو خصم)
     *
     * @param int $product_id معرف المنتج
     * @param int $warehouse_id معرف المخزن
     * @param int $company_id معرف الشركة
     * @param float $quantity_change التغيير في الكمية (موجب للإضافة، سالب للخصم)
     * @param float $cost التكلفة
     * @param int $user_id معرف المستخدم
     * @return bool
     */
    public function adjustQuantity($product_id, $warehouse_id, $company_id, $quantity_change, $cost = 0, $user_id = null) {
        $current_stock = $this->getStock($product_id, $warehouse_id, $company_id);

        if (!$current_stock) {
            // إنشاء رصيد جديد إذا لم يكن موجوداً
            if ($quantity_change > 0) {
                return $this->createStock([
                    'company_id' => $company_id,
                    'product_id' => $product_id,
                    'warehouse_id' => $warehouse_id,
                    'quantity_on_hand' => $quantity_change,
                    'average_cost' => $cost,
                    'last_cost' => $cost,
                    'last_movement_date' => date('Y-m-d H:i:s'),
                    'created_by' => $user_id
                ]);
            }
            return false; // لا يمكن خصم من رصيد غير موجود
        }

        $new_quantity = $current_stock['quantity_on_hand'] + $quantity_change;

        if ($new_quantity < 0) {
            return false; // لا يمكن أن يكون الرصيد سالباً
        }

        // حساب متوسط التكلفة الجديد (للإضافات فقط)
        $new_average_cost = $current_stock['average_cost'];
        if ($quantity_change > 0 && $cost > 0) {
            $total_value = ($current_stock['quantity_on_hand'] * $current_stock['average_cost']) + ($quantity_change * $cost);
            $new_average_cost = $total_value / $new_quantity;
        }

        return $this->updateStock($product_id, $warehouse_id, $company_id, [
            'quantity_on_hand' => $new_quantity,
            'quantity_reserved' => $current_stock['quantity_reserved'],
            'average_cost' => $new_average_cost,
            'last_cost' => $cost > 0 ? $cost : $current_stock['last_cost'],
            'last_movement_date' => date('Y-m-d H:i:s'),
            'updated_by' => $user_id
        ]);
    }

    /**
     * حجز كمية من المخزون
     *
     * @param int $product_id معرف المنتج
     * @param int $warehouse_id معرف المخزن
     * @param int $company_id معرف الشركة
     * @param float $quantity الكمية المراد حجزها
     * @param int $user_id معرف المستخدم
     * @return bool
     */
    public function reserveQuantity($product_id, $warehouse_id, $company_id, $quantity, $user_id = null) {
        $current_stock = $this->getStock($product_id, $warehouse_id, $company_id);

        if (!$current_stock || $current_stock['quantity_available'] < $quantity) {
            return false; // لا توجد كمية كافية متاحة
        }

        $new_reserved = $current_stock['quantity_reserved'] + $quantity;

        return $this->updateStock($product_id, $warehouse_id, $company_id, [
            'quantity_on_hand' => $current_stock['quantity_on_hand'],
            'quantity_reserved' => $new_reserved,
            'average_cost' => $current_stock['average_cost'],
            'last_cost' => $current_stock['last_cost'],
            'last_movement_date' => $current_stock['last_movement_date'],
            'updated_by' => $user_id
        ]);
    }

    /**
     * إلغاء حجز كمية من المخزون
     *
     * @param int $product_id معرف المنتج
     * @param int $warehouse_id معرف المخزن
     * @param int $company_id معرف الشركة
     * @param float $quantity الكمية المراد إلغاء حجزها
     * @param int $user_id معرف المستخدم
     * @return bool
     */
    public function unreserveQuantity($product_id, $warehouse_id, $company_id, $quantity, $user_id = null) {
        $current_stock = $this->getStock($product_id, $warehouse_id, $company_id);

        if (!$current_stock || $current_stock['quantity_reserved'] < $quantity) {
            return false; // لا توجد كمية محجوزة كافية
        }

        $new_reserved = $current_stock['quantity_reserved'] - $quantity;

        return $this->updateStock($product_id, $warehouse_id, $company_id, [
            'quantity_on_hand' => $current_stock['quantity_on_hand'],
            'quantity_reserved' => $new_reserved,
            'average_cost' => $current_stock['average_cost'],
            'last_cost' => $current_stock['last_cost'],
            'last_movement_date' => $current_stock['last_movement_date'],
            'updated_by' => $user_id
        ]);
    }

    /**
     * الحصول على إجمالي رصيد منتج في جميع المخازن
     *
     * @param int $product_id معرف المنتج
     * @param int $company_id معرف الشركة
     * @return array
     */
    public function getTotalStock($product_id, $company_id) {
        $sql = "
            SELECT 
                COALESCE(SUM(quantity_on_hand), 0) as total_on_hand,
                COALESCE(SUM(quantity_reserved), 0) as total_reserved,
                COALESCE(SUM(quantity_available), 0) as total_available,
                COALESCE(AVG(average_cost), 0) as weighted_average_cost,
                COALESCE(SUM(quantity_on_hand * average_cost), 0) as total_value
            FROM inventory_stock 
            WHERE product_id = :product_id AND company_id = :company_id
        ";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':product_id', $product_id);
        $stmt->bindParam(':company_id', $company_id);
        $stmt->execute();
        return $stmt->fetch(\PDO::FETCH_ASSOC);
    }
}
