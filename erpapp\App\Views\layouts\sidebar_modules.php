<?php
/**
 * سايدبار الوحدات والبرامج
 * يظهر في صفحات الوحدات (inventory, sales, accounting, etc.)
 */

// جلب المستخدم الحالي
$user = current_user();
if (!$user) return;

$companyId = $user['current_company_id'];
if (!$companyId) return;

// جلب الوحدات والبرامج
$modules = getCompanyModulesWithPrograms($companyId);
$currentModule = getCurrentModule();
?>

<div class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <div class="d-flex align-items-center">
            <i class="fas fa-cubes text-primary me-2"></i>
            <h5 class="mb-0">الوحدات والبرامج</h5>
        </div>
        <button type="button" class="btn-close d-lg-none" data-bs-dismiss="offcanvas"></button>
    </div>

    <div class="sidebar-body">
        <?php if (empty($modules)): ?>
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            لا توجد وحدات منزلة لهذه الشركة
        </div>
        <?php else: ?>

        <div class="modules-list">
            <?php foreach ($modules as $module): ?>
            <div class="module-section mb-3">
                <!-- عنوان الوحدة -->
                <div class="module-header">
                    <a href="<?= moduleUrl($module['base_url']) ?>"
                       class="module-link <?= $currentModule === $module['module_code'] ? 'active' : '' ?>">
                        <i class="<?= htmlspecialchars($module['icon_name']) ?> me-2"></i>
                        <span><?= htmlspecialchars($module['module_name_ar']) ?></span>
                        <?php if ($module['module_type'] === 'premium'): ?>
                        <span class="badge bg-warning ms-2">متقدم</span>
                        <?php elseif ($module['module_type'] === 'addon'): ?>
                        <span class="badge bg-info ms-2">إضافي</span>
                        <?php endif; ?>
                    </a>
                </div>

                <!-- البرامج الفرعية -->
                <?php if (!empty($module['programs'])): ?>
                <div class="programs-list <?= $currentModule === $module['module_code'] ? 'show' : '' ?>">
                    <?php foreach ($module['programs'] as $program): ?>

                    <?php if ($program['program_type'] === 'Main'): ?>
                    <!-- البرنامج الرئيسي -->
                    <div class="program-main">
                        <?php if (!empty($program['page_url'])): ?>
                        <a href="<?= moduleUrl($program['page_url']) ?>"
                           class="program-link main-program <?= isCurrentPath($program['page_url']) ? 'active' : '' ?>">
                            <i class="<?= htmlspecialchars($program['icon_name']) ?> me-2"></i>
                            <span><?= htmlspecialchars($program['program_name_ar']) ?></span>
                        </a>
                        <?php else: ?>
                        <div class="program-title">
                            <i class="<?= htmlspecialchars($program['icon_name']) ?> me-2"></i>
                            <span><?= htmlspecialchars($program['program_name_ar']) ?></span>
                        </div>
                        <?php endif; ?>

                        <!-- البرامج الفرعية تحت الرئيسي -->
                        <?php if (!empty($program['sub_programs'])): ?>
                        <div class="sub-programs">
                            <?php foreach ($program['sub_programs'] as $subProgram): ?>
                            <?php if (hasUserPermissionForProgram($subProgram['program_id'], 'view')): ?>
                            <a href="<?= moduleUrl($subProgram['page_url']) ?>"
                               class="program-link sub-program <?= isCurrentPath($subProgram['page_url']) ? 'active' : '' ?>">
                                <i class="<?= htmlspecialchars($subProgram['icon_name']) ?> me-2"></i>
                                <span><?= htmlspecialchars($subProgram['program_name_ar']) ?></span>
                            </a>
                            <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>

                    <?php else: ?>
                    <!-- برنامج فرعي مستقل (بدون رئيسي) -->
                    <?php if (hasUserPermissionForProgram($program['program_id'], 'view')): ?>
                    <a href="<?= moduleUrl($program['page_url']) ?>"
                       class="program-link sub-program <?= isCurrentPath($program['page_url']) ? 'active' : '' ?>">
                        <i class="<?= htmlspecialchars($program['icon_name']) ?> me-2"></i>
                        <span><?= htmlspecialchars($program['program_name_ar']) ?></span>
                    </a>
                    <?php endif; ?>
                    <?php endif; ?>

                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
            <?php endforeach; ?>
        </div>

        <?php endif; ?>
    </div>

    <!-- تذييل السايدبار -->
    <div class="sidebar-footer">
        <div class="company-info">
            <small class="text-muted">
                <i class="fas fa-building me-1"></i>
                <?= htmlspecialchars($user['company_name'] ?? 'شركة غير محددة') ?>
            </small>
        </div>
    </div>
</div>

<style>
/* تنسيق سايدبار الوحدات */
.sidebar {
    width: 280px;
    height: 100vh;
    background: #f8f9fa;
    border-right: 1px solid #dee2e6;
    overflow-y: auto;
}

.sidebar-header {
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
    background: white;
}

.sidebar-body {
    padding: 1rem;
}

.module-section {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background: white;
    overflow: hidden;
}

.module-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.module-link {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    text-decoration: none;
    color: #495057;
    font-weight: 600;
    transition: all 0.3s ease;
}

.module-link:hover {
    background: #e9ecef;
    color: #007bff;
    text-decoration: none;
}

.module-link.active {
    background: #007bff;
    color: white;
}

.programs-list {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.programs-list.show {
    max-height: 500px;
}

.program-main {
    border-bottom: 1px solid #f1f3f4;
}

.program-main:last-child {
    border-bottom: none;
}

.program-link {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    text-decoration: none;
    color: #6c757d;
    transition: all 0.2s ease;
}

.program-link:hover {
    background: #f8f9fa;
    color: #007bff;
    text-decoration: none;
}

.program-link.active {
    background: #e3f2fd;
    color: #1976d2;
    border-right: 3px solid #1976d2;
}

.main-program {
    font-weight: 500;
    padding-right: 15px;
}

.sub-program {
    padding-right: 35px;
    font-size: 0.9rem;
}

.sub-programs {
    background: #fafbfc;
}

.program-title {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    font-weight: 500;
    color: #495057;
}

.sidebar-footer {
    padding: 1rem;
    border-top: 1px solid #dee2e6;
    background: white;
    margin-top: auto;
}

/* تجاوب مع الشاشات الصغيرة */
@media (max-width: 991.98px) {
    .sidebar {
        position: fixed;
        top: 0;
        right: -280px;
        z-index: 1050;
        transition: right 0.3s ease;
    }

    .sidebar.show {
        right: 0;
    }
}
</style>

<script>
// تفعيل/إلغاء تفعيل قوائم البرامج
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل الوحدة الحالية
    const currentModule = '<?= $currentModule ?>';
    if (currentModule) {
        const moduleSection = document.querySelector(`[data-module="${currentModule}"]`);
        if (moduleSection) {
            const programsList = moduleSection.querySelector('.programs-list');
            if (programsList) {
                programsList.classList.add('show');
            }
        }
    }

    // إضافة تفاعل للنقر على عناوين الوحدات
    document.querySelectorAll('.module-link').forEach(link => {
        link.addEventListener('click', function(e) {
            const moduleSection = this.closest('.module-section');
            const programsList = moduleSection.querySelector('.programs-list');

            if (programsList) {
                programsList.classList.toggle('show');
            }
        });
    });
});
</script>
