<?php
namespace App\System\Dashboard\Controllers;

use App\System\Dashboard\Services\DashboardService;

/**
 * متحكم لوحة التحكم
 */
class DashboardController
{
    /**
     * معلمات المسار
     */
    protected $params = [];

    /**
     * خدمة لوحة التحكم
     */
    protected $dashboardService;

    /**
     * Constructor
     *
     * @param array $params معلمات المسار
     */
    public function __construct($params = [])
    {
        $this->params = $params;
        $this->dashboardService = new DashboardService();

        // التحقق من تسجيل الدخول
        if (!is_logged_in()) {
            redirect(base_url('login'));
        }
    }

    /**
     * عرض لوحة التحكم
     *
     * @return void
     */
    public function index()
    {
        // الحصول على بيانات المستخدم الحالي
        $user = current_user();

        // الحصول على إحصائيات لوحة التحكم
        $stats = $this->dashboardService->getDashboardStats($user);

        // الحصول على آخر الأنشطة
        $activities = $this->dashboardService->getRecentActivities(10);

        // الحصول على الشركات التي يملكها المستخدم
        $companies = $this->dashboardService->getUserCompanies($user, 5);

        // عرض لوحة التحكم
        view('Dashboard::index', [
            'title' => __('لوحة التحكم'),
            'user' => $user,
            'stats' => $stats,
            'activities' => $activities,
            'companies' => $companies,
            'notifications' => [],
            'tasks' => []
        ]);
    }
}
