<?php
namespace App\System\Dashboard\Controllers;

use App\System\Dashboard\Services\DashboardService;

/**
 * متحكم لوحة التحكم
 */
class DashboardController
{
    /**
     * معلمات المسار
     */
    protected $params = [];

    /**
     * خدمة لوحة التحكم
     */
    protected $dashboardService;

    /**
     * Constructor
     *
     * @param array $params معلمات المسار
     */
    public function __construct($params = [])
    {
        $this->params = $params;
        $this->dashboardService = new DashboardService();

        // التحقق من تسجيل الدخول
        if (!is_logged_in()) {
            redirect(base_url('login'));
        }
    }

    /**
     * عرض لوحة التحكم الذكية
     *
     * @return void
     */
    public function index()
    {
        // تحديد نوع Dashboard حسب السياق الحالي
        $pageType = $this->getCurrentPageType();

        if ($pageType === 'module') {
            // عرض Modules Dashboard
            $this->showModulesDashboard();
        } else {
            // عرض System Dashboard
            $this->showSystemDashboard();
        }
    }

    /**
     * عرض لوحة تحكم النظام
     *
     * @return void
     */
    public function showSystemDashboard()
    {
        // الحصول على بيانات المستخدم الحالي
        $user = current_user();

        // الحصول على إحصائيات لوحة التحكم
        $stats = $this->dashboardService->getDashboardStats($user);

        // الحصول على آخر الأنشطة
        $activities = $this->dashboardService->getRecentActivities(10);

        // الحصول على الشركات التي يملكها المستخدم
        $companies = $this->dashboardService->getUserCompanies($user, 5);

        // عرض لوحة تحكم النظام
        view('Dashboard::system_dashboard', [
            'title' => __('لوحة تحكم النظام'),
            'user' => $user,
            'stats' => $stats,
            'activities' => $activities,
            'companies' => $companies,
            'notifications' => [],
            'tasks' => [],
            'dashboard_type' => 'system'
        ]);
    }

    /**
     * عرض لوحة تحكم الوحدات
     *
     * @return void
     */
    public function showModulesDashboard()
    {
        // الحصول على بيانات المستخدم الحالي
        $user = current_user();
        $companyId = $user['current_company_id'];

        if (!$companyId) {
            redirect('companies');
            return;
        }

        // جلب بيانات لوحة تحكم الوحدات
        $dashboardData = $this->getModulesDashboardData($companyId);

        // عرض لوحة تحكم الوحدات
        view('Dashboard::modules_dashboard', [
            'title' => __('لوحة تحكم الوحدات'),
            'user' => $user,
            'company_id' => $companyId,
            'dashboard_data' => $dashboardData,
            'dashboard_type' => 'modules'
        ]);
    }

    /**
     * تحديد نوع الصفحة الحالية
     *
     * @return string
     */
    private function getCurrentPageType()
    {
        // استخدام نفس منطق السايدبار
        if (function_exists('getCurrentPageType')) {
            return getCurrentPageType();
        }

        // منطق احتياطي
        $currentPath = $_SERVER['REQUEST_URI'];
        $path = parse_url($currentPath, PHP_URL_PATH);
        $path = str_replace('/erpapp/', '', $path);
        $path = trim($path, '/');

        // فحص إذا كان المسار ينتمي لوحدة
        if (function_exists('isModulePath')) {
            $firstSegment = explode('/', $path)[0];
            if (isModulePath($firstSegment)) {
                return 'module';
            }
        }

        return 'system';
    }

    /**
     * جلب بيانات لوحة تحكم الوحدات
     *
     * @param int $companyId
     * @return array
     */
    private function getModulesDashboardData($companyId)
    {
        try {
            global $db;

            $data = [
                'modules_stats' => $this->getModulesStats($companyId),
                'recent_activities' => $this->getModulesRecentActivities($companyId),
                'system_info' => $this->getSystemInfo($companyId),
                'quick_actions' => $this->getQuickActions($companyId)
            ];

            return $data;

        } catch (Exception $e) {
            error_log("خطأ في جلب بيانات لوحة تحكم الوحدات: " . $e->getMessage());
            return [
                'modules_stats' => [],
                'recent_activities' => [],
                'system_info' => [],
                'quick_actions' => []
            ];
        }
    }

    /**
     * جلب إحصائيات الوحدات
     */
    private function getModulesStats($companyId)
    {
        try {
            global $db;

            // إحصائيات الوحدات المنزلة
            $stmt = $db->prepare("
                SELECT
                    COUNT(*) as total_modules,
                    SUM(CASE WHEN cm.is_active = 1 THEN 1 ELSE 0 END) as active_modules,
                    SUM(CASE WHEN cm.license_expires_at IS NOT NULL AND cm.license_expires_at < NOW() THEN 1 ELSE 0 END) as expired_modules
                FROM company_modules cm
                WHERE cm.company_id = ?
            ");
            $stmt->execute([$companyId]);
            $modulesStats = $stmt->fetch(PDO::FETCH_ASSOC);

            // إحصائيات البرامج
            $stmt = $db->prepare("
                SELECT COUNT(*) as total_programs
                FROM module_programs mp
                WHERE mp.company_id = ?
                AND mp.is_active = 1
            ");
            $stmt->execute([$companyId]);
            $programsStats = $stmt->fetch(PDO::FETCH_ASSOC);

            // إحصائيات المستخدمين
            $stmt = $db->prepare("
                SELECT COUNT(*) as total_users
                FROM company_users cu
                WHERE cu.company_id = ?
                AND cu.status = 'accepted'
                AND cu.user_status = 'active'
            ");
            $stmt->execute([$companyId]);
            $usersStats = $stmt->fetch(PDO::FETCH_ASSOC);

            return [
                'total_modules' => $modulesStats['total_modules'] ?? 0,
                'active_modules' => $modulesStats['active_modules'] ?? 0,
                'expired_modules' => $modulesStats['expired_modules'] ?? 0,
                'total_programs' => $programsStats['total_programs'] ?? 0,
                'total_users' => $usersStats['total_users'] ?? 0
            ];

        } catch (Exception $e) {
            error_log("خطأ في جلب إحصائيات الوحدات: " . $e->getMessage());
            return [];
        }
    }

    /**
     * جلب الأنشطة الحديثة للوحدات
     */
    private function getModulesRecentActivities($companyId)
    {
        try {
            global $db;

            $stmt = $db->prepare("
                SELECT
                    'module_installed' as activity_type,
                    sm.module_name_ar as title,
                    cm.installed_at as created_at,
                    u.FirstName as user_name
                FROM company_modules cm
                JOIN system_modules sm ON sm.module_id = cm.module_id
                JOIN users u ON u.UserID = cm.installed_by
                WHERE cm.company_id = ?
                ORDER BY cm.installed_at DESC
                LIMIT 10
            ");
            $stmt->execute([$companyId]);

            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (Exception $e) {
            error_log("خطأ في جلب الأنشطة الحديثة: " . $e->getMessage());
            return [];
        }
    }

    /**
     * جلب معلومات النظام
     */
    private function getSystemInfo($companyId)
    {
        try {
            global $db;

            $stmt = $db->prepare("
                SELECT
                    c.CompanyName,
                    c.CompanyStatus,
                    c.trial_end_date,
                    sp.plan_name_ar,
                    s.end_date as subscription_end
                FROM companies c
                LEFT JOIN subscriptions s ON s.subscription_id = c.subscription_id
                LEFT JOIN subscription_plans sp ON sp.plan_id = s.plan_id
                WHERE c.CompanyID = ?
            ");
            $stmt->execute([$companyId]);

            return $stmt->fetch(PDO::FETCH_ASSOC) ?: [];

        } catch (Exception $e) {
            error_log("خطأ في جلب معلومات النظام: " . $e->getMessage());
            return [];
        }
    }

    /**
     * جلب الإجراءات السريعة
     */
    private function getQuickActions($companyId)
    {
        return [
            [
                'title' => 'تنزيل وحدة جديدة',
                'description' => 'استعراض وتنزيل وحدات جديدة',
                'icon' => 'fas fa-download',
                'url' => base_url('modules/install'),
                'color' => 'primary'
            ],
            [
                'title' => 'إدارة الصلاحيات',
                'description' => 'تحديد صلاحيات المستخدمين',
                'icon' => 'fas fa-user-shield',
                'url' => base_url('permissions'),
                'color' => 'warning'
            ],
            [
                'title' => 'إعدادات الوحدات',
                'description' => 'تكوين إعدادات الوحدات',
                'icon' => 'fas fa-cogs',
                'url' => base_url('modules/settings'),
                'color' => 'info'
            ],
            [
                'title' => 'النسخ الاحتياطي',
                'description' => 'إنشاء نسخة احتياطية من البيانات',
                'icon' => 'fas fa-database',
                'url' => base_url('modules/backup'),
                'color' => 'success'
            ]
        ];
    }
}
