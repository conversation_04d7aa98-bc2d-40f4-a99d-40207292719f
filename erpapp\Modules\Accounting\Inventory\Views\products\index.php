<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title">
                    <i class="fas fa-cube text-primary"></i> <?= $title ?? 'إدارة المنتجات' ?>
                </h1>
                <div class="btn-group">
                    <a href="<?= base_url('inventory/products/create') ?>" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-2"></i> منتج جديد
                    </a>
                    <a href="<?= base_url('inventory/export/products') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-download me-2"></i> تصدير
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (isset($_SESSION['flash_message'])): ?>
        <div class="alert alert-<?= $_SESSION['flash_message']['type'] ?> alert-dismissible fade show" role="alert">
            <?= $_SESSION['flash_message']['message'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['flash_message']); ?>
    <?php endif; ?>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-filter me-2"></i> فلترة النتائج
                    </h6>
                </div>
                <div class="card-body">
                    <form method="GET" action="<?= base_url('inventory/products') ?>">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="search" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="<?= htmlspecialchars($filters['search'] ?? '') ?>" 
                                       placeholder="اسم المنتج، الكود، أو الباركود">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="category_id" class="form-label">الفئة</label>
                                <select class="form-select" id="category_id" name="category_id">
                                    <option value="">جميع الفئات</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?= $category['category_id'] ?>" 
                                                <?= ($filters['category_id'] ?? '') == $category['category_id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($category['category_name_ar']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="is_active" class="form-label">الحالة</label>
                                <select class="form-select" id="is_active" name="is_active">
                                    <option value="">جميع الحالات</option>
                                    <option value="1" <?= ($filters['is_active'] ?? '') === 1 ? 'selected' : '' ?>>نشط</option>
                                    <option value="0" <?= ($filters['is_active'] ?? '') === 0 ? 'selected' : '' ?>>غير نشط</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search me-1"></i> بحث
                                </button>
                                <a href="<?= base_url('inventory/products') ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i> إعادة تعيين
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي المنتجات</h6>
                            <h3><?= number_format($total_count) ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-cube fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">المنتجات النشطة</h6>
                            <h3><?= number_format(count(array_filter($products, function($p) { return $p['is_active']; }))) ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">يحتاج إعادة طلب</h6>
                            <h3><?= number_format(count(array_filter($products, function($p) { return ($p['available_stock'] ?? 0) <= ($p['reorder_point'] ?? 0); }))) ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">منتجات نافدة</h6>
                            <h3><?= number_format(count(array_filter($products, function($p) { return ($p['total_stock'] ?? 0) <= 0; }))) ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-times-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Products Table -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">قائمة المنتجات</h6>
                </div>
                <div class="card-body">
                    <?php if (empty($products)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-cube fa-3x text-gray-300 mb-3"></i>
                            <h5 class="text-muted">لا توجد منتجات</h5>
                            <p class="text-muted">لم يتم العثور على منتجات تطابق معايير البحث</p>
                            <a href="<?= base_url('inventory/products/create') ?>" class="btn btn-primary">
                                <i class="fas fa-plus-circle me-2"></i> إضافة منتج جديد
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="productsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الكود</th>
                                        <th>اسم المنتج</th>
                                        <th>الفئة</th>
                                        <th>الوحدة</th>
                                        <th>سعر التكلفة</th>
                                        <th>سعر البيع</th>
                                        <th>المخزون</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($products as $product): ?>
                                        <tr>
                                            <td>
                                                <strong><?= htmlspecialchars($product['product_code']) ?></strong>
                                                <?php if (!empty($product['barcode'])): ?>
                                                    <br><small class="text-muted"><?= htmlspecialchars($product['barcode']) ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?= htmlspecialchars($product['product_name_ar']) ?></strong>
                                                    <?php if (!empty($product['product_name_en'])): ?>
                                                        <br><small class="text-muted"><?= htmlspecialchars($product['product_name_en']) ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td><?= htmlspecialchars($product['category_name_ar'] ?? 'غير محدد') ?></td>
                                            <td><?= htmlspecialchars($product['unit_symbol_ar'] ?? $product['unit_name_ar'] ?? 'غير محدد') ?></td>
                                            <td><?= number_format($product['cost_price'], 2) ?> ر.س</td>
                                            <td><?= number_format($product['selling_price'], 2) ?> ر.س</td>
                                            <td>
                                                <?php 
                                                $stock = $product['total_stock'] ?? 0;
                                                $available = $product['available_stock'] ?? 0;
                                                $reorder_point = $product['reorder_point'] ?? 0;
                                                
                                                if ($stock <= 0) {
                                                    $badge_class = 'bg-danger';
                                                    $status_text = 'نافد';
                                                } elseif ($available <= $reorder_point) {
                                                    $badge_class = 'bg-warning';
                                                    $status_text = 'يحتاج طلب';
                                                } else {
                                                    $badge_class = 'bg-success';
                                                    $status_text = 'متوفر';
                                                }
                                                ?>
                                                <span class="badge <?= $badge_class ?>"><?= number_format($stock, 2) ?></span>
                                                <br><small class="text-muted"><?= $status_text ?></small>
                                            </td>
                                            <td>
                                                <?php if ($product['is_active']): ?>
                                                    <span class="badge bg-success">نشط</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">غير نشط</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="<?= base_url('inventory/products/' . $product['product_id']) ?>" 
                                                       class="btn btn-outline-primary" title="عرض">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('inventory/products/' . $product['product_id'] . '/edit') ?>" 
                                                       class="btn btn-outline-secondary" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-outline-danger" 
                                                            onclick="deleteProduct(<?= $product['product_id'] ?>)" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i> تأكيد الحذف
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من رغبتك في حذف هذا المنتج؟</p>
                <p class="text-danger"><strong>تحذير:</strong> لا يمكن التراجع عن هذا الإجراء.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i> حذف
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function deleteProduct(productId) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `<?= base_url('inventory/products/') ?>${productId}/delete`;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

// Initialize DataTable if available
document.addEventListener('DOMContentLoaded', function() {
    if (typeof $.fn.DataTable !== 'undefined') {
        $('#productsTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
            },
            "pageLength": 25,
            "order": [[1, "asc"]],
            "columnDefs": [
                { "orderable": false, "targets": -1 }
            ]
        });
    }
});
</script>
