<?php
namespace Modules\Accounting\Inventory\Controllers;

use Modules\Accounting\Inventory\Models\Product;
use Modules\Accounting\Inventory\Models\Category;
use Modules\Accounting\Inventory\Models\Unit;

/**
 * Product Controller
 * متحكم المنتجات
 */
class ProductController {
    
    private $productModel;
    private $categoryModel;
    private $unitModel;

    public function __construct() {
        $this->productModel = new Product();
        $this->categoryModel = new Category();
        $this->unitModel = new Unit();
    }

    /**
     * عرض قائمة المنتجات
     */
    public function index() {
        try {
            $company_id = get_user_company_id();
            
            // الحصول على الفلاتر من الطلب
            $filters = [
                'category_id' => $_GET['category_id'] ?? '',
                'is_active' => isset($_GET['is_active']) ? (int)$_GET['is_active'] : null,
                'search' => $_GET['search'] ?? ''
            ];

            // إزالة الفلاتر الفارغة
            $filters = array_filter($filters, function($value) {
                return $value !== '' && $value !== null;
            });

            $products = $this->productModel->getAll($company_id, $filters);
            $categories = $this->categoryModel->getAll($company_id);

            $data = [
                'title' => 'إدارة المنتجات',
                'products' => $products,
                'categories' => $categories,
                'filters' => $filters,
                'total_count' => count($products)
            ];

            load_view('Modules/Accounting/Inventory/Views/products/index', $data);

        } catch (Exception $e) {
            error_log("Error in ProductController::index: " . $e->getMessage());
            show_error('حدث خطأ أثناء تحميل المنتجات');
        }
    }

    /**
     * عرض تفاصيل منتج
     */
    public function show($product_id) {
        try {
            $company_id = get_user_company_id();
            $product = $this->productModel->getById($product_id, $company_id);

            if (!$product) {
                show_404('المنتج غير موجود');
                return;
            }

            $data = [
                'title' => 'تفاصيل المنتج: ' . $product['product_name_ar'],
                'product' => $product
            ];

            load_view('Modules/Accounting/Inventory/Views/products/show', $data);

        } catch (Exception $e) {
            error_log("Error in ProductController::show: " . $e->getMessage());
            show_error('حدث خطأ أثناء تحميل تفاصيل المنتج');
        }
    }

    /**
     * عرض نموذج إنشاء منتج جديد
     */
    public function create() {
        try {
            $company_id = get_user_company_id();
            $categories = $this->categoryModel->getAll($company_id);
            $units = $this->unitModel->getAll($company_id);

            $data = [
                'title' => 'إضافة منتج جديد',
                'categories' => $categories,
                'units' => $units,
                'product' => [] // منتج فارغ للنموذج
            ];

            load_view('Modules/Accounting/Inventory/Views/products/create', $data);

        } catch (Exception $e) {
            error_log("Error in ProductController::create: " . $e->getMessage());
            show_error('حدث خطأ أثناء تحميل نموذج إنشاء المنتج');
        }
    }

    /**
     * حفظ منتج جديد
     */
    public function store() {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                redirect('/inventory/products');
                return;
            }

            $company_id = get_user_company_id();
            $user_id = get_current_user_id();

            // التحقق من صحة البيانات
            $validation_errors = $this->validateProductData($_POST);
            
            if (!empty($validation_errors)) {
                set_flash_message('error', implode('<br>', $validation_errors));
                $this->create();
                return;
            }

            // التحقق من عدم تكرار كود المنتج
            if ($this->productModel->isProductCodeExists($_POST['product_code'], $company_id)) {
                set_flash_message('error', 'كود المنتج موجود مسبقاً');
                $this->create();
                return;
            }

            // التحقق من عدم تكرار الباركود
            if (!empty($_POST['barcode']) && $this->productModel->isBarcodeExists($_POST['barcode'], $company_id)) {
                set_flash_message('error', 'الباركود موجود مسبقاً');
                $this->create();
                return;
            }

            // إعداد بيانات المنتج
            $product_data = [
                'company_id' => $company_id,
                'product_code' => $_POST['product_code'],
                'barcode' => $_POST['barcode'] ?? null,
                'product_name_ar' => $_POST['product_name_ar'],
                'product_name_en' => $_POST['product_name_en'] ?? null,
                'description_ar' => $_POST['description_ar'] ?? null,
                'description_en' => $_POST['description_en'] ?? null,
                'category_id' => $_POST['category_id'],
                'unit_id' => $_POST['unit_id'],
                'product_type' => $_POST['product_type'] ?? 'product',
                'track_inventory' => isset($_POST['track_inventory']) ? 1 : 0,
                'cost_price' => (float)($_POST['cost_price'] ?? 0),
                'selling_price' => (float)($_POST['selling_price'] ?? 0),
                'min_stock_level' => (float)($_POST['min_stock_level'] ?? 0),
                'max_stock_level' => !empty($_POST['max_stock_level']) ? (float)$_POST['max_stock_level'] : null,
                'reorder_point' => (float)($_POST['reorder_point'] ?? 0),
                'weight' => !empty($_POST['weight']) ? (float)$_POST['weight'] : null,
                'dimensions' => $_POST['dimensions'] ?? null,
                'image_url' => $_POST['image_url'] ?? null,
                'tax_rate' => (float)($_POST['tax_rate'] ?? 0),
                'is_active' => isset($_POST['is_active']) ? 1 : 0,
                'created_by' => $user_id
            ];

            $product_id = $this->productModel->create($product_data);

            if ($product_id) {
                set_flash_message('success', 'تم إنشاء المنتج بنجاح');
                redirect('/inventory/products/' . $product_id);
            } else {
                set_flash_message('error', 'حدث خطأ أثناء إنشاء المنتج');
                $this->create();
            }

        } catch (Exception $e) {
            error_log("Error in ProductController::store: " . $e->getMessage());
            set_flash_message('error', 'حدث خطأ أثناء حفظ المنتج');
            $this->create();
        }
    }

    /**
     * عرض نموذج تعديل منتج
     */
    public function edit($product_id) {
        try {
            $company_id = get_user_company_id();
            $product = $this->productModel->getById($product_id, $company_id);

            if (!$product) {
                show_404('المنتج غير موجود');
                return;
            }

            $categories = $this->categoryModel->getAll($company_id);
            $units = $this->unitModel->getAll($company_id);

            $data = [
                'title' => 'تعديل المنتج: ' . $product['product_name_ar'],
                'product' => $product,
                'categories' => $categories,
                'units' => $units
            ];

            load_view('Modules/Accounting/Inventory/Views/products/edit', $data);

        } catch (Exception $e) {
            error_log("Error in ProductController::edit: " . $e->getMessage());
            show_error('حدث خطأ أثناء تحميل نموذج تعديل المنتج');
        }
    }

    /**
     * تحديث منتج
     */
    public function update($product_id) {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                redirect('/inventory/products');
                return;
            }

            $company_id = get_user_company_id();
            $user_id = get_current_user_id();

            // التحقق من وجود المنتج
            $product = $this->productModel->getById($product_id, $company_id);
            if (!$product) {
                show_404('المنتج غير موجود');
                return;
            }

            // التحقق من صحة البيانات
            $validation_errors = $this->validateProductData($_POST);
            
            if (!empty($validation_errors)) {
                set_flash_message('error', implode('<br>', $validation_errors));
                $this->edit($product_id);
                return;
            }

            // التحقق من عدم تكرار كود المنتج
            if ($this->productModel->isProductCodeExists($_POST['product_code'], $company_id, $product_id)) {
                set_flash_message('error', 'كود المنتج موجود مسبقاً');
                $this->edit($product_id);
                return;
            }

            // التحقق من عدم تكرار الباركود
            if (!empty($_POST['barcode']) && $this->productModel->isBarcodeExists($_POST['barcode'], $company_id, $product_id)) {
                set_flash_message('error', 'الباركود موجود مسبقاً');
                $this->edit($product_id);
                return;
            }

            // إعداد بيانات المنتج
            $product_data = [
                'product_code' => $_POST['product_code'],
                'barcode' => $_POST['barcode'] ?? null,
                'product_name_ar' => $_POST['product_name_ar'],
                'product_name_en' => $_POST['product_name_en'] ?? null,
                'description_ar' => $_POST['description_ar'] ?? null,
                'description_en' => $_POST['description_en'] ?? null,
                'category_id' => $_POST['category_id'],
                'unit_id' => $_POST['unit_id'],
                'product_type' => $_POST['product_type'] ?? 'product',
                'track_inventory' => isset($_POST['track_inventory']) ? 1 : 0,
                'cost_price' => (float)($_POST['cost_price'] ?? 0),
                'selling_price' => (float)($_POST['selling_price'] ?? 0),
                'min_stock_level' => (float)($_POST['min_stock_level'] ?? 0),
                'max_stock_level' => !empty($_POST['max_stock_level']) ? (float)$_POST['max_stock_level'] : null,
                'reorder_point' => (float)($_POST['reorder_point'] ?? 0),
                'weight' => !empty($_POST['weight']) ? (float)$_POST['weight'] : null,
                'dimensions' => $_POST['dimensions'] ?? null,
                'image_url' => $_POST['image_url'] ?? null,
                'tax_rate' => (float)($_POST['tax_rate'] ?? 0),
                'is_active' => isset($_POST['is_active']) ? 1 : 0,
                'updated_by' => $user_id
            ];

            $success = $this->productModel->update($product_id, $product_data, $company_id);

            if ($success) {
                set_flash_message('success', 'تم تحديث المنتج بنجاح');
                redirect('/inventory/products/' . $product_id);
            } else {
                set_flash_message('error', 'حدث خطأ أثناء تحديث المنتج');
                $this->edit($product_id);
            }

        } catch (Exception $e) {
            error_log("Error in ProductController::update: " . $e->getMessage());
            set_flash_message('error', 'حدث خطأ أثناء تحديث المنتج');
            $this->edit($product_id);
        }
    }

    /**
     * حذف منتج
     */
    public function delete($product_id) {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                redirect('/inventory/products');
                return;
            }

            $company_id = get_user_company_id();

            // التحقق من وجود المنتج
            $product = $this->productModel->getById($product_id, $company_id);
            if (!$product) {
                set_flash_message('error', 'المنتج غير موجود');
                redirect('/inventory/products');
                return;
            }

            $success = $this->productModel->delete($product_id, $company_id);

            if ($success) {
                set_flash_message('success', 'تم حذف المنتج بنجاح');
            } else {
                set_flash_message('error', 'لا يمكن حذف المنتج لوجود حركات مخزون مرتبطة به');
            }

            redirect('/inventory/products');

        } catch (Exception $e) {
            error_log("Error in ProductController::delete: " . $e->getMessage());
            set_flash_message('error', 'حدث خطأ أثناء حذف المنتج');
            redirect('/inventory/products');
        }
    }

    /**
     * عرض المنتجات التي تحتاج إعادة طلب
     */
    public function reorder() {
        try {
            $company_id = get_user_company_id();
            $products = $this->productModel->getProductsNeedReorder($company_id);

            $data = [
                'title' => 'المنتجات التي تحتاج إعادة طلب',
                'products' => $products,
                'total_count' => count($products)
            ];

            load_view('Modules/Accounting/Inventory/Views/products/reorder', $data);

        } catch (Exception $e) {
            error_log("Error in ProductController::reorder: " . $e->getMessage());
            show_error('حدث خطأ أثناء تحميل المنتجات');
        }
    }

    /**
     * التحقق من صحة بيانات المنتج
     */
    private function validateProductData($data) {
        $errors = [];

        if (empty($data['product_code'])) {
            $errors[] = 'كود المنتج مطلوب';
        }

        if (empty($data['product_name_ar'])) {
            $errors[] = 'اسم المنتج بالعربية مطلوب';
        }

        if (empty($data['category_id'])) {
            $errors[] = 'فئة المنتج مطلوبة';
        }

        if (empty($data['unit_id'])) {
            $errors[] = 'وحدة القياس مطلوبة';
        }

        if (!empty($data['cost_price']) && !is_numeric($data['cost_price'])) {
            $errors[] = 'سعر التكلفة يجب أن يكون رقماً';
        }

        if (!empty($data['selling_price']) && !is_numeric($data['selling_price'])) {
            $errors[] = 'سعر البيع يجب أن يكون رقماً';
        }

        return $errors;
    }
}
