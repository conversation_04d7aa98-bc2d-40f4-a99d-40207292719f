-- ===================================
-- بيانات أساسية لوحدة المخزون
-- الشركة: 4 | المستخدم: 32
-- ===================================

-- ===================================
-- إدراج وحدات القياس الأساسية
-- ===================================

INSERT INTO `inventory_units` (`company_id`, `unit_code`, `unit_name_ar`, `unit_name_en`, `unit_symbol_ar`, `unit_symbol_en`, `is_base_unit`, `created_by`) VALUES
(4, 'PIECE', 'قطعة', 'Piece', 'قطعة', 'pcs', 1, 32),
(4, 'KG', 'كيلوجرام', 'Kilogram', 'كجم', 'kg', 1, 32),
(4, 'GRAM', 'جرام', 'Gram', 'جم', 'g', 0, 32),
(4, 'LITER', 'لتر', 'Liter', 'لتر', 'L', 1, 32),
(4, 'ML', 'مليلتر', 'Milliliter', 'مل', 'ml', 0, 32),
(4, 'METER', 'متر', 'Meter', 'م', 'm', 1, 32),
(4, 'CM', 'سنتيمتر', 'Centimeter', 'سم', 'cm', 0, 32),
(4, 'BOX', 'صندوق', 'Box', 'صندوق', 'box', 0, 32),
(4, 'CARTON', 'كرتون', 'Carton', 'كرتون', 'carton', 0, 32),
(4, 'PACK', 'عبوة', 'Pack', 'عبوة', 'pack', 0, 32),
(4, 'DOZEN', 'دزينة', 'Dozen', 'دزينة', 'dz', 0, 32),
(4, 'SET', 'طقم', 'Set', 'طقم', 'set', 0, 32);

-- ===================================
-- إدراج فئات المنتجات الأساسية
-- ===================================

INSERT INTO `inventory_categories` (`company_id`, `category_code`, `category_name_ar`, `category_name_en`, `display_order`, `created_by`) VALUES
(4, 'ELECTRONICS', 'إلكترونيات', 'Electronics', 1, 32),
(4, 'COMPUTERS', 'حاسوب وملحقاته', 'Computers & Accessories', 2, 32),
(4, 'MOBILE', 'هواتف ذكية', 'Mobile Phones', 3, 32),
(4, 'FURNITURE', 'أثاث', 'Furniture', 4, 32),
(4, 'OFFICE_FURNITURE', 'أثاث مكتبي', 'Office Furniture', 5, 32),
(4, 'OFFICE_SUPPLIES', 'مستلزمات مكتبية', 'Office Supplies', 6, 32),
(4, 'STATIONERY', 'قرطاسية', 'Stationery', 7, 32),
(4, 'BOOKS', 'كتب', 'Books', 8, 32),
(4, 'FOOD_BEVERAGE', 'أطعمة ومشروبات', 'Food & Beverage', 9, 32),
(4, 'CLEANING', 'مواد تنظيف', 'Cleaning Supplies', 10, 32),
(4, 'TOOLS', 'أدوات', 'Tools', 11, 32),
(4, 'AUTOMOTIVE', 'قطع غيار سيارات', 'Automotive Parts', 12, 32),
(4, 'CLOTHING', 'ملابس', 'Clothing', 13, 32),
(4, 'MEDICAL', 'مستلزمات طبية', 'Medical Supplies', 14, 32),
(4, 'SPORTS', 'مستلزمات رياضية', 'Sports Equipment', 15, 32);

-- ===================================
-- إدراج المخازن الأساسية
-- ===================================

INSERT INTO `inventory_warehouses` (`company_id`, `warehouse_code`, `warehouse_name_ar`, `warehouse_name_en`, `address`, `warehouse_type`, `capacity`, `created_by`) VALUES
(4, 'MAIN_WH', 'المخزن الرئيسي', 'Main Warehouse', 'الرياض، المملكة العربية السعودية', 'main', 10000.00, 32),
(4, 'BRANCH_RUH', 'مخزن فرع الرياض', 'Riyadh Branch Warehouse', 'الرياض، حي العليا', 'branch', 5000.00, 32),
(4, 'BRANCH_JED', 'مخزن فرع جدة', 'Jeddah Branch Warehouse', 'جدة، حي الروضة', 'branch', 3000.00, 32),
(4, 'BRANCH_DAM', 'مخزن فرع الدمام', 'Dammam Branch Warehouse', 'الدمام، حي الفيصلية', 'branch', 2000.00, 32),
(4, 'VIRTUAL_WH', 'المخزن الافتراضي', 'Virtual Warehouse', 'للمنتجات الرقمية والخدمات', 'virtual', NULL, 32),
(4, 'EXTERNAL_WH', 'المخزن الخارجي', 'External Warehouse', 'مخزن خارجي للتخزين المؤقت', 'external', 1000.00, 32);

-- ===================================
-- إدراج منتجات تجريبية
-- ===================================

-- منتجات إلكترونية
INSERT INTO `inventory_products` (`company_id`, `product_code`, `barcode`, `product_name_ar`, `product_name_en`, `category_id`, `unit_id`, `cost_price`, `selling_price`, `min_stock_level`, `reorder_point`, `created_by`) VALUES
(4, 'LAPTOP001', '1234567890123', 'لابتوب ديل انسبايرون', 'Dell Inspiron Laptop', (SELECT category_id FROM inventory_categories WHERE category_code = 'COMPUTERS' AND company_id = 4 LIMIT 1), (SELECT unit_id FROM inventory_units WHERE unit_code = 'PIECE' AND company_id = 4 LIMIT 1), 2500.00, 3200.00, 5, 10, 32),
(4, 'MOUSE001', '1234567890124', 'فأرة لاسلكية', 'Wireless Mouse', (SELECT category_id FROM inventory_categories WHERE category_code = 'COMPUTERS' AND company_id = 4 LIMIT 1), (SELECT unit_id FROM inventory_units WHERE unit_code = 'PIECE' AND company_id = 4 LIMIT 1), 25.00, 45.00, 20, 30, 32),
(4, 'KEYBOARD001', '1234567890125', 'لوحة مفاتيح عربية', 'Arabic Keyboard', (SELECT category_id FROM inventory_categories WHERE category_code = 'COMPUTERS' AND company_id = 4 LIMIT 1), (SELECT unit_id FROM inventory_units WHERE unit_code = 'PIECE' AND company_id = 4 LIMIT 1), 35.00, 60.00, 15, 25, 32);

-- أثاث مكتبي
INSERT INTO `inventory_products` (`company_id`, `product_code`, `barcode`, `product_name_ar`, `product_name_en`, `category_id`, `unit_id`, `cost_price`, `selling_price`, `min_stock_level`, `reorder_point`, `created_by`) VALUES
(4, 'CHAIR001', '1234567890126', 'كرسي مكتب جلد', 'Leather Office Chair', (SELECT category_id FROM inventory_categories WHERE category_code = 'OFFICE_FURNITURE' AND company_id = 4 LIMIT 1), (SELECT unit_id FROM inventory_units WHERE unit_code = 'PIECE' AND company_id = 4 LIMIT 1), 180.00, 280.00, 8, 15, 32),
(4, 'DESK001', '1234567890127', 'مكتب خشبي', 'Wooden Desk', (SELECT category_id FROM inventory_categories WHERE category_code = 'OFFICE_FURNITURE' AND company_id = 4 LIMIT 1), (SELECT unit_id FROM inventory_units WHERE unit_code = 'PIECE' AND company_id = 4 LIMIT 1), 350.00, 550.00, 5, 10, 32);

-- قرطاسية
INSERT INTO `inventory_products` (`company_id`, `product_code`, `barcode`, `product_name_ar`, `product_name_en`, `category_id`, `unit_id`, `cost_price`, `selling_price`, `min_stock_level`, `reorder_point`, `created_by`) VALUES
(4, 'PEN001', '1234567890128', 'قلم حبر أزرق', 'Blue Ink Pen', (SELECT category_id FROM inventory_categories WHERE category_code = 'STATIONERY' AND company_id = 4 LIMIT 1), (SELECT unit_id FROM inventory_units WHERE unit_code = 'PIECE' AND company_id = 4 LIMIT 1), 1.50, 3.00, 100, 200, 32),
(4, 'PAPER001', '1234567890129', 'ورق A4', 'A4 Paper', (SELECT category_id FROM inventory_categories WHERE category_code = 'STATIONERY' AND company_id = 4 LIMIT 1), (SELECT unit_id FROM inventory_units WHERE unit_code = 'PACK' AND company_id = 4 LIMIT 1), 15.00, 25.00, 50, 100, 32),
(4, 'NOTEBOOK001', '1234567890130', 'دفتر ملاحظات', 'Notebook', (SELECT category_id FROM inventory_categories WHERE category_code = 'STATIONERY' AND company_id = 4 LIMIT 1), (SELECT unit_id FROM inventory_units WHERE unit_code = 'PIECE' AND company_id = 4 LIMIT 1), 8.00, 15.00, 30, 50, 32);

-- مواد تنظيف
INSERT INTO `inventory_products` (`company_id`, `product_code`, `barcode`, `product_name_ar`, `product_name_en`, `category_id`, `unit_id`, `cost_price`, `selling_price`, `min_stock_level`, `reorder_point`, `created_by`) VALUES
(4, 'DETERGENT001', '1234567890131', 'مسحوق غسيل', 'Laundry Detergent', (SELECT category_id FROM inventory_categories WHERE category_code = 'CLEANING' AND company_id = 4 LIMIT 1), (SELECT unit_id FROM inventory_units WHERE unit_code = 'KG' AND company_id = 4 LIMIT 1), 12.00, 20.00, 25, 40, 32),
(4, 'DISINFECTANT001', '1234567890132', 'مطهر', 'Disinfectant', (SELECT category_id FROM inventory_categories WHERE category_code = 'CLEANING' AND company_id = 4 LIMIT 1), (SELECT unit_id FROM inventory_units WHERE unit_code = 'LITER' AND company_id = 4 LIMIT 1), 8.00, 15.00, 20, 35, 32);

-- ===================================
-- إدراج أرصدة أولية
-- ===================================

INSERT INTO `inventory_stock` (`company_id`, `product_id`, `warehouse_id`, `quantity_on_hand`, `quantity_available`, `average_cost`, `created_by`) VALUES
(4, (SELECT product_id FROM inventory_products WHERE product_code = 'LAPTOP001' AND company_id = 4), (SELECT warehouse_id FROM inventory_warehouses WHERE warehouse_code = 'MAIN_WH' AND company_id = 4), 15.000, 15.000, 2500.00, 32),
(4, (SELECT product_id FROM inventory_products WHERE product_code = 'MOUSE001' AND company_id = 4), (SELECT warehouse_id FROM inventory_warehouses WHERE warehouse_code = 'MAIN_WH' AND company_id = 4), 50.000, 50.000, 25.00, 32),
(4, (SELECT product_id FROM inventory_products WHERE product_code = 'KEYBOARD001' AND company_id = 4), (SELECT warehouse_id FROM inventory_warehouses WHERE warehouse_code = 'MAIN_WH' AND company_id = 4), 30.000, 30.000, 35.00, 32),
(4, (SELECT product_id FROM inventory_products WHERE product_code = 'CHAIR001' AND company_id = 4), (SELECT warehouse_id FROM inventory_warehouses WHERE warehouse_code = 'MAIN_WH' AND company_id = 4), 25.000, 25.000, 180.00, 32),
(4, (SELECT product_id FROM inventory_products WHERE product_code = 'DESK001' AND company_id = 4), (SELECT warehouse_id FROM inventory_warehouses WHERE warehouse_code = 'MAIN_WH' AND company_id = 4), 12.000, 12.000, 350.00, 32),
(4, (SELECT product_id FROM inventory_products WHERE product_code = 'PEN001' AND company_id = 4), (SELECT warehouse_id FROM inventory_warehouses WHERE warehouse_code = 'MAIN_WH' AND company_id = 4), 500.000, 500.000, 1.50, 32),
(4, (SELECT product_id FROM inventory_products WHERE product_code = 'PAPER001' AND company_id = 4), (SELECT warehouse_id FROM inventory_warehouses WHERE warehouse_code = 'MAIN_WH' AND company_id = 4), 100.000, 100.000, 15.00, 32),
(4, (SELECT product_id FROM inventory_products WHERE product_code = 'NOTEBOOK001' AND company_id = 4), (SELECT warehouse_id FROM inventory_warehouses WHERE warehouse_code = 'MAIN_WH' AND company_id = 4), 80.000, 80.000, 8.00, 32),
(4, (SELECT product_id FROM inventory_products WHERE product_code = 'DETERGENT001' AND company_id = 4), (SELECT warehouse_id FROM inventory_warehouses WHERE warehouse_code = 'MAIN_WH' AND company_id = 4), 60.000, 60.000, 12.00, 32),
(4, (SELECT product_id FROM inventory_products WHERE product_code = 'DISINFECTANT001' AND company_id = 4), (SELECT warehouse_id FROM inventory_warehouses WHERE warehouse_code = 'MAIN_WH' AND company_id = 4), 40.000, 40.000, 8.00, 32);

-- ===================================
-- عرض النتائج
-- ===================================

SELECT 'تم إدراج البيانات الأساسية بنجاح للشركة 4' AS result;
SELECT COUNT(*) AS units_count FROM inventory_units WHERE company_id = 4;
SELECT COUNT(*) AS categories_count FROM inventory_categories WHERE company_id = 4;
SELECT COUNT(*) AS warehouses_count FROM inventory_warehouses WHERE company_id = 4;
SELECT COUNT(*) AS products_count FROM inventory_products WHERE company_id = 4;
SELECT COUNT(*) AS stock_count FROM inventory_stock WHERE company_id = 4;
