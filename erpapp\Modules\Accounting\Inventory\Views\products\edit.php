<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title">
                    <i class="fas fa-edit text-primary"></i> <?= $title ?? 'تعديل المنتج' ?>
                </h1>
                <div class="btn-group">
                    <a href="<?= base_url('inventory/products/' . $product['product_id']) ?>" class="btn btn-outline-info">
                        <i class="fas fa-eye me-2"></i> عرض التفاصيل
                    </a>
                    <a href="<?= base_url('inventory/products') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i> العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (isset($_SESSION['flash_message'])): ?>
        <div class="alert alert-<?= $_SESSION['flash_message']['type'] ?> alert-dismissible fade show" role="alert">
            <?= $_SESSION['flash_message']['message'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['flash_message']); ?>
    <?php endif; ?>

    <!-- Form -->
    <form method="POST" action="<?= base_url('inventory/products/' . $product['product_id'] . '/update') ?>" id="productForm">
        <div class="row">
            <!-- Basic Information -->
            <div class="col-lg-8">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-info-circle me-2"></i> المعلومات الأساسية
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="product_code" class="form-label">كود المنتج <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="product_code" name="product_code" 
                                       value="<?= htmlspecialchars($product['product_code']) ?>" required>
                                <div class="form-text">كود فريد للمنتج</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="barcode" class="form-label">الباركود</label>
                                <input type="text" class="form-control" id="barcode" name="barcode" 
                                       value="<?= htmlspecialchars($product['barcode'] ?? '') ?>">
                                <div class="form-text">باركود المنتج (اختياري)</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="product_name_ar" class="form-label">اسم المنتج (عربي) <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="product_name_ar" name="product_name_ar" 
                                       value="<?= htmlspecialchars($product['product_name_ar']) ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="product_name_en" class="form-label">اسم المنتج (إنجليزي)</label>
                                <input type="text" class="form-control" id="product_name_en" name="product_name_en" 
                                       value="<?= htmlspecialchars($product['product_name_en'] ?? '') ?>">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="description_ar" class="form-label">الوصف (عربي)</label>
                                <textarea class="form-control" id="description_ar" name="description_ar" rows="3"><?= htmlspecialchars($product['description_ar'] ?? '') ?></textarea>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="description_en" class="form-label">الوصف (إنجليزي)</label>
                                <textarea class="form-control" id="description_en" name="description_en" rows="3"><?= htmlspecialchars($product['description_en'] ?? '') ?></textarea>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="category_id" class="form-label">الفئة <span class="text-danger">*</span></label>
                                <select class="form-select" id="category_id" name="category_id" required>
                                    <option value="">اختر الفئة</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?= $category['category_id'] ?>" 
                                                <?= $product['category_id'] == $category['category_id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($category['category_name_ar']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="unit_id" class="form-label">وحدة القياس <span class="text-danger">*</span></label>
                                <select class="form-select" id="unit_id" name="unit_id" required>
                                    <option value="">اختر الوحدة</option>
                                    <?php foreach ($units as $unit): ?>
                                        <option value="<?= $unit['unit_id'] ?>" 
                                                <?= $product['unit_id'] == $unit['unit_id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($unit['unit_name_ar']) ?> (<?= htmlspecialchars($unit['unit_symbol_ar']) ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="product_type" class="form-label">نوع المنتج</label>
                                <select class="form-select" id="product_type" name="product_type">
                                    <option value="product" <?= $product['product_type'] == 'product' ? 'selected' : '' ?>>منتج</option>
                                    <option value="service" <?= $product['product_type'] == 'service' ? 'selected' : '' ?>>خدمة</option>
                                    <option value="digital" <?= $product['product_type'] == 'digital' ? 'selected' : '' ?>>رقمي</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pricing Information -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-dollar-sign me-2"></i> معلومات التسعير
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="cost_price" class="form-label">سعر التكلفة</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="cost_price" name="cost_price" 
                                           value="<?= $product['cost_price'] ?>" step="0.01" min="0">
                                    <span class="input-group-text">ر.س</span>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="selling_price" class="form-label">سعر البيع</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="selling_price" name="selling_price" 
                                           value="<?= $product['selling_price'] ?>" step="0.01" min="0">
                                    <span class="input-group-text">ر.س</span>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="tax_rate" class="form-label">معدل الضريبة (%)</label>
                                <input type="number" class="form-control" id="tax_rate" name="tax_rate" 
                                       value="<?= $product['tax_rate'] ?>" step="0.01" min="0" max="100">
                            </div>
                        </div>

                        <!-- Profit Margin Display -->
                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-info" id="profitMarginAlert">
                                    <i class="fas fa-calculator me-2"></i>
                                    <span id="profitMarginText">هامش الربح سيظهر هنا</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Inventory Information -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-boxes me-2"></i> معلومات المخزون
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="track_inventory" name="track_inventory" 
                                           <?= $product['track_inventory'] ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="track_inventory">
                                        تتبع المخزون لهذا المنتج
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div id="inventory_fields">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="min_stock_level" class="form-label">الحد الأدنى للمخزون</label>
                                    <input type="number" class="form-control" id="min_stock_level" name="min_stock_level" 
                                           value="<?= $product['min_stock_level'] ?>" step="0.01" min="0">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="max_stock_level" class="form-label">الحد الأقصى للمخزون</label>
                                    <input type="number" class="form-control" id="max_stock_level" name="max_stock_level" 
                                           value="<?= $product['max_stock_level'] ?? '' ?>" step="0.01" min="0">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="reorder_point" class="form-label">نقطة إعادة الطلب</label>
                                    <input type="number" class="form-control" id="reorder_point" name="reorder_point" 
                                           value="<?= $product['reorder_point'] ?>" step="0.01" min="0">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="col-lg-4">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-cog me-2"></i> إعدادات إضافية
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="weight" class="form-label">الوزن (كجم)</label>
                            <input type="number" class="form-control" id="weight" name="weight" 
                                   value="<?= $product['weight'] ?? '' ?>" step="0.001" min="0">
                        </div>

                        <div class="mb-3">
                            <label for="dimensions" class="form-label">الأبعاد</label>
                            <input type="text" class="form-control" id="dimensions" name="dimensions" 
                                   value="<?= htmlspecialchars($product['dimensions'] ?? '') ?>" 
                                   placeholder="الطول × العرض × الارتفاع">
                        </div>

                        <div class="mb-3">
                            <label for="image_url" class="form-label">رابط الصورة</label>
                            <input type="url" class="form-control" id="image_url" name="image_url" 
                                   value="<?= htmlspecialchars($product['image_url'] ?? '') ?>">
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       <?= $product['is_active'] ? 'checked' : '' ?>>
                                <label class="form-check-label" for="is_active">
                                    منتج نشط
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Current Image Preview -->
                <?php if (!empty($product['image_url'])): ?>
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-image me-2"></i> الصورة الحالية
                        </h6>
                    </div>
                    <div class="card-body text-center">
                        <img src="<?= htmlspecialchars($product['image_url']) ?>" 
                             alt="<?= htmlspecialchars($product['product_name_ar']) ?>" 
                             class="img-fluid rounded" style="max-height: 150px;">
                    </div>
                </div>
                <?php endif; ?>

                <!-- Action Buttons -->
                <div class="card shadow">
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i> حفظ التغييرات
                            </button>
                            <a href="<?= base_url('inventory/products/' . $product['product_id']) ?>" class="btn btn-outline-info">
                                <i class="fas fa-eye me-2"></i> عرض التفاصيل
                            </a>
                            <a href="<?= base_url('inventory/products') ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i> إلغاء
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const trackInventoryCheckbox = document.getElementById('track_inventory');
    const inventoryFields = document.getElementById('inventory_fields');
    const costPriceInput = document.getElementById('cost_price');
    const sellingPriceInput = document.getElementById('selling_price');
    const profitMarginText = document.getElementById('profitMarginText');

    function toggleInventoryFields() {
        if (trackInventoryCheckbox.checked) {
            inventoryFields.style.display = 'block';
        } else {
            inventoryFields.style.display = 'none';
        }
    }

    function calculateProfitMargin() {
        const costPrice = parseFloat(costPriceInput.value) || 0;
        const sellingPrice = parseFloat(sellingPriceInput.value) || 0;
        const profitMargin = sellingPrice - costPrice;
        const profitPercentage = costPrice > 0 ? (profitMargin / costPrice) * 100 : 0;

        profitMarginText.innerHTML = `
            هامش الربح: <strong>${profitMargin.toFixed(2)} ر.س</strong> 
            (<strong>${profitPercentage.toFixed(1)}%</strong>)
        `;
    }

    trackInventoryCheckbox.addEventListener('change', toggleInventoryFields);
    costPriceInput.addEventListener('input', calculateProfitMargin);
    sellingPriceInput.addEventListener('input', calculateProfitMargin);

    // Initial calls
    toggleInventoryFields();
    calculateProfitMargin();

    // Form validation
    document.getElementById('productForm').addEventListener('submit', function(e) {
        const productCode = document.getElementById('product_code').value.trim();
        const productNameAr = document.getElementById('product_name_ar').value.trim();
        const categoryId = document.getElementById('category_id').value;
        const unitId = document.getElementById('unit_id').value;

        if (!productCode || !productNameAr || !categoryId || !unitId) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
            return false;
        }
    });
});
</script>
