<?php
/**
 * اختبار أمان نظام الصلاحيات
 * هذا الملف لاختبار الثغرات الأمنية والتأكد من عمل النظام بشكل صحيح
 */

// تحميل النظام
require_once 'loader.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أمان نظام الصلاحيات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-4">
    <h1 class="text-center mb-4">
        <i class="fas fa-shield-alt text-danger"></i> اختبار أمان نظام الصلاحيات
    </h1>

    <?php 
    $user = current_user();
    $securityTests = [];
    
    // اختبار 1: التحقق من تسجيل الدخول
    $securityTests[] = [
        'name' => 'تسجيل الدخول',
        'status' => $user ? 'pass' : 'fail',
        'message' => $user ? 'المستخدم مسجل دخول' : 'المستخدم غير مسجل دخول',
        'critical' => true
    ];
    
    if ($user) {
        $companyId = $user['current_company_id'];
        
        // اختبار 2: التحقق من وجود شركة حالية
        $securityTests[] = [
            'name' => 'الشركة الحالية',
            'status' => $companyId ? 'pass' : 'fail',
            'message' => $companyId ? "الشركة الحالية: $companyId" : 'لا توجد شركة حالية',
            'critical' => true
        ];
        
        if ($companyId) {
            // اختبار 3: التحقق من ملكية الشركة
            $isOwner = isCompanyOwner();
            $securityTests[] = [
                'name' => 'ملكية الشركة',
                'status' => $isOwner ? 'info' : 'pass',
                'message' => $isOwner ? 'المستخدم مالك الشركة (صلاحيات كاملة)' : 'المستخدم ليس مالك الشركة',
                'critical' => false
            ];
            
            // اختبار 4: التحقق من العضوية في الشركة
            $isMember = isUserInCompany();
            $securityTests[] = [
                'name' => 'العضوية في الشركة',
                'status' => $isMember || $isOwner ? 'pass' : 'fail',
                'message' => $isMember || $isOwner ? 'المستخدم عضو في الشركة' : 'المستخدم غير مضاف للشركة',
                'critical' => true
            ];
            
            // اختبار 5: التحقق من المنصب
            $position = getUserPosition();
            $securityTests[] = [
                'name' => 'المنصب',
                'status' => $position || $isOwner ? 'pass' : 'fail',
                'message' => $position ? "المنصب: " . $position['PositionNameAR'] : ($isOwner ? 'مالك الشركة' : 'لا يوجد منصب'),
                'critical' => !$isOwner
            ];
            
            // اختبار 6: التحقق من حالة المستخدم
            $status = getUserCompanyStatus();
            $securityTests[] = [
                'name' => 'حالة المستخدم',
                'status' => $status && $status['status'] === 'accepted' && $status['user_status'] === 'active' ? 'pass' : 'fail',
                'message' => $status ? "الحالة: {$status['status']} - النشاط: {$status['user_status']}" : 'لا توجد حالة',
                'critical' => !$isOwner
            ];
            
            // اختبار 7: اختبار الصلاحيات الأساسية
            $basicPermissions = [
                'inventory_management' => 'إدارة المخزون',
                'company_management' => 'إدارة الشركات',
                'user_management' => 'إدارة المستخدمين',
                'accounting' => 'المحاسبة'
            ];
            
            foreach ($basicPermissions as $program => $name) {
                $hasPermission = hasPermission($program, 'view');
                $securityTests[] = [
                    'name' => "صلاحية $name",
                    'status' => $hasPermission ? 'pass' : 'warning',
                    'message' => $hasPermission ? "يمكن الوصول لـ $name" : "لا يمكن الوصول لـ $name",
                    'critical' => false
                ];
            }
        }
    }
    ?>

    <!-- نتائج الاختبارات -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5><i class="fas fa-clipboard-check"></i> نتائج اختبارات الأمان</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الاختبار</th>
                                    <th>النتيجة</th>
                                    <th>التفاصيل</th>
                                    <th>الأهمية</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($securityTests as $test): ?>
                                <tr class="<?= $test['status'] === 'fail' ? 'table-danger' : ($test['status'] === 'warning' ? 'table-warning' : ($test['status'] === 'info' ? 'table-info' : 'table-success')) ?>">
                                    <td>
                                        <strong><?= htmlspecialchars($test['name']) ?></strong>
                                    </td>
                                    <td>
                                        <?php if ($test['status'] === 'pass'): ?>
                                        <span class="badge bg-success">
                                            <i class="fas fa-check"></i> نجح
                                        </span>
                                        <?php elseif ($test['status'] === 'fail'): ?>
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times"></i> فشل
                                        </span>
                                        <?php elseif ($test['status'] === 'warning'): ?>
                                        <span class="badge bg-warning">
                                            <i class="fas fa-exclamation-triangle"></i> تحذير
                                        </span>
                                        <?php else: ?>
                                        <span class="badge bg-info">
                                            <i class="fas fa-info"></i> معلومات
                                        </span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= htmlspecialchars($test['message']) ?></td>
                                    <td>
                                        <?php if ($test['critical']): ?>
                                        <span class="badge bg-danger">حرج</span>
                                        <?php else: ?>
                                        <span class="badge bg-secondary">عادي</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ملخص الأمان -->
    <div class="row mt-4">
        <div class="col-12">
            <?php
            $criticalFails = array_filter($securityTests, function($test) {
                return $test['critical'] && $test['status'] === 'fail';
            });
            
            $totalTests = count($securityTests);
            $passedTests = count(array_filter($securityTests, function($test) {
                return $test['status'] === 'pass' || $test['status'] === 'info';
            }));
            
            $securityScore = round(($passedTests / $totalTests) * 100);
            ?>
            
            <div class="card">
                <div class="card-header <?= count($criticalFails) > 0 ? 'bg-danger' : ($securityScore >= 80 ? 'bg-success' : 'bg-warning') ?> text-white">
                    <h5>
                        <i class="fas fa-shield-alt"></i> 
                        ملخص الأمان - النتيجة: <?= $securityScore ?>%
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center">
                                <h3 class="text-primary"><?= $passedTests ?>/<?= $totalTests ?></h3>
                                <p>اختبارات نجحت</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h3 class="text-danger"><?= count($criticalFails) ?></h3>
                                <p>مشاكل حرجة</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h3 class="<?= $securityScore >= 80 ? 'text-success' : 'text-warning' ?>"><?= $securityScore ?>%</h3>
                                <p>نسبة الأمان</p>
                            </div>
                        </div>
                    </div>
                    
                    <?php if (count($criticalFails) > 0): ?>
                    <div class="alert alert-danger mt-3">
                        <h6><i class="fas fa-exclamation-triangle"></i> مشاكل حرجة تحتاج إصلاح:</h6>
                        <ul class="mb-0">
                            <?php foreach ($criticalFails as $fail): ?>
                            <li><?= htmlspecialchars($fail['name']) ?>: <?= htmlspecialchars($fail['message']) ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php elseif ($securityScore >= 80): ?>
                    <div class="alert alert-success mt-3">
                        <i class="fas fa-check-circle"></i> 
                        <strong>ممتاز!</strong> نظام الصلاحيات يعمل بشكل صحيح وآمن.
                    </div>
                    <?php else: ?>
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle"></i> 
                        <strong>تحذير:</strong> هناك بعض المشاكل في نظام الصلاحيات تحتاج مراجعة.
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- توصيات الأمان -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5><i class="fas fa-lightbulb"></i> توصيات الأمان</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item">
                            <i class="fas fa-check text-success"></i>
                            تأكد من أن جميع المستخدمين مضافين في جدول <code>company_users</code>
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-check text-success"></i>
                            تأكد من أن حالة المستخدمين <code>accepted</code> و <code>active</code>
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-check text-success"></i>
                            تأكد من أن كل مستخدم له <code>position_id</code> صحيح
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-check text-success"></i>
                            راجع صلاحيات كل منصب في جدول <code>permissions</code>
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-check text-success"></i>
                            تأكد من أن مالك الشركة محدد بشكل صحيح في جدول <code>companies</code>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
