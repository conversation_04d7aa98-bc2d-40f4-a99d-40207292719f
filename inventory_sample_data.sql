-- ===================================
-- بيانات أساسية لوحدة المخزون
-- ===================================

-- تعليمات الاستخدام:
-- 1. تأكد من وجود شركة ومستخدم في النظام
-- 2. استبدل القيم أدناه بالقيم الصحيحة من قاعدة البيانات
-- 3. قم بتشغيل هذا الملف بعد إنشاء جداول المخزون

-- للحصول على الشركات والمستخدمين المتاحين:
-- SELECT CompanyID, CompanyName FROM companies WHERE CompanyStatus = 'Active';
-- SELECT UserID, UserName FROM users WHERE AccountStatus = 'Active';

-- ===================================
-- الطريقة الأولى: استخدام متغيرات
-- ===================================

-- تعيين متغيرات للشركة والمستخدم
SET @company_id = (SELECT CompanyID FROM companies WHERE CompanyStatus = 'Active' LIMIT 1);
SET @user_id = (SELECT UserID FROM users WHERE AccountStatus = 'Active' LIMIT 1);

-- التحقق من وجود القيم
SELECT 
    CASE 
        WHEN @company_id IS NULL THEN 'خطأ: لا توجد شركة نشطة في النظام'
        WHEN @user_id IS NULL THEN 'خطأ: لا يوجد مستخدم نشط في النظام'
        ELSE CONCAT('سيتم استخدام الشركة رقم: ', @company_id, ' والمستخدم رقم: ', @user_id)
    END AS status_message;

-- إدراج وحدات قياس أساسية
INSERT INTO `inventory_units` (`company_id`, `unit_code`, `unit_name_ar`, `unit_name_en`, `unit_symbol_ar`, `unit_symbol_en`, `is_base_unit`, `created_by`) VALUES
(@company_id, 'PIECE', 'قطعة', 'Piece', 'قطعة', 'pcs', 1, @user_id),
(@company_id, 'KG', 'كيلوجرام', 'Kilogram', 'كجم', 'kg', 1, @user_id),
(@company_id, 'GRAM', 'جرام', 'Gram', 'جم', 'g', 0, @user_id),
(@company_id, 'LITER', 'لتر', 'Liter', 'لتر', 'L', 1, @user_id),
(@company_id, 'ML', 'مليلتر', 'Milliliter', 'مل', 'ml', 0, @user_id),
(@company_id, 'METER', 'متر', 'Meter', 'م', 'm', 1, @user_id),
(@company_id, 'CM', 'سنتيمتر', 'Centimeter', 'سم', 'cm', 0, @user_id),
(@company_id, 'BOX', 'صندوق', 'Box', 'صندوق', 'box', 0, @user_id),
(@company_id, 'CARTON', 'كرتون', 'Carton', 'كرتون', 'carton', 0, @user_id),
(@company_id, 'PACK', 'عبوة', 'Pack', 'عبوة', 'pack', 0, @user_id);

-- إدراج فئات أساسية
INSERT INTO `inventory_categories` (`company_id`, `category_code`, `category_name_ar`, `category_name_en`, `display_order`, `created_by`) VALUES
(@company_id, 'ELECTRONICS', 'إلكترونيات', 'Electronics', 1, @user_id),
(@company_id, 'COMPUTERS', 'حاسوب وملحقاته', 'Computers & Accessories', 2, @user_id),
(@company_id, 'FURNITURE', 'أثاث', 'Furniture', 3, @user_id),
(@company_id, 'OFFICE_SUPPLIES', 'مستلزمات مكتبية', 'Office Supplies', 4, @user_id),
(@company_id, 'STATIONERY', 'قرطاسية', 'Stationery', 5, @user_id),
(@company_id, 'FOOD_BEVERAGE', 'أطعمة ومشروبات', 'Food & Beverage', 6, @user_id),
(@company_id, 'CLEANING', 'مواد تنظيف', 'Cleaning Supplies', 7, @user_id),
(@company_id, 'TOOLS', 'أدوات', 'Tools', 8, @user_id);

-- إدراج مخازن أساسية
INSERT INTO `inventory_warehouses` (`company_id`, `warehouse_code`, `warehouse_name_ar`, `warehouse_name_en`, `address`, `warehouse_type`, `created_by`) VALUES
(@company_id, 'MAIN_WH', 'المخزن الرئيسي', 'Main Warehouse', 'الرياض، المملكة العربية السعودية', 'main', @user_id),
(@company_id, 'BRANCH_01', 'مخزن الفرع الأول', 'Branch Warehouse 01', 'جدة، المملكة العربية السعودية', 'branch', @user_id),
(@company_id, 'VIRTUAL_WH', 'المخزن الافتراضي', 'Virtual Warehouse', 'للمنتجات الرقمية والخدمات', 'virtual', @user_id);

-- إدراج منتجات تجريبية
INSERT INTO `inventory_products` (`company_id`, `product_code`, `product_name_ar`, `product_name_en`, `category_id`, `unit_id`, `cost_price`, `selling_price`, `min_stock_level`, `created_by`) VALUES
(@company_id, 'LAPTOP001', 'لابتوب ديل', 'Dell Laptop', (SELECT category_id FROM inventory_categories WHERE category_code = 'COMPUTERS' AND company_id = @company_id), (SELECT unit_id FROM inventory_units WHERE unit_code = 'PIECE' AND company_id = @company_id), 2500.00, 3000.00, 5, @user_id),
(@company_id, 'CHAIR001', 'كرسي مكتب', 'Office Chair', (SELECT category_id FROM inventory_categories WHERE category_code = 'FURNITURE' AND company_id = @company_id), (SELECT unit_id FROM inventory_units WHERE unit_code = 'PIECE' AND company_id = @company_id), 150.00, 200.00, 10, @user_id),
(@company_id, 'PEN001', 'قلم حبر أزرق', 'Blue Ink Pen', (SELECT category_id FROM inventory_categories WHERE category_code = 'STATIONERY' AND company_id = @company_id), (SELECT unit_id FROM inventory_units WHERE unit_code = 'PIECE' AND company_id = @company_id), 2.00, 3.50, 100, @user_id);

-- إدراج أرصدة أولية
INSERT INTO `inventory_stock` (`company_id`, `product_id`, `warehouse_id`, `quantity_on_hand`, `quantity_available`, `average_cost`, `created_by`) VALUES
(@company_id, (SELECT product_id FROM inventory_products WHERE product_code = 'LAPTOP001' AND company_id = @company_id), (SELECT warehouse_id FROM inventory_warehouses WHERE warehouse_code = 'MAIN_WH' AND company_id = @company_id), 10.000, 10.000, 2500.00, @user_id),
(@company_id, (SELECT product_id FROM inventory_products WHERE product_code = 'CHAIR001' AND company_id = @company_id), (SELECT warehouse_id FROM inventory_warehouses WHERE warehouse_code = 'MAIN_WH' AND company_id = @company_id), 25.000, 25.000, 150.00, @user_id),
(@company_id, (SELECT product_id FROM inventory_products WHERE product_code = 'PEN001' AND company_id = @company_id), (SELECT warehouse_id FROM inventory_warehouses WHERE warehouse_code = 'MAIN_WH' AND company_id = @company_id), 500.000, 500.000, 2.00, @user_id);

-- عرض النتائج
SELECT 'تم إدراج البيانات الأساسية بنجاح' AS result;
SELECT COUNT(*) AS units_count FROM inventory_units WHERE company_id = @company_id;
SELECT COUNT(*) AS categories_count FROM inventory_categories WHERE company_id = @company_id;
SELECT COUNT(*) AS warehouses_count FROM inventory_warehouses WHERE company_id = @company_id;
SELECT COUNT(*) AS products_count FROM inventory_products WHERE company_id = @company_id;
SELECT COUNT(*) AS stock_count FROM inventory_stock WHERE company_id = @company_id;
