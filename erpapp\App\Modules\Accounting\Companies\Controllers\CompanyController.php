<?php
namespace App\System\Companies\Controllers;

use App\System\Companies\Models\Company;
use App\System\Companies\Services\CompanyService;

/**
 * Company Controller
 */
class CompanyController {
    /**
     * Route parameters
     *
     * @var array
     */
    protected $params = [];

    /**
     * Company model
     *
     * @var Company
     */
    protected $companyModel;

    /**
     * Company service
     *
     * @var CompanyService
     */
    protected $companyService;

    /**
     * Constructor
     *
     * @param array $params Route parameters
     */
    public function __construct($params = []) {
        $this->params = $params;
        $this->companyModel = new Company();
        $this->companyService = new CompanyService();

        // التحقق من تسجيل الدخول
        if (!is_logged_in()) {
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
                // إذا كان الطلب AJAX، أرجع رد خطأ JSON
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'غير مصرح']);
                exit;
            } else {
                redirect(base_url('login'));
            }
        }
    }

    /**
     * عرض قائمة الشركات
     *
     * @return void
     */
    public function index() {
        // الحصول على الشركات التي يملكها المستخدم الحالي
        $user_id = current_user_id();

        // استخدام النموذج للحصول على الشركات
        $owned_companies = $this->companyModel->getOwnedCompanies($user_id);
        $joined_companies = $this->companyModel->getJoinedCompanies($user_id);

        view('Companies::index', [
            'title' => __('الشركات'),
            'owned_companies' => $owned_companies,
            'joined_companies' => $joined_companies
        ]);
    }

    /**
     * عرض نموذج إنشاء شركة جديدة
     *
     * @return void
     */
    public function create() {
        view('Companies::create', [
            'title' => __('إنشاء شركة جديدة')
        ]);
    }

    /**
     * حفظ شركة جديدة
     *
     * @return void
     */
    public function store() {
        // التحقق من أن الطلب هو POST
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect(base_url('companies/create'));
        }

        // التحقق من CSRF token
        if (!csrf_check($_POST['csrf_token'] ?? '')) {
            flash('company_error', 'خطأ في التحقق من الأمان. يرجى المحاولة مرة أخرى.', 'danger');
            redirect(base_url('companies/create'));
        }

        // التحقق مما إذا كان المستخدم لديه شركة مجانية نشطة بالفعل
        $user_id = current_user_id();
        if ($this->companyModel->hasActiveFreeCompany($user_id)) {
            flash('company_error', 'لا يمكنك إنشاء أكثر من شركة مجانية. يرجى ترقية الخطة الحالية أو حذف الشركة الحالية.', 'danger');
            redirect(base_url('companies'));
        }

        // التحقق من البيانات المدخلة
        $company_name = trim($_POST['company_name'] ?? '');
        $company_name_en = trim($_POST['company_name_en'] ?? '');
        $company_email = trim($_POST['company_email'] ?? '');
        $company_phone = trim($_POST['company_phone'] ?? '');
        $company_address = trim($_POST['company_address'] ?? '');
        $company_website = trim($_POST['company_website'] ?? '');
        $tax_id = trim($_POST['tax_id'] ?? '');

        if (empty($company_name)) {
            flash('company_error', 'يرجى إدخال اسم الشركة', 'danger');
            redirect(base_url('companies/create'));
        }

        if (empty($company_email) || !filter_var($company_email, FILTER_VALIDATE_EMAIL)) {
            flash('company_error', 'يرجى إدخال بريد إلكتروني صحيح للشركة', 'danger');
            redirect(base_url('companies/create'));
        }

        if (empty($company_phone)) {
            flash('company_error', 'يرجى إدخال رقم هاتف الشركة', 'danger');
            redirect(base_url('companies/create'));
        }

        // معالجة الصورة إذا تم تحميلها
        $company_logo = null;
        if (isset($_FILES['company_logo']) && $_FILES['company_logo']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = UPLOAD_PATH . '/company_logos/';

            // إنشاء المجلد إذا لم يكن موجودًا
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            $file_name = time() . '_' . basename($_FILES['company_logo']['name']);
            $upload_path = $upload_dir . $file_name;

            // التحقق من نوع الملف
            $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
            $file_type = $_FILES['company_logo']['type'];

            if (!in_array($file_type, $allowed_types)) {
                flash('company_error', 'نوع الملف غير مسموح به. يرجى تحميل صورة (JPG, PNG, GIF)', 'danger');
                redirect(base_url('companies/create'));
            }

            // التحقق من حجم الملف (5MB كحد أقصى)
            if ($_FILES['company_logo']['size'] > 5 * 1024 * 1024) {
                flash('company_error', 'حجم الملف كبير جدًا. الحد الأقصى هو 5 ميجابايت', 'danger');
                redirect(base_url('companies/create'));
            }

            if (move_uploaded_file($_FILES['company_logo']['tmp_name'], $upload_path)) {
                $company_logo = 'public/uploads/company_logos/' . $file_name;
            } else {
                flash('company_error', 'حدث خطأ أثناء تحميل الصورة. يرجى المحاولة مرة أخرى.', 'danger');
                redirect(base_url('companies/create'));
            }
        }

        // حساب تاريخ انتهاء الفترة التجريبية (14 يوم من الآن)
        $trial_end_date = date('Y-m-d H:i:s', strtotime('+14 days'));

        // معالجة وسائل التواصل الاجتماعي
        $social_media_json = null;
        if (isset($_POST['social_media_json'])) {
            $social_media_json = $_POST['social_media_json'];
        } elseif (isset($_POST['social_media']) && is_array($_POST['social_media'])) {
            $social_media = [];
            foreach ($_POST['social_media'] as $key => $value) {
                if (!empty($value)) {
                    $social_media[$key] = $value;
                }
            }
            if (!empty($social_media)) {
                $social_media_json = json_encode($social_media);
            }
        }

        // إعداد بيانات الشركة
        $user_id = current_user_id();
        $company_data = [
            'owner_id' => $user_id,
            'company_name' => $company_name,
            'company_name_en' => $company_name_en,
            'company_email' => $company_email,
            'company_phone' => $company_phone,
            'company_logo' => $company_logo,
            'company_address' => $company_address,
            'company_website' => $company_website,
            'tax_id' => $tax_id,
            'trial_end_date' => $trial_end_date,
            // الحقول الجديدة
            'industry_type' => $_POST['industry_type'] ?? null,
            'company_size' => $_POST['company_size'] ?? null,
            'foundation_date' => !empty($_POST['foundation_date']) ? $_POST['foundation_date'] : null,
            'social_media_links' => $social_media_json,
            'notes' => $_POST['notes'] ?? null
        ];

        // إنشاء الشركة باستخدام النموذج
        $company_id = $this->companyModel->create($company_data);

        if ($company_id) {
            // تحديث الشركة الحالية للمستخدم إذا لم تكن محددة
            if (empty(current_user()['current_company_id'])) {
                $this->companyModel->setCurrentCompanyForUser($user_id, $company_id);

                // تحديث بيانات المستخدم في الجلسة
                if (isset($_SESSION['user'])) {
                    $_SESSION['user']['current_company_id'] = $company_id;
                }
            }

            flash('company_success', 'تم إنشاء الشركة بنجاح', 'success');
            redirect(base_url('companies'));
        } else {
            flash('company_error', 'حدث خطأ أثناء إنشاء الشركة. يرجى المحاولة مرة أخرى.', 'danger');
            redirect(base_url('companies/create'));
        }
    }

    /**
     * عرض تفاصيل شركة
     *
     * @return void
     */
    public function show() {
        $company_id = $this->params['id'] ?? null;

        if (!$company_id) {
            flash('company_error', 'الشركة غير موجودة', 'danger');
            redirect(base_url('companies'));
        }

        // التحقق من وجود الشركة وصلاحية الوصول
        $user_id = current_user_id();

        // التحقق من صلاحية المستخدم للوصول إلى الشركة
        if (!$this->companyModel->userHasAccess($company_id, $user_id)) {
            flash('company_error', 'ليس لديك صلاحية للوصول إلى هذه الشركة', 'danger');
            redirect(base_url('companies'));
        }

        // الحصول على بيانات الشركة
        $company = $this->companyModel->getById($company_id);

        if (!$company) {
            flash('company_error', 'الشركة غير موجودة', 'danger');
            redirect(base_url('companies'));
        }

        // الحصول على عدد المستخدمين والبرامج
        $user_count = $this->companyModel->getUserCount($company_id);
        $program_count = $this->companyModel->getProgramCount($company_id);

        // الحصول على الاشتراك الحالي للشركة باستخدام الخدمة المحلية
        $current_subscription = $this->companyService->getCompanySubscriptionInfo($company_id);

        view('Companies::show', [
            'title' => $company['CompanyName'],
            'company' => $company,
            'user_count' => $user_count,
            'program_count' => $program_count,
            'is_owner' => $this->companyModel->isOwner($company_id, $user_id),
            'current_subscription' => $current_subscription
        ]);
    }

    /**
     * عرض نموذج تعديل شركة
     *
     * @return void
     */
    public function edit() {
        $company_id = $this->params['id'] ?? null;

        if (!$company_id) {
            flash('company_error', 'الشركة غير موجودة', 'danger');
            redirect(base_url('companies'));
        }

        // التحقق من وجود الشركة وصلاحية التعديل (المالك فقط)
        $user_id = current_user_id();

        // التحقق من ملكية المستخدم للشركة
        if (!$this->companyModel->isOwner($company_id, $user_id)) {
            flash('company_error', 'ليس لديك صلاحية لتعديل هذه الشركة', 'danger');
            redirect(base_url('companies'));
        }

        // الحصول على بيانات الشركة
        $company = $this->companyModel->getById($company_id);

        if (!$company) {
            flash('company_error', 'الشركة غير موجودة', 'danger');
            redirect(base_url('companies'));
        }

        view('Companies::edit', [
            'title' => __('تعديل الشركة') . ' - ' . $company['CompanyName'],
            'company' => $company
        ]);
    }

    /**
     * تحديث بيانات الشركة
     *
     * @return void
     */
    public function update() {
        $company_id = $this->params['id'] ?? null;

        if (!$company_id) {
            flash('company_error', 'الشركة غير موجودة', 'danger');
            redirect(base_url('companies'));
        }

        // التحقق من أن الطلب هو POST
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect(base_url('companies/' . $company_id . '/edit'));
        }

        // التحقق من CSRF token
        if (!csrf_check($_POST['csrf_token'] ?? '')) {
            flash('company_error', 'خطأ في التحقق من الأمان. يرجى المحاولة مرة أخرى.', 'danger');
            redirect(base_url('companies/' . $company_id . '/edit'));
        }

        // التحقق من وجود الشركة وصلاحية التعديل (المالك فقط)
        $user_id = current_user_id();

        // التحقق من ملكية المستخدم للشركة
        if (!$this->companyModel->isOwner($company_id, $user_id)) {
            flash('company_error', 'ليس لديك صلاحية لتعديل هذه الشركة', 'danger');
            redirect(base_url('companies'));
        }

        // الحصول على بيانات الشركة
        $company = $this->companyModel->getById($company_id);

        if (!$company) {
            flash('company_error', 'الشركة غير موجودة', 'danger');
            redirect(base_url('companies'));
        }

        // التحقق من البيانات المدخلة
        $company_name = trim($_POST['company_name'] ?? '');
        $company_name_en = trim($_POST['company_name_en'] ?? '');
        $company_email = trim($_POST['company_email'] ?? '');
        $company_phone = trim($_POST['company_phone'] ?? '');
        $company_address = trim($_POST['company_address'] ?? '');
        $company_website = trim($_POST['company_website'] ?? '');
        $tax_id = trim($_POST['tax_id'] ?? '');
        $remove_logo = isset($_POST['remove_logo']) ? (bool)$_POST['remove_logo'] : false;

        if (empty($company_name)) {
            flash('company_error', 'يرجى إدخال اسم الشركة', 'danger');
            redirect(base_url('companies/' . $company_id . '/edit'));
        }

        if (empty($company_email) || !filter_var($company_email, FILTER_VALIDATE_EMAIL)) {
            flash('company_error', 'يرجى إدخال بريد إلكتروني صحيح للشركة', 'danger');
            redirect(base_url('companies/' . $company_id . '/edit'));
        }

        if (empty($company_phone)) {
            flash('company_error', 'يرجى إدخال رقم هاتف الشركة', 'danger');
            redirect(base_url('companies/' . $company_id . '/edit'));
        }

        // معالجة الصورة إذا تم تحميلها
        $company_logo = $company['CompanyLogo'];

        if ($remove_logo) {
            // إذا تم طلب إزالة الشعار
            if (!empty($company_logo) && file_exists(BASE_PATH . '/' . $company_logo)) {
                unlink(BASE_PATH . '/' . $company_logo);
            }
            $company_logo = null;
        }

        if (isset($_FILES['company_logo']) && $_FILES['company_logo']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = UPLOAD_PATH . '/company_logos/';

            // إنشاء المجلد إذا لم يكن موجودًا
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            $file_name = time() . '_' . basename($_FILES['company_logo']['name']);
            $upload_path = $upload_dir . $file_name;

            // التحقق من نوع الملف
            $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
            $file_type = $_FILES['company_logo']['type'];

            if (!in_array($file_type, $allowed_types)) {
                flash('company_error', 'نوع الملف غير مسموح به. يرجى تحميل صورة (JPG, PNG, GIF)', 'danger');
                redirect(base_url('companies/' . $company_id . '/edit'));
            }

            // التحقق من حجم الملف (5MB كحد أقصى)
            if ($_FILES['company_logo']['size'] > 5 * 1024 * 1024) {
                flash('company_error', 'حجم الملف كبير جدًا. الحد الأقصى هو 5 ميجابايت', 'danger');
                redirect(base_url('companies/' . $company_id . '/edit'));
            }

            if (move_uploaded_file($_FILES['company_logo']['tmp_name'], $upload_path)) {
                // إذا تم تحميل صورة جديدة، قم بحذف الصورة القديمة
                if (!empty($company['CompanyLogo']) && file_exists(BASE_PATH . '/' . $company['CompanyLogo'])) {
                    unlink(BASE_PATH . '/' . $company['CompanyLogo']);
                }

                $company_logo = 'public/uploads/company_logos/' . $file_name;
            } else {
                flash('company_error', 'حدث خطأ أثناء تحميل الصورة. يرجى المحاولة مرة أخرى.', 'danger');
                redirect(base_url('companies/' . $company_id . '/edit'));
            }
        }

        // استخدام حالة الشركة الحالية
        $company_status = $company['CompanyStatus'];

        // معالجة وسائل التواصل الاجتماعي
        $social_media_json = null;
        if (isset($_POST['social_media_json'])) {
            $social_media_json = $_POST['social_media_json'];
        } elseif (isset($_POST['social_media']) && is_array($_POST['social_media'])) {
            $social_media = [];
            foreach ($_POST['social_media'] as $key => $value) {
                if (!empty($value)) {
                    $social_media[$key] = $value;
                }
            }
            if (!empty($social_media)) {
                $social_media_json = json_encode($social_media);
            }
        }

        // إعداد بيانات الشركة للتحديث
        $company_data = [
            'company_name' => $company_name,
            'company_name_en' => $company_name_en,
            'company_email' => $company_email,
            'company_phone' => $company_phone,
            'company_logo' => $company_logo,
            'company_address' => $company_address,
            'company_website' => $company_website,
            'tax_id' => $tax_id,
            'company_status' => $company_status,
            'subscription_status' => $company['subscription_status'] ?? 'Trial', // الحفاظ على حالة الاشتراك الحالية
            // الحقول الجديدة
            'industry_type' => $_POST['industry_type'] ?? null,
            'company_size' => $_POST['company_size'] ?? null,
            'foundation_date' => !empty($_POST['foundation_date']) ? $_POST['foundation_date'] : null,
            'social_media_links' => $social_media_json,
            'notes' => $_POST['notes'] ?? null,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // تحديث بيانات الشركة باستخدام النموذج
        if ($this->companyModel->update($company_id, $company_data)) {
            flash('company_success', 'تم تحديث بيانات الشركة بنجاح', 'success');
            redirect(base_url('companies/' . $company_id));
        } else {
            flash('company_error', 'حدث خطأ أثناء تحديث بيانات الشركة. يرجى المحاولة مرة أخرى.', 'danger');
            redirect(base_url('companies/' . $company_id . '/edit'));
        }
    }

    /**
     * تعطيل شركة
     *
     * @return void
     */
    public function deactivate() {
        $company_id = $this->params['id'] ?? null;

        if (!$company_id) {
            flash('company_error', 'الشركة غير موجودة', 'danger');
            redirect(base_url('companies'));
        }

        // التحقق من أن الطلب هو POST
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect(base_url('companies/' . $company_id));
        }

        // التحقق من CSRF token
        if (!csrf_check($_POST['csrf_token'] ?? '')) {
            flash('company_error', 'خطأ في التحقق من الأمان. يرجى المحاولة مرة أخرى.', 'danger');
            redirect(base_url('companies/' . $company_id));
        }

        // التحقق من وجود الشركة وصلاحية التعديل (المالك فقط)
        $user_id = current_user_id();

        // التحقق من ملكية المستخدم للشركة
        if (!$this->companyModel->isOwner($company_id, $user_id)) {
            flash('company_error', 'ليس لديك صلاحية لتعطيل هذه الشركة', 'danger');
            redirect(base_url('companies'));
        }

        // الحصول على بيانات الشركة
        $company = $this->companyModel->getById($company_id);

        if (!$company) {
            flash('company_error', 'الشركة غير موجودة', 'danger');
            redirect(base_url('companies'));
        }

        // التحقق من تأكيد التعطيل
        if (!isset($_POST['confirm_deactivation']) || $_POST['confirm_deactivation'] !== 'on') {
            flash('company_error', 'يرجى تأكيد رغبتك في تعطيل الشركة', 'danger');
            redirect(base_url('companies/' . $company_id));
        }

        // حفظ سبب التعطيل إذا تم إدخاله
        $deactivation_reason = $_POST['deactivation_reason'] ?? null;

        // تعطيل الشركة باستخدام النموذج
        if ($this->companyModel->deactivate($company_id, $deactivation_reason)) {
            flash('company_success', 'تم تعطيل الشركة بنجاح', 'success');
            redirect(base_url('companies'));
        } else {
            flash('company_error', 'حدث خطأ أثناء تعطيل الشركة. يرجى المحاولة مرة أخرى.', 'danger');
            redirect(base_url('companies/' . $company_id));
        }
    }

    /**
     * تفعيل شركة
     *
     * @return void
     */
    public function activate() {
        $company_id = $this->params['id'] ?? null;

        if (!$company_id) {
            flash('company_error', 'الشركة غير موجودة', 'danger');
            redirect(base_url('companies'));
        }

        // التحقق من أن الطلب هو POST
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect(base_url('companies/' . $company_id));
        }

        // التحقق من CSRF token
        if (!csrf_check($_POST['csrf_token'] ?? '')) {
            flash('company_error', 'خطأ في التحقق من الأمان. يرجى المحاولة مرة أخرى.', 'danger');
            redirect(base_url('companies/' . $company_id));
        }

        // التحقق من وجود الشركة وصلاحية التعديل (المالك فقط)
        $user_id = current_user_id();

        // التحقق من ملكية المستخدم للشركة
        if (!$this->companyModel->isOwner($company_id, $user_id)) {
            flash('company_error', 'ليس لديك صلاحية لتفعيل هذه الشركة', 'danger');
            redirect(base_url('companies'));
        }

        // الحصول على بيانات الشركة
        $company = $this->companyModel->getById($company_id);

        if (!$company) {
            flash('company_error', 'الشركة غير موجودة', 'danger');
            redirect(base_url('companies'));
        }

        // تفعيل الشركة باستخدام النموذج
        if ($this->companyModel->activate($company_id)) {
            flash('company_success', 'تم تفعيل الشركة بنجاح', 'success');
            redirect(base_url('companies'));
        } else {
            flash('company_error', 'حدث خطأ أثناء تفعيل الشركة. يرجى المحاولة مرة أخرى.', 'danger');
            redirect(base_url('companies/' . $company_id));
        }
    }

    /**
     * قبول دعوة للانضمام إلى شركة
     *
     * @return void
     */
    public function acceptInvitation() {
        $company_id = $this->params['id'] ?? null;

        if (!$company_id) {
            flash('company_error', 'الدعوة غير صالحة', 'danger');
            redirect(base_url('companies'));
        }

        $user_id = current_user_id();

        // الحصول على الدعوة باستخدام النموذج
        $invitation = $this->companyModel->getInvitation($company_id, $user_id);

        if (!$invitation) {
            flash('company_error', 'الدعوة غير صالحة أو تم قبولها مسبقًا', 'danger');
            redirect(base_url('companies'));
        }

        // قبول الدعوة باستخدام النموذج
        if ($this->companyModel->acceptInvitation($invitation['id'])) {
            flash('company_success', 'تم قبول الدعوة بنجاح', 'success');
        } else {
            flash('company_error', 'حدث خطأ أثناء قبول الدعوة. يرجى المحاولة مرة أخرى.', 'danger');
        }

        redirect(base_url('companies'));
    }

    /**
     * رفض دعوة للانضمام إلى شركة
     *
     * @return void
     */
    public function rejectInvitation() {
        $company_id = $this->params['id'] ?? null;

        if (!$company_id) {
            flash('company_error', 'الدعوة غير صالحة', 'danger');
            redirect(base_url('companies'));
        }

        $user_id = current_user_id();

        // الحصول على الدعوة باستخدام النموذج
        $invitation = $this->companyModel->getInvitation($company_id, $user_id);

        if (!$invitation) {
            flash('company_error', 'الدعوة غير صالحة أو تم رفضها مسبقًا', 'danger');
            redirect(base_url('companies'));
        }

        // رفض الدعوة باستخدام النموذج
        if ($this->companyModel->rejectInvitation($invitation['id'])) {
            flash('company_success', 'تم رفض الدعوة بنجاح', 'success');
        } else {
            flash('company_error', 'حدث خطأ أثناء رفض الدعوة. يرجى المحاولة مرة أخرى.', 'danger');
        }

        redirect(base_url('companies'));
    }

    /**
     * حذف شركة
     *
     * @return void
     */
    public function delete() {
        $company_id = $this->params['id'] ?? null;

        if (!$company_id) {
            flash('company_error', 'الشركة غير موجودة', 'danger');
            redirect(base_url('companies'));
        }

        // التحقق من أن الطلب هو POST
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect(base_url('companies/' . $company_id));
        }

        // التحقق من CSRF token
        if (!csrf_check($_POST['csrf_token'] ?? '')) {
            flash('company_error', 'خطأ في التحقق من الأمان. يرجى المحاولة مرة أخرى.', 'danger');
            redirect(base_url('companies/' . $company_id));
        }

        // التحقق من وجود الشركة وصلاحية الحذف (المالك فقط)
        $user_id = current_user_id();

        // التحقق من ملكية المستخدم للشركة
        if (!$this->companyModel->isOwner($company_id, $user_id)) {
            flash('company_error', 'ليس لديك صلاحية لحذف هذه الشركة', 'danger');
            redirect(base_url('companies'));
        }

        // التحقق من تأكيد الحذف
        if (!isset($_POST['confirm_delete']) || $_POST['confirm_delete'] !== 'on') {
            flash('company_error', 'يرجى تأكيد رغبتك في حذف الشركة', 'danger');
            redirect(base_url('companies/' . $company_id));
        }

        // حذف الشركة باستخدام النموذج
        if ($this->companyModel->delete($company_id)) {
            // إذا كانت هذه هي الشركة الحالية للمستخدم، قم بإزالتها
            $current_user = current_user();
            if ($current_user['current_company_id'] == $company_id) {
                $this->companyModel->setCurrentCompanyForUser($user_id, null);

                // تحديث بيانات المستخدم في الجلسة
                if (isset($_SESSION['user'])) {
                    $_SESSION['user']['current_company_id'] = null;
                }
            }

            flash('company_success', 'تم حذف الشركة بنجاح', 'success');
        } else {
            flash('company_error', 'حدث خطأ أثناء حذف الشركة. يرجى المحاولة مرة أخرى.', 'danger');
        }

        redirect(base_url('companies'));
    }
}