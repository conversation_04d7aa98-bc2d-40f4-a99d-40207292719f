<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title">
                    <i class="fas fa-building"></i> <?= __('الشركات') ?>
                </h1>
                <a href="<?= base_url('companies/create') ?>" class="btn btn-primary">
                    <i class="fas fa-plus-circle me-2"></i> <?= __('إنشاء شركة جديدة') ?>
                </a>
            </div>
        </div>
    </div>

    <?php
    // استخدام Toastr بدلاً من display_flash
    $success = flash('company_success');
    if ($success) {
        echo '<script>
            document.addEventListener("DOMContentLoaded", function() {
                if (typeof toastr !== "undefined") {
                    toastr.success("' . addslashes($success['message']) . '", "' . __('نجاح') . '");
                }
            });
        </script>';
    }

    $error = flash('company_error');
    if ($error) {
        echo '<script>
            document.addEventListener("DOMContentLoaded", function() {
                if (typeof toastr !== "undefined") {
                    toastr.error("' . addslashes($error['message']) . '", "' . __('خطأ') . '");
                }
            });
        </script>';
    }
    ?>

    <!-- الشركات التي يملكها المستخدم -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-crown me-2"></i> <?= __('الشركات التي أملكها') ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($owned_companies)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> <?= __('لا توجد شركات مملوكة لك حالياً. قم بإنشاء شركة جديدة للبدء.') ?>
                        </div>
                    <?php else: ?>
                        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
                            <?php foreach ($owned_companies as $company): ?>
                                <div class="col">
                                    <div class="card h-100 company-card">
                                        <div class="position-relative">
                                            <div class="company-banner bg-light d-flex align-items-center justify-content-center" style="height: 100px; background-color: #f8f9fa;">
                                                <?php if (!empty($company['CompanyLogo'])): ?>
                                                    <img src="<?= base_url($company['CompanyLogo']) ?>" alt="<?= e($company['CompanyName']) ?>" class="img-fluid" style="max-height: 80px;">
                                                <?php else: ?>
                                                    <i class="fas fa-building fa-3x text-muted"></i>
                                                <?php endif; ?>
                                            </div>

                                            <?php
                                            $status_class = 'bg-success';
                                            $status_text = 'نشطة';

                                            if ($company['ActualStatus'] === 'Trial') {
                                                $status_class = 'bg-info';
                                                $status_text = 'تجريبية';
                                                if ($company['RemainingDays'] <= 3) {
                                                    $status_class = 'bg-warning';
                                                }
                                            } elseif ($company['ActualStatus'] === 'Expired') {
                                                $status_class = 'bg-danger';
                                                $status_text = 'منتهية';
                                            } elseif ($company['ActualStatus'] === 'Inactive') {
                                                $status_class = 'bg-secondary';
                                                $status_text = 'غير نشطة';
                                            }
                                            ?>

                                            <div class="position-absolute top-0 end-0 m-2">
                                                <span class="badge <?= $status_class ?> rounded-pill">
                                                    <?= $status_text ?>
                                                    <?php if ($company['ActualStatus'] === 'Trial' && $company['RemainingDays'] > 0): ?>
                                                        (<?= $company['RemainingDays'] ?> <?= __('يوم') ?>)
                                                    <?php endif; ?>
                                                </span>
                                            </div>
                                        </div>

                                        <div class="card-body">
                                            <h5 class="card-title"><?= e($company['CompanyName']) ?></h5>
                                            <?php if (!empty($company['CompanyNameEN'])): ?>
                                                <p class="card-text text-muted small"><?= e($company['CompanyNameEN']) ?></p>
                                            <?php endif; ?>

                                            <div class="mb-2">
                                                <i class="fas fa-envelope text-muted me-2"></i>
                                                <span class="small"><?= e($company['CompanyEmail']) ?></span>
                                            </div>

                                            <div class="mb-2">
                                                <i class="fas fa-phone text-muted me-2"></i>
                                                <span class="small"><?= e($company['CompanyPhone']) ?></span>
                                            </div>

                                            <?php if (!empty($company['CompanyAddress'])): ?>
                                                <div class="mb-2">
                                                    <i class="fas fa-map-marker-alt text-muted me-2"></i>
                                                    <span class="small"><?= e($company['CompanyAddress']) ?></span>
                                                </div>
                                            <?php endif; ?>

                                            <div class="mt-3">
                                                <span class="badge bg-primary rounded-pill">
                                                    <i class="fas fa-crown me-1"></i> <?= __('مالك') ?>
                                                </span>

                                                <?php if ($company['ActualStatus'] === 'Trial'): ?>
                                                    <div class="mt-2 small">
                                                        <div class="progress" style="height: 6px;">
                                                            <?php
                                                            $trial_percentage = 100 - min(100, max(0, ($company['RemainingDays'] / 14) * 100));
                                                            $progress_class = 'bg-info';

                                                            if ($trial_percentage > 70) {
                                                                $progress_class = 'bg-warning';
                                                            }
                                                            if ($trial_percentage > 90) {
                                                                $progress_class = 'bg-danger';
                                                            }
                                                            ?>
                                                            <div class="progress-bar <?= $progress_class ?>" role="progressbar" style="width: <?= $trial_percentage ?>%"></div>
                                                        </div>
                                                        <div class="d-flex justify-content-between mt-1">
                                                            <span><?= __('الفترة التجريبية') ?></span>
                                                            <span><?= $company['RemainingDays'] ?> / 14 <?= __('يوم') ?></span>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <div class="card-footer bg-transparent">
                                            <div class="d-flex justify-content-between">
                                                <div class="btn-group">
                                                    <a href="<?= base_url('companies/' . $company['CompanyID']) ?>" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye me-1"></i> <?= __('عرض') ?>
                                                    </a>
                                                    <a href="<?= base_url('companies/' . $company['CompanyID'] . '/edit') ?>" class="btn btn-sm btn-outline-secondary">
                                                        <i class="fas fa-edit me-1"></i> <?= __('تعديل') ?>
                                                    </a>
                                                </div>
                                                <div class="btn-group">
                                                    <?php if ($company['ActualStatus'] === 'Inactive'): ?>
                                                    <button type="button" class="btn btn-sm btn-outline-success activate-company" data-bs-toggle="modal" data-bs-target="#activateCompanyModal<?= $company['CompanyID'] ?>">
                                                        <i class="fas fa-check-circle me-1"></i> <?= __('تفعيل') ?>
                                                    </button>
                                                    <?php else: ?>
                                                    <button type="button" class="btn btn-sm btn-outline-danger deactivate-company" data-bs-toggle="modal" data-bs-target="#deactivateCompanyModal<?= $company['CompanyID'] ?>">
                                                        <i class="fas fa-ban me-1"></i> <?= __('تعطيل') ?>
                                                    </button>
                                                    <?php endif; ?>
                                                    <a href="<?= base_url('dashboard?company=' . $company['CompanyID']) ?>" class="btn btn-sm btn-success <?= $company['ActualStatus'] === 'Inactive' ? 'disabled' : '' ?>">
                                                        <i class="fas fa-sign-in-alt me-1"></i> <?= __('دخول') ?>
                                                    </a>
                                                </div>
                                            </div>

                                            <!-- Modal تعطيل الشركة -->
                                            <?php if ($company['ActualStatus'] !== 'Inactive'): ?>
                                            <div class="modal fade" id="deactivateCompanyModal<?= $company['CompanyID'] ?>" tabindex="-1" aria-labelledby="deactivateCompanyModalLabel<?= $company['CompanyID'] ?>" aria-hidden="true">
                                                <div class="modal-dialog modal-dialog-centered">
                                                    <div class="modal-content">
                                                        <div class="modal-header bg-danger text-white">
                                                            <h5 class="modal-title" id="deactivateCompanyModalLabel<?= $company['CompanyID'] ?>">
                                                                <i class="fas fa-exclamation-triangle me-2"></i> <?= __('تأكيد تعطيل الشركة') ?>
                                                            </h5>
                                                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <div class="text-center mb-4">
                                                                <div class="avatar-icon avatar-icon-lg bg-danger-soft text-danger mb-3">
                                                                    <i class="fas fa-ban fa-2x"></i>
                                                                </div>
                                                                <h4><?= __('هل أنت متأكد من رغبتك في تعطيل الشركة؟') ?></h4>
                                                                <p class="text-muted"><?= e($company['CompanyName']) ?></p>
                                                            </div>

                                                            <div class="alert alert-warning">
                                                                <div class="d-flex">
                                                                    <div class="me-3">
                                                                        <i class="fas fa-exclamation-circle fa-2x"></i>
                                                                    </div>
                                                                    <div>
                                                                        <h5 class="alert-heading"><?= __('تنبيه هام') ?></h5>
                                                                        <p class="mb-0"><?= __('سيتم منع جميع المستخدمين من الوصول إلى الشركة حتى يتم تفعيلها مرة أخرى.') ?></p>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <form action="<?= base_url('companies/' . $company['CompanyID'] . '/deactivate') ?>" method="post" id="deactivateCompanyForm<?= $company['CompanyID'] ?>">
                                                                <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">

                                                                <div class="mb-3">
                                                                    <label for="deactivation_reason<?= $company['CompanyID'] ?>" class="form-label"><?= __('سبب التعطيل (اختياري)') ?></label>
                                                                    <textarea class="form-control" id="deactivation_reason<?= $company['CompanyID'] ?>" name="deactivation_reason" rows="3" placeholder="<?= __('يرجى ذكر سبب تعطيل الشركة...') ?>"></textarea>
                                                                </div>

                                                                <div class="form-check mb-3">
                                                                    <input class="form-check-input" type="checkbox" id="confirm_deactivation<?= $company['CompanyID'] ?>" name="confirm_deactivation" required>
                                                                    <label class="form-check-label" for="confirm_deactivation<?= $company['CompanyID'] ?>">
                                                                        <?= __('أؤكد رغبتي في تعطيل الشركة') ?>
                                                                    </label>
                                                                </div>
                                                            </form>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                                                <i class="fas fa-times me-1"></i> <?= __('إلغاء') ?>
                                                            </button>
                                                            <button type="button" class="btn btn-danger confirm-deactivate" data-company-id="<?= $company['CompanyID'] ?>">
                                                                <i class="fas fa-ban me-1"></i> <?= __('تعطيل الشركة') ?>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endif; ?>

                                            <!-- Modal تفعيل الشركة -->
                                            <?php if ($company['ActualStatus'] === 'Inactive'): ?>
                                            <div class="modal fade" id="activateCompanyModal<?= $company['CompanyID'] ?>" tabindex="-1" aria-labelledby="activateCompanyModalLabel<?= $company['CompanyID'] ?>" aria-hidden="true">
                                                <div class="modal-dialog modal-dialog-centered">
                                                    <div class="modal-content">
                                                        <div class="modal-header bg-success text-white">
                                                            <h5 class="modal-title" id="activateCompanyModalLabel<?= $company['CompanyID'] ?>">
                                                                <i class="fas fa-check-circle me-2"></i> <?= __('تأكيد تفعيل الشركة') ?>
                                                            </h5>
                                                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <div class="text-center mb-4">
                                                                <div class="avatar-icon avatar-icon-lg bg-success-soft text-success mb-3">
                                                                    <i class="fas fa-check-circle fa-2x"></i>
                                                                </div>
                                                                <h4><?= __('هل ترغب في تفعيل الشركة؟') ?></h4>
                                                                <p class="text-muted"><?= e($company['CompanyName']) ?></p>
                                                            </div>

                                                            <div class="alert alert-info">
                                                                <div class="d-flex">
                                                                    <div class="me-3">
                                                                        <i class="fas fa-info-circle fa-2x"></i>
                                                                    </div>
                                                                    <div>
                                                                        <h5 class="alert-heading"><?= __('معلومات') ?></h5>
                                                                        <p class="mb-0"><?= __('سيتمكن جميع المستخدمين من الوصول إلى الشركة مرة أخرى بعد التفعيل.') ?></p>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <form action="<?= base_url('companies/' . $company['CompanyID'] . '/activate') ?>" method="post" id="activateCompanyForm<?= $company['CompanyID'] ?>">
                                                                <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
                                                            </form>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                                                <i class="fas fa-times me-1"></i> <?= __('إلغاء') ?>
                                                            </button>
                                                            <button type="button" class="btn btn-success confirm-activate" data-company-id="<?= $company['CompanyID'] ?>">
                                                                <i class="fas fa-check-circle me-1"></i> <?= __('تفعيل الشركة') ?>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- الشركات التي تمت دعوة المستخدم إليها -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-friends me-2"></i> <?= __('الشركات المنضم إليها') ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($joined_companies)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> <?= __('لم تنضم إلى أي شركات حتى الآن.') ?>
                        </div>
                    <?php else: ?>
                        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
                            <?php foreach ($joined_companies as $company): ?>
                                <div class="col">
                                    <div class="card h-100 company-card">
                                        <div class="position-relative">
                                            <div class="company-banner bg-light d-flex align-items-center justify-content-center" style="height: 100px; background-color: #f8f9fa;">
                                                <?php if (!empty($company['CompanyLogo'])): ?>
                                                    <img src="<?= base_url($company['CompanyLogo']) ?>" alt="<?= e($company['CompanyName']) ?>" class="img-fluid" style="max-height: 80px;">
                                                <?php else: ?>
                                                    <i class="fas fa-building fa-3x text-muted"></i>
                                                <?php endif; ?>
                                            </div>

                                            <?php
                                            $status_class = 'bg-success';
                                            $status_text = 'نشطة';

                                            if ($company['CompanyStatus'] === 'Trial') {
                                                $status_class = 'bg-info';
                                                $status_text = 'تجريبية';
                                            } elseif ($company['CompanyStatus'] === 'Expired') {
                                                $status_class = 'bg-danger';
                                                $status_text = 'منتهية';
                                            } elseif ($company['CompanyStatus'] === 'Inactive') {
                                                $status_class = 'bg-secondary';
                                                $status_text = 'غير نشطة';
                                            }

                                            $invitation_status_class = 'bg-success';
                                            $invitation_status_text = $company['status_ar'];

                                            if ($company['status'] === 'pending') {
                                                $invitation_status_class = 'bg-warning';
                                            } elseif ($company['status'] === 'rejected') {
                                                $invitation_status_class = 'bg-danger';
                                            }
                                            ?>

                                            <div class="position-absolute top-0 end-0 m-2">
                                                <span class="badge <?= $status_class ?> rounded-pill">
                                                    <?= $status_text ?>
                                                </span>
                                            </div>

                                            <div class="position-absolute top-0 start-0 m-2">
                                                <span class="badge <?= $invitation_status_class ?> rounded-pill">
                                                    <?= $invitation_status_text ?>
                                                </span>
                                            </div>
                                        </div>

                                        <div class="card-body">
                                            <h5 class="card-title"><?= e($company['CompanyName']) ?></h5>
                                            <?php if (!empty($company['CompanyNameEN'])): ?>
                                                <p class="card-text text-muted small"><?= e($company['CompanyNameEN']) ?></p>
                                            <?php endif; ?>

                                            <div class="mb-2">
                                                <i class="fas fa-envelope text-muted me-2"></i>
                                                <span class="small"><?= e($company['CompanyEmail']) ?></span>
                                            </div>

                                            <div class="mb-2">
                                                <i class="fas fa-phone text-muted me-2"></i>
                                                <span class="small"><?= e($company['CompanyPhone']) ?></span>
                                            </div>

                                            <?php if (!empty($company['PositionNameAR'])): ?>
                                                <div class="mb-2">
                                                    <i class="fas fa-id-badge text-muted me-2"></i>
                                                    <span class="small"><?= e($company['PositionNameAR']) ?></span>
                                                </div>
                                            <?php endif; ?>

                                            <div class="mt-3">
                                                <span class="badge bg-secondary rounded-pill">
                                                    <i class="fas fa-user me-1"></i> <?= __('عضو') ?>
                                                </span>
                                            </div>
                                        </div>

                                        <div class="card-footer bg-transparent">
                                            <div class="d-flex justify-content-between">
                                                <?php if ($company['status'] === 'pending'): ?>
                                                    <button class="btn btn-sm btn-success accept-invitation" data-company-id="<?= $company['CompanyID'] ?>">
                                                        <i class="fas fa-check me-1"></i> <?= __('قبول') ?>
                                                    </button>
                                                    <button class="btn btn-sm btn-danger reject-invitation" data-company-id="<?= $company['CompanyID'] ?>">
                                                        <i class="fas fa-times me-1"></i> <?= __('رفض') ?>
                                                    </button>
                                                <?php elseif ($company['status'] === 'accepted'): ?>
                                                    <a href="<?= base_url('companies/' . $company['CompanyID']) ?>" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye me-1"></i> <?= __('عرض') ?>
                                                    </a>
                                                    <a href="<?= base_url('dashboard?company=' . $company['CompanyID']) ?>" class="btn btn-sm btn-success">
                                                        <i class="fas fa-sign-in-alt me-1"></i> <?= __('دخول') ?>
                                                    </a>
                                                <?php else: ?>
                                                    <span class="text-muted small"><?= __('تم رفض الدعوة') ?></span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* تنسيق أيقونات النوافذ المنبثقة */
.avatar-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    border-radius: 50%;
}

.avatar-icon-lg {
    width: 100px;
    height: 100px;
}

.bg-danger-soft {
    background-color: rgba(220, 53, 69, 0.1);
}

.bg-success-soft {
    background-color: rgba(40, 167, 69, 0.1);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // قبول الدعوة
    const acceptButtons = document.querySelectorAll('.accept-invitation');
    acceptButtons.forEach(button => {
        button.addEventListener('click', function() {
            const companyId = this.getAttribute('data-company-id');
            if (confirm('هل أنت متأكد من قبول الدعوة للانضمام إلى هذه الشركة؟')) {
                window.location.href = `<?= base_url('companies/accept-invitation/') ?>${companyId}`;
            }
        });
    });

    // رفض الدعوة
    const rejectButtons = document.querySelectorAll('.reject-invitation');
    rejectButtons.forEach(button => {
        button.addEventListener('click', function() {
            const companyId = this.getAttribute('data-company-id');
            if (confirm('هل أنت متأكد من رفض الدعوة للانضمام إلى هذه الشركة؟')) {
                window.location.href = `<?= base_url('companies/reject-invitation/') ?>${companyId}`;
            }
        });
    });

    // تأكيد تعطيل الشركة
    const confirmDeactivateButtons = document.querySelectorAll('.confirm-deactivate');
    confirmDeactivateButtons.forEach(button => {
        button.addEventListener('click', function() {
            const companyId = this.getAttribute('data-company-id');
            const confirmCheckbox = document.getElementById(`confirm_deactivation${companyId}`);

            if (confirmCheckbox.checked) {
                document.getElementById(`deactivateCompanyForm${companyId}`).submit();
            } else {
                alert('يرجى تأكيد رغبتك في تعطيل الشركة');
            }
        });
    });

    // تأكيد تفعيل الشركة
    const confirmActivateButtons = document.querySelectorAll('.confirm-activate');
    confirmActivateButtons.forEach(button => {
        button.addEventListener('click', function() {
            const companyId = this.getAttribute('data-company-id');
            document.getElementById(`activateCompanyForm${companyId}`).submit();
        });
    });
});
</script>
