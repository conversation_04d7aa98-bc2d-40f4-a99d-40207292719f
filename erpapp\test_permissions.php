<?php
/**
 * صفحة اختبار نظام الصلاحيات الجديد
 * للتأكد من أن النظام يعمل بشكل صحيح
 */

// تحميل النظام
require_once 'loader.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الصلاحيات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-4">
    <h1 class="text-center mb-4">
        <i class="fas fa-shield-alt"></i> اختبار نظام الصلاحيات الديناميكي
    </h1>

    <!-- معلومات المستخدم الحالي -->
    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5><i class="fas fa-user"></i> معلومات المستخدم الحالي</h5>
        </div>
        <div class="card-body">
            <?php
            $user = current_user();
            if ($user):
            ?>
            <div class="row">
                <div class="col-md-6">
                    <strong>اسم المستخدم:</strong> <?= htmlspecialchars($user['username'] ?? 'غير محدد') ?><br>
                    <strong>معرف المستخدم:</strong> <?= $user['UserID'] ?? 'غير محدد' ?><br>
                    <strong>معرف الشركة الحالية:</strong> <?= $user['current_company_id'] ?? 'غير محدد' ?><br>
                    <strong>مالك الشركة:</strong>
                    <span class="badge bg-<?= isCompanyOwner() ? 'success' : 'secondary' ?>">
                        <?= isCompanyOwner() ? 'نعم' : 'لا' ?>
                    </span>
                </div>
                <div class="col-md-6">
                    <?php
                    $position = getUserPosition();
                    $companyStatus = getUserCompanyStatus();
                    ?>
                    <?php if ($position): ?>
                    <strong>المنصب:</strong> <?= htmlspecialchars($position['PositionNameAR']) ?><br>
                    <strong>تاريخ الانضمام:</strong> <?= date('Y-m-d', strtotime($position['join_date'])) ?><br>
                    <?php endif; ?>

                    <?php if ($companyStatus): ?>
                    <strong>حالة العضوية:</strong>
                    <span class="badge bg-<?= $companyStatus['status'] === 'accepted' ? 'success' : 'warning' ?>">
                        <?= $companyStatus['status_ar'] ?? $companyStatus['status'] ?>
                    </span><br>
                    <strong>حالة المستخدم:</strong>
                    <span class="badge bg-<?= $companyStatus['user_status'] === 'active' ? 'success' : 'danger' ?>">
                        <?= $companyStatus['user_status'] === 'active' ? 'نشط' : 'غير نشط' ?>
                    </span>
                    <?php endif; ?>
                </div>
            </div>
            <?php else: ?>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i> لم يتم تسجيل الدخول
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- البرامج المتاحة للمستخدم -->
    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5><i class="fas fa-apps"></i> البرامج المتاحة للمستخدم</h5>
        </div>
        <div class="card-body">
            <?php
            $availablePrograms = getAvailablePrograms();
            if (!empty($availablePrograms)):
            ?>
            <div class="row">
                <?php foreach ($availablePrograms as $program): ?>
                <div class="col-md-4 mb-3">
                    <div class="card border-success">
                        <div class="card-body text-center">
                            <i class="<?= $program['icon_name'] ?> fa-2x text-success mb-2"></i>
                            <h6><?= htmlspecialchars($program['name_ar']) ?></h6>
                            <small class="text-muted"><?= htmlspecialchars($program['name_en']) ?></small>
                            <?php if (!empty($program['page_url'])): ?>
                            <br><br>
                            <a href="<?= base_url($program['page_url']) ?>" class="btn btn-sm btn-success">
                                <i class="fas fa-external-link-alt"></i> دخول
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php else: ?>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i> لا توجد برامج متاحة لك حالياً
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- معلومات إضافية -->
    <div class="card mb-4">
        <div class="card-header bg-warning text-dark">
            <h5><i class="fas fa-info-circle"></i> معلومات إضافية</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <h6>حالة النشاط:</h6>
                    <span class="badge bg-<?= isUserActiveInCompany() ? 'success' : 'danger' ?> fs-6">
                        <?= isUserActiveInCompany() ? 'مستخدم نشط' : 'مستخدم غير نشط' ?>
                    </span>
                </div>
                <div class="col-md-4">
                    <h6>نوع المستخدم:</h6>
                    <?php if (isCompanyOwner()): ?>
                    <span class="badge bg-gold fs-6" style="background-color: #ffd700; color: #000;">
                        <i class="fas fa-crown"></i> مالك الشركة
                    </span>
                    <?php elseif ($position): ?>
                    <span class="badge bg-info fs-6">
                        <i class="fas fa-user-tie"></i> <?= htmlspecialchars($position['PositionNameAR']) ?>
                    </span>
                    <?php else: ?>
                    <span class="badge bg-secondary fs-6">
                        <i class="fas fa-user"></i> بدون منصب
                    </span>
                    <?php endif; ?>
                </div>
                <div class="col-md-4">
                    <h6>مستوى الصلاحيات:</h6>
                    <?php if (isCompanyOwner()): ?>
                    <span class="badge bg-success fs-6">
                        <i class="fas fa-unlock"></i> صلاحيات كاملة
                    </span>
                    <?php elseif (isUserActiveInCompany()): ?>
                    <span class="badge bg-primary fs-6">
                        <i class="fas fa-key"></i> صلاحيات محدودة
                    </span>
                    <?php else: ?>
                    <span class="badge bg-danger fs-6">
                        <i class="fas fa-lock"></i> بدون صلاحيات
                    </span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- اختبار الصلاحيات -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5><i class="fas fa-check-circle"></i> اختبار الصلاحيات</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <!-- صلاحيات المخزون -->
                <div class="col-md-6">
                    <h6 class="text-primary">صلاحيات المخزون:</h6>
                    <table class="table table-sm">
                        <tr>
                            <td>عرض المخزون</td>
                            <td>
                                <span class="badge bg-<?= canView('inventory_management') ? 'success' : 'danger' ?>">
                                    <?= canView('inventory_management') ? 'متاح' : 'غير متاح' ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td>إضافة منتجات</td>
                            <td>
                                <span class="badge bg-<?= canCreate('inventory_management') ? 'success' : 'danger' ?>">
                                    <?= canCreate('inventory_management') ? 'متاح' : 'غير متاح' ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td>تعديل منتجات</td>
                            <td>
                                <span class="badge bg-<?= canEdit('inventory_management') ? 'success' : 'danger' ?>">
                                    <?= canEdit('inventory_management') ? 'متاح' : 'غير متاح' ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td>حذف منتجات</td>
                            <td>
                                <span class="badge bg-<?= canDelete('inventory_management') ? 'success' : 'danger' ?>">
                                    <?= canDelete('inventory_management') ? 'متاح' : 'غير متاح' ?>
                                </span>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- صلاحيات أخرى -->
                <div class="col-md-6">
                    <h6 class="text-primary">صلاحيات أخرى:</h6>
                    <table class="table table-sm">
                        <tr>
                            <td>إدارة الشركات</td>
                            <td>
                                <span class="badge bg-<?= canAccessProgram('company_management') ? 'success' : 'danger' ?>">
                                    <?= canAccessProgram('company_management') ? 'متاح' : 'غير متاح' ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td>إدارة المستخدمين</td>
                            <td>
                                <span class="badge bg-<?= canAccessProgram('user_management') ? 'success' : 'danger' ?>">
                                    <?= canAccessProgram('user_management') ? 'متاح' : 'غير متاح' ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td>المحاسبة</td>
                            <td>
                                <span class="badge bg-<?= canAccessProgram('accounting') ? 'success' : 'danger' ?>">
                                    <?= canAccessProgram('accounting') ? 'متاح' : 'غير متاح' ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td>التقارير</td>
                            <td>
                                <span class="badge bg-<?= canAccessProgram('reports') ? 'success' : 'danger' ?>">
                                    <?= canAccessProgram('reports') ? 'متاح' : 'غير متاح' ?>
                                </span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- اختبار الصلاحيات المخصصة -->
    <div class="card mb-4">
        <div class="card-header bg-warning text-dark">
            <h5><i class="fas fa-cog"></i> الصلاحيات المخصصة</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <?php if (hasCustomPermission('inventory_management', 'export_data')): ?>
                        <button class="btn btn-info">
                            <i class="fas fa-download"></i><br>تصدير البيانات
                        </button>
                        <?php else: ?>
                        <button class="btn btn-secondary" disabled>
                            <i class="fas fa-download"></i><br>تصدير البيانات
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <?php if (hasCustomPermission('inventory_management', 'import_data')): ?>
                        <button class="btn btn-warning">
                            <i class="fas fa-upload"></i><br>استيراد البيانات
                        </button>
                        <?php else: ?>
                        <button class="btn btn-secondary" disabled>
                            <i class="fas fa-upload"></i><br>استيراد البيانات
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <?php if (hasCustomPermission('inventory_management', 'bulk_operations')): ?>
                        <button class="btn btn-dark">
                            <i class="fas fa-tasks"></i><br>عمليات مجمعة
                        </button>
                        <?php else: ?>
                        <button class="btn btn-secondary" disabled>
                            <i class="fas fa-tasks"></i><br>عمليات مجمعة
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <?php if (hasCustomPermission('inventory_management', 'advanced_reports')): ?>
                        <button class="btn btn-success">
                            <i class="fas fa-chart-line"></i><br>تقارير متقدمة
                        </button>
                        <?php else: ?>
                        <button class="btn btn-secondary" disabled>
                            <i class="fas fa-chart-line"></i><br>تقارير متقدمة
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- عرض المسارات المحملة (للتطوير) -->
    <?php if (defined('DEBUG_MODE') && DEBUG_MODE): ?>
    <div class="card mb-4">
        <div class="card-header bg-dark text-white">
            <h5><i class="fas fa-code"></i> المسارات المحملة (وضع التطوير)</h5>
        </div>
        <div class="card-body">
            <?php debugRoutes(); ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- اختبار الحماية -->
    <div class="card mb-4">
        <div class="card-header bg-danger text-white">
            <h5><i class="fas fa-shield-alt"></i> اختبار الحماية</h5>
        </div>
        <div class="card-body">
            <p>جرب الوصول إلى هذه الروابط لاختبار الحماية:</p>
            <div class="row">
                <div class="col-md-6">
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="<?= base_url('inventory/products/create') ?>" target="_blank">
                                إنشاء منتج جديد
                            </a>
                        </li>
                        <li class="list-group-item">
                            <a href="<?= base_url('companies/create') ?>" target="_blank">
                                إنشاء شركة جديدة
                            </a>
                        </li>
                        <li class="list-group-item">
                            <a href="<?= base_url('users/create') ?>" target="_blank">
                                إنشاء مستخدم جديد
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="<?= base_url('accounting/invoices') ?>" target="_blank">
                                عرض الفواتير
                            </a>
                        </li>
                        <li class="list-group-item">
                            <a href="<?= base_url('hr/employees') ?>" target="_blank">
                                عرض الموظفين
                            </a>
                        </li>
                        <li class="list-group-item">
                            <a href="<?= base_url('settings/permissions') ?>" target="_blank">
                                إعدادات الصلاحيات
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
