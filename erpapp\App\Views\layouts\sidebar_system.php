<?php
/**
 * سايدبار النظام الأساسي
 * يظهر في صفحات النظام (companies, users, settings, etc.)
 */
?>

<!-- Sidebar -->
<aside class="sidebar <?= is_logged_in() && current_user()['sidebar_mode'] == 'hide' ? 'collapsed' : '' ?>" id="sidebar">
    <div class="sidebar-header">
        <a href="<?= base_url('dashboard') ?>" class="sidebar-brand">
            <i class="fas fa-cubes"></i>
            <span class="sidebar-brand-text"><?= APP_NAME ?></span>
        </a>
        <button class="sidebar-toggle" id="sidebarToggle">
            <i class="fas fa-bars"></i>
        </button>
        <!-- زر إغلاق السايدبار على الأجهزة المحمولة -->
        <button class="mobile-sidebar-close" id="mobileSidebarClose">
            <i class="fas fa-times"></i>
        </button>
    </div>

    <ul class="sidebar-menu">
        <li class="sidebar-menu-item">
            <a href="<?= base_url('dashboard') ?>" class="sidebar-menu-link <?= strpos($_SERVER['REQUEST_URI'], '/dashboard') !== false ? 'active' : '' ?>" data-title="<?= __('لوحة التحكم') ?>">
                <i class="fas fa-tachometer-alt sidebar-menu-icon"></i>
                <span class="sidebar-menu-text"><?= __('لوحة التحكم') ?></span>
            </a>
        </li>

        <li class="sidebar-menu-item">
            <a href="<?= base_url('companies') ?>" class="sidebar-menu-link <?= strpos($_SERVER['REQUEST_URI'], '/companies') !== false ? 'active' : '' ?>" data-title="<?= __('الشركات') ?>">
                <i class="fas fa-building sidebar-menu-icon"></i>
                <span class="sidebar-menu-text"><?= __('الشركات') ?></span>
            </a>
        </li>

        <li class="sidebar-menu-item">
            <a href="<?= base_url('programs') ?>" class="sidebar-menu-link <?= strpos($_SERVER['REQUEST_URI'], '/programs') !== false ? 'active' : '' ?>" data-title="<?= __('البرامج') ?>">
                <i class="fas fa-puzzle-piece sidebar-menu-icon"></i>
                <span class="sidebar-menu-text"><?= __('البرامج') ?></span>
            </a>
        </li>

        <li class="sidebar-menu-item">
            <a href="#" class="sidebar-menu-link <?= strpos($_SERVER['REQUEST_URI'], '/subscriptions') !== false ? 'active' : '' ?>" data-bs-toggle="collapse" data-bs-target="#subscriptionsSubmenu" aria-expanded="<?= strpos($_SERVER['REQUEST_URI'], '/subscriptions') !== false ? 'true' : 'false' ?>" data-title="<?= __('الاشتراكات') ?>">
                <i class="fas fa-credit-card sidebar-menu-icon"></i>
                <span class="sidebar-menu-text"><?= __('الاشتراكات') ?></span>
                <i class="fas fa-angle-down sidebar-dropdown-icon"></i>
            </a>
            <ul class="collapse <?= strpos($_SERVER['REQUEST_URI'], '/subscriptions') !== false ? 'show' : '' ?>" id="subscriptionsSubmenu" style="list-style: none; padding-left: 3rem;">
                <li>
                    <a href="<?= base_url('subscriptions') ?>" class="sidebar-menu-link <?= $_SERVER['REQUEST_URI'] === '/erpapp/subscriptions' ? 'active' : '' ?>" style="font-size: 0.9rem; padding: 0.5rem 1rem;" data-title="<?= __('إدارة الاشتراكات') ?>">
                        <i class="fas fa-list-alt sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text"><?= __('إدارة الاشتراكات') ?></span>
                    </a>
                </li>
                <li>
                    <a href="<?= base_url('subscriptions/plans') ?>" class="sidebar-menu-link <?= strpos($_SERVER['REQUEST_URI'], '/subscriptions/plans') !== false && !strpos($_SERVER['REQUEST_URI'], '/compare') ? 'active' : '' ?>" style="font-size: 0.9rem; padding: 0.5rem 1rem;" data-title="<?= __('خطط الاشتراك') ?>">
                        <i class="fas fa-tags sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text"><?= __('خطط الاشتراك') ?></span>
                    </a>
                </li>
                <li>
                    <a href="<?= base_url('subscriptions/plans/compare') ?>" class="sidebar-menu-link <?= strpos($_SERVER['REQUEST_URI'], '/subscriptions/plans/compare') !== false ? 'active' : '' ?>" style="font-size: 0.9rem; padding: 0.5rem 1rem;" data-title="<?= __('مقارنة الخطط') ?>">
                        <i class="fas fa-balance-scale sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text"><?= __('مقارنة الخطط') ?></span>
                    </a>
                </li>
            </ul>
        </li>

        <div class="sidebar-divider"></div>

        <li class="sidebar-menu-item">
            <a href="<?= base_url('chat') ?>" class="sidebar-menu-link <?= strpos($_SERVER['REQUEST_URI'], '/chat') !== false ? 'active' : '' ?>" data-title="<?= __('الدردشة') ?>">
                <i class="fas fa-comments sidebar-menu-icon"></i>
                <span class="sidebar-menu-text"><?= __('الدردشة') ?></span>
            </a>
        </li>

        <li class="sidebar-menu-item">
            <a href="<?= base_url('notifications') ?>" class="sidebar-menu-link <?= strpos($_SERVER['REQUEST_URI'], '/notifications') !== false ? 'active' : '' ?>" data-title="<?= __('الإشعارات') ?>">
                <i class="fas fa-bell sidebar-menu-icon"></i>
                <span class="sidebar-menu-text"><?= __('الإشعارات') ?></span>
            </a>
        </li>

        <div class="sidebar-divider"></div>

        <li class="sidebar-menu-item">
            <a href="<?= base_url('settings') ?>" class="sidebar-menu-link <?= strpos($_SERVER['REQUEST_URI'], '/settings') !== false ? 'active' : '' ?>" data-title="<?= __('الإعدادات') ?>">
                <i class="fas fa-cog sidebar-menu-icon"></i>
                <span class="sidebar-menu-text"><?= __('الإعدادات') ?></span>
            </a>
        </li>

        <li class="sidebar-menu-item">
            <a href="<?= base_url('logout') ?>" class="sidebar-menu-link" data-title="<?= __('تسجيل الخروج') ?>">
                <i class="fas fa-sign-out-alt sidebar-menu-icon"></i>
                <span class="sidebar-menu-text"><?= __('تسجيل الخروج') ?></span>
            </a>
        </li>
    </ul>
</aside>
