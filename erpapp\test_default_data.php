<?php
/**
 * اختبار البيانات الافتراضية المدخلة
 * مالك الشركة: 32
 * الشركة: 4
 * المستخدم المضاف: 35
 */

// تحميل النظام
require_once 'loader.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار البيانات الافتراضية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-4">
    <h1 class="text-center mb-4">
        <i class="fas fa-database text-primary"></i> اختبار البيانات الافتراضية
    </h1>

    <?php
    global $db;

    // معلومات الاختبار
    $companyId = 4;
    $ownerId = 32;
    $userId = 35;
    ?>

    <!-- معلومات أساسية -->
    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5><i class="fas fa-info-circle"></i> معلومات الاختبار</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <strong>الشركة:</strong> <?= $companyId ?><br>
                    <strong>مالك الشركة:</strong> <?= $ownerId ?><br>
                    <strong>المستخدم المضاف:</strong> <?= $userId ?>
                </div>
                <div class="col-md-8">
                    <?php
                    // التحقق من وجود البيانات
                    $stmt = $db->prepare("SELECT CompanyName FROM companies WHERE CompanyID = ?");
                    $stmt->execute([$companyId]);
                    $company = $stmt->fetch();

                    $stmt = $db->prepare("SELECT UserName FROM users WHERE UserID = ?");
                    $stmt->execute([$ownerId]);
                    $owner = $stmt->fetch();

                    $stmt = $db->prepare("SELECT UserName FROM users WHERE UserID = ?");
                    $stmt->execute([$userId]);
                    $user = $stmt->fetch();
                    ?>
                    <strong>اسم الشركة:</strong> <?= $company ? htmlspecialchars($company['CompanyName']) : 'غير موجودة' ?><br>
                    <strong>اسم المالك:</strong> <?= $owner ? htmlspecialchars($owner['UserName']) : 'غير موجود' ?><br>
                    <strong>اسم المستخدم:</strong> <?= $user ? htmlspecialchars($user['UserName']) : 'غير موجود' ?>
                </div>
            </div>
        </div>
    </div>

    <!-- الوحدات المتاحة في النظام -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5><i class="fas fa-cubes"></i> الوحدات المتاحة في النظام</h5>
        </div>
        <div class="card-body">
            <?php
            $stmt = $db->prepare("SELECT * FROM system_modules ORDER BY display_order");
            $stmt->execute();
            $modules = $stmt->fetchAll();
            ?>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>كود الوحدة</th>
                            <th>الاسم العربي</th>
                            <th>الاسم الإنجليزي</th>
                            <th>النوع</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($modules as $module): ?>
                        <tr>
                            <td><?= $module['program_id'] ?></td>
                            <td><code><?= htmlspecialchars($module['program_code']) ?></code></td>
                            <td><?= htmlspecialchars($module['name_ar']) ?></td>
                            <td><?= htmlspecialchars($module['name_en']) ?></td>
                            <td><span class="badge bg-info"><?= $module['module_type'] ?? 'addon' ?></span></td>
                            <td>
                                <span class="badge bg-<?= $module['is_active'] ? 'success' : 'danger' ?>">
                                    <?= $module['is_active'] ? 'نشط' : 'غير نشط' ?>
                                </span>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- الوحدات المنزلة للشركة -->
    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5><i class="fas fa-download"></i> الوحدات المنزلة للشركة <?= $companyId ?></h5>
        </div>
        <div class="card-body">
            <?php
            $stmt = $db->prepare("
                SELECT cip.*, sm.name_ar, sm.name_en, sm.program_code, sm.base_url
                FROM company_installed_programs cip
                JOIN system_modules sm ON sm.program_id = cip.program_id
                WHERE cip.company_id = ?
                ORDER BY cip.installation_date
            ");
            $stmt->execute([$companyId]);
            $installedModules = $stmt->fetchAll();
            ?>
            <?php if (empty($installedModules)): ?>
            <div class="alert alert-warning">لا توجد وحدات منزلة لهذه الشركة</div>
            <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>الوحدة</th>
                            <th>تاريخ التنزيل</th>
                            <th>منزل بواسطة</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($installedModules as $module): ?>
                        <tr>
                            <td>
                                <strong><?= htmlspecialchars($module['name_ar']) ?></strong><br>
                                <small class="text-muted"><?= htmlspecialchars($module['program_code']) ?></small>
                            </td>
                            <td><?= date('Y-m-d H:i', strtotime($module['installation_date'])) ?></td>
                            <td><?= $module['installed_by'] ?></td>
                            <td>
                                <span class="badge bg-<?= $module['is_active'] ? 'success' : 'danger' ?>">
                                    <?= $module['is_active'] ? 'نشط' : 'غير نشط' ?>
                                </span>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- البرامج الفرعية -->
    <div class="card mb-4">
        <div class="card-header bg-warning text-dark">
            <h5><i class="fas fa-list"></i> البرامج الفرعية للشركة <?= $companyId ?></h5>
        </div>
        <div class="card-body">
            <?php
            $stmt = $db->prepare("
                SELECT cap.*
                FROM company_all_programs cap
                WHERE cap.company_id = ?
                ORDER BY cap.program_type DESC, cap.display_order
            ");
            $stmt->execute([$companyId]);
            $programs = $stmt->fetchAll();
            ?>
            <?php if (empty($programs)): ?>
            <div class="alert alert-warning">لا توجد برامج فرعية لهذه الشركة</div>
            <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>البرنامج</th>
                            <th>النوع</th>
                            <th>المسار</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($programs as $program): ?>
                        <tr class="<?= $program['program_type'] === 'Main' ? 'table-primary' : '' ?>">
                            <td>
                                <?php if ($program['program_type'] === 'Sub'): ?>
                                <span class="ms-3">└─</span>
                                <?php endif; ?>
                                <strong><?= htmlspecialchars($program['name_ar']) ?></strong><br>
                                <small class="text-muted"><?= htmlspecialchars($program['name_en']) ?></small>
                            </td>
                            <td>
                                <span class="badge bg-<?= $program['program_type'] === 'Main' ? 'primary' : 'secondary' ?>">
                                    <?= $program['program_type'] === 'Main' ? 'رئيسي' : 'فرعي' ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($program['page_url']): ?>
                                <code><?= htmlspecialchars($program['page_url']) ?></code>
                                <?php else: ?>
                                <span class="text-muted">غير محدد</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-<?= $program['status_en'] === 'Active' ? 'success' : 'danger' ?>">
                                    <?= $program['status_ar'] ?>
                                </span>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- المناصب والمستخدمين -->
    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-secondary text-white">
                    <h5><i class="fas fa-user-tag"></i> المناصب</h5>
                </div>
                <div class="card-body">
                    <?php
                    $stmt = $db->prepare("SELECT * FROM positions WHERE CompanyID = ? ORDER BY PositionID");
                    $stmt->execute([$companyId]);
                    $positions = $stmt->fetchAll();
                    ?>
                    <?php if (empty($positions)): ?>
                    <div class="alert alert-warning">لا توجد مناصب</div>
                    <?php else: ?>
                    <div class="list-group">
                        <?php foreach ($positions as $position): ?>
                        <div class="list-group-item">
                            <strong><?= htmlspecialchars($position['PositionNameAR']) ?></strong><br>
                            <small class="text-muted">ID: <?= $position['PositionID'] ?></small>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-dark text-white">
                    <h5><i class="fas fa-users"></i> مستخدمي الشركة</h5>
                </div>
                <div class="card-body">
                    <?php
                    $stmt = $db->prepare("
                        SELECT cu.*, u.UserName, p.PositionNameAR
                        FROM company_users cu
                        JOIN users u ON u.UserID = cu.user_id
                        LEFT JOIN positions p ON p.PositionID = cu.position_id
                        WHERE cu.company_id = ?
                        ORDER BY cu.join_date
                    ");
                    $stmt->execute([$companyId]);
                    $companyUsers = $stmt->fetchAll();
                    ?>
                    <?php if (empty($companyUsers)): ?>
                    <div class="alert alert-warning">لا يوجد مستخدمين</div>
                    <?php else: ?>
                    <div class="list-group">
                        <?php foreach ($companyUsers as $companyUser): ?>
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong><?= htmlspecialchars($companyUser['UserName']) ?></strong><br>
                                    <small class="text-muted">
                                        <?= htmlspecialchars($companyUser['PositionNameAR'] ?: 'بدون منصب') ?>
                                    </small>
                                </div>
                                <div>
                                    <span class="badge bg-<?= $companyUser['status'] === 'accepted' ? 'success' : 'warning' ?>">
                                        <?= $companyUser['status_ar'] ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات الصلاحيات -->
    <div class="card mb-4">
        <div class="card-header bg-danger text-white">
            <h5><i class="fas fa-key"></i> إحصائيات الصلاحيات</h5>
        </div>
        <div class="card-body">
            <?php
            $stmt = $db->prepare("
                SELECT
                    p.PositionNameAR,
                    COUNT(perm.PermissionID) as total_permissions,
                    SUM(perm.CanView) as can_view,
                    SUM(perm.CanCreate) as can_create,
                    SUM(perm.CanEdit) as can_edit,
                    SUM(perm.CanDelete) as can_delete
                FROM positions p
                LEFT JOIN permissions perm ON perm.PositionID = p.PositionID
                WHERE p.CompanyID = ?
                GROUP BY p.PositionID, p.PositionNameAR
                ORDER BY p.PositionID
            ");
            $stmt->execute([$companyId]);
            $permissionStats = $stmt->fetchAll();
            ?>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>المنصب</th>
                            <th>إجمالي الصلاحيات</th>
                            <th>عرض</th>
                            <th>إنشاء</th>
                            <th>تعديل</th>
                            <th>حذف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($permissionStats as $stat): ?>
                        <tr>
                            <td><?= htmlspecialchars($stat['PositionNameAR']) ?></td>
                            <td><span class="badge bg-primary"><?= $stat['total_permissions'] ?></span></td>
                            <td><span class="badge bg-success"><?= $stat['can_view'] ?></span></td>
                            <td><span class="badge bg-info"><?= $stat['can_create'] ?></span></td>
                            <td><span class="badge bg-warning"><?= $stat['can_edit'] ?></span></td>
                            <td><span class="badge bg-danger"><?= $stat['can_delete'] ?></span></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- روابط اختبار -->
    <div class="card">
        <div class="card-header bg-info text-white">
            <h5><i class="fas fa-link"></i> روابط اختبار</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>اختبار الوحدات:</h6>
                    <div class="d-grid gap-2">
                        <a href="<?= base_url('inventory') ?>" class="btn btn-outline-primary" target="_blank">
                            <i class="fas fa-boxes"></i> وحدة المخزون
                        </a>
                        <a href="<?= base_url('purchases') ?>" class="btn btn-outline-success" target="_blank">
                            <i class="fas fa-shopping-cart"></i> وحدة المشتريات
                        </a>
                        <a href="<?= base_url('sales') ?>" class="btn btn-outline-warning" target="_blank">
                            <i class="fas fa-chart-line"></i> وحدة المبيعات
                        </a>
                    </div>
                </div>
                <div class="col-md-6">
                    <h6>اختبار البرامج الفرعية:</h6>
                    <div class="d-grid gap-2">
                        <a href="<?= base_url('inventory/products') ?>" class="btn btn-outline-secondary" target="_blank">
                            <i class="fas fa-box"></i> المنتجات
                        </a>
                        <a href="<?= base_url('purchases/suppliers') ?>" class="btn btn-outline-secondary" target="_blank">
                            <i class="fas fa-truck"></i> الموردين
                        </a>
                        <a href="<?= base_url('sales/customers') ?>" class="btn btn-outline-secondary" target="_blank">
                            <i class="fas fa-users"></i> العملاء
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
