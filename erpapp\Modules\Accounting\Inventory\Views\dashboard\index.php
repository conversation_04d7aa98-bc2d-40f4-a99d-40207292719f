<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title">
                    <i class="fas fa-boxes text-primary"></i> <?= $title ?? 'وحدة إدارة المخزون' ?>
                </h1>
                <div class="btn-group">
                    <a href="https://qqo.shq.mybluehost.me/inventory/products/create" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-2"></i> منتج جديد
                    </a>
                    <a href="https://qqo.shq.mybluehost.me/inventory/reports" class="btn btn-outline-secondary">
                        <i class="fas fa-chart-line me-2"></i> التقارير
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي المنتجات
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['total_products'] ?? 0) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-cube fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                المنتجات النشطة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['active_products'] ?? 0) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                المخازن
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['total_warehouses'] ?? 0) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-warehouse fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                يحتاج إعادة طلب
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['reorder_count'] ?? 0) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt me-2"></i> إجراءات سريعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="<?= base_url('inventory/products') ?>" class="btn btn-outline-primary btn-block h-100 d-flex flex-column justify-content-center">
                                <i class="fas fa-cube fa-2x mb-2"></i>
                                <span>إدارة المنتجات</span>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="<?= base_url('inventory/stock') ?>" class="btn btn-outline-success btn-block h-100 d-flex flex-column justify-content-center">
                                <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                <span>أرصدة المخزون</span>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="<?= base_url('inventory/categories') ?>" class="btn btn-outline-info btn-block h-100 d-flex flex-column justify-content-center">
                                <i class="fas fa-tags fa-2x mb-2"></i>
                                <span>إدارة الفئات</span>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="<?= base_url('inventory/warehouses') ?>" class="btn btn-outline-warning btn-block h-100 d-flex flex-column justify-content-center">
                                <i class="fas fa-warehouse fa-2x mb-2"></i>
                                <span>إدارة المخازن</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alerts Row -->
    <?php if (($stats['out_of_stock_count'] ?? 0) > 0 || ($stats['reorder_count'] ?? 0) > 0): ?>
    <div class="row mb-4">
        <?php if (($stats['out_of_stock_count'] ?? 0) > 0): ?>
        <div class="col-lg-6 mb-4">
            <div class="card border-left-danger shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-danger">
                        <i class="fas fa-exclamation-circle me-2"></i> منتجات نافدة
                    </h6>
                    <a href="<?= base_url('inventory/stock/out-of-stock') ?>" class="btn btn-sm btn-outline-danger">
                        عرض الكل
                    </a>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <strong><?= $stats['out_of_stock_count'] ?></strong> منتج نافد من المخزون
                    </div>
                    <a href="<?= base_url('inventory/stock/out-of-stock') ?>" class="btn btn-danger btn-sm">
                        <i class="fas fa-eye me-1"></i> عرض المنتجات النافدة
                    </a>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <?php if (($stats['reorder_count'] ?? 0) > 0): ?>
        <div class="col-lg-6 mb-4">
            <div class="card border-left-warning shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i> يحتاج إعادة طلب
                    </h6>
                    <a href="<?= base_url('inventory/stock/need-reorder') ?>" class="btn btn-sm btn-outline-warning">
                        عرض الكل
                    </a>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <strong><?= $stats['reorder_count'] ?></strong> منتج يحتاج إعادة طلب
                    </div>
                    <a href="<?= base_url('inventory/stock/need-reorder') ?>" class="btn btn-warning btn-sm">
                        <i class="fas fa-eye me-1"></i> عرض المنتجات
                    </a>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-lg-8 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-history me-2"></i> النشاط الأخير
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center py-4">
                        <i class="fas fa-clock fa-3x text-gray-300 mb-3"></i>
                        <p class="text-muted">سيتم عرض النشاط الأخير هنا قريباً</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-pie me-2"></i> توزيع الفئات
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center py-4">
                        <i class="fas fa-chart-pie fa-3x text-gray-300 mb-3"></i>
                        <p class="text-muted">إجمالي الفئات: <?= $stats['total_categories'] ?? 0 ?></p>
                        <a href="<?= base_url('inventory/categories') ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-eye me-1"></i> عرض الفئات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
}

.btn-block {
    display: block;
    width: 100%;
}

.card {
    transition: all 0.3s;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}
</style>
