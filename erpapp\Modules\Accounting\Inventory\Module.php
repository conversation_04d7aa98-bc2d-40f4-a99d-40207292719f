<?php
namespace Modules\Accounting\Inventory;

use App\Core\Module as BaseModule;

/**
 * وحدة المخزون
 */
class Module extends BaseModule
{
    /**
     * اسم الوحدة
     */
    protected $name = 'Inventory';

    /**
     * وصف الوحدة
     */
    protected $description = 'إدارة المخزون والمنتجات';

    /**
     * إصدار الوحدة
     */
    protected $version = '1.0.0';

    /**
     * الوحدات المطلوبة
     */
    protected $dependencies = ['System.Companies', 'System.Users'];

    /**
     * تسجيل المسارات الخاصة بالوحدة
     */
    public function registerRoutes()
    {
        // مسارات المخزون
        add_route('GET', '/inventory', 'App\Modules\Accounting\Inventory\Controllers\InventoryController@index');
        add_route('GET', '/inventory/products', 'App\Modules\Accounting\Inventory\Controllers\ProductController@index');
        add_route('GET', '/inventory/products/create', 'App\Modules\Accounting\Inventory\Controllers\ProductController@create');
        add_route('POST', '/inventory/products/store', 'App\Modules\Accounting\Inventory\Controllers\ProductController@store');
        add_route('GET', '/inventory/products/{id}', 'App\Modules\Accounting\Inventory\Controllers\ProductController@show');
        add_route('GET', '/inventory/products/{id}/edit', 'App\Modules\Accounting\Inventory\Controllers\ProductController@edit');
        add_route('POST', '/inventory/products/{id}/update', 'App\Modules\Accounting\Inventory\Controllers\ProductController@update');

        // مسارات حركات المخزون
        add_route('GET', '/inventory/movements', 'App\Modules\Accounting\Inventory\Controllers\MovementController@index');
        add_route('GET', '/inventory/movements/create', 'App\Modules\Accounting\Inventory\Controllers\MovementController@create');
        add_route('POST', '/inventory/movements/store', 'App\Modules\Accounting\Inventory\Controllers\MovementController@store');

        // مسارات التقارير
        add_route('GET', '/inventory/reports', 'App\Modules\Accounting\Inventory\Controllers\ReportController@index');
        add_route('GET', '/inventory/reports/stock', 'App\Modules\Accounting\Inventory\Controllers\ReportController@stockReport');
        add_route('GET', '/inventory/reports/movements', 'App\Modules\Accounting\Inventory\Controllers\ReportController@movementsReport');
    }

    /**
     * تهيئة الوحدة
     */
    public function boot()
    {
        parent::boot();

        // تسجيل المسارات
        $this->registerRoutes();

        // تسجيل الخدمات
        $this->registerServices();
    }

    /**
     * تسجيل الخدمات
     */
    protected function registerServices()
    {
        // يمكن تسجيل خدمات إضافية هنا
    }

    /**
     * التحقق من صلاحيات الوحدة
     */
    public function hasPermission($user_id, $company_id)
    {
        // التحقق من أن الشركة لديها اشتراك يسمح بوحدة المخزون
        return true;
    }

    /**
     * الحصول على معلومات الوحدة
     */
    public function getInfo()
    {
        return [
            'name' => $this->name,
            'description' => $this->description,
            'version' => $this->version,
            'dependencies' => $this->dependencies,
            'category' => 'Accounting'
        ];
    }
}
