<?php
namespace Modules\Accounting\Inventory;

use App\Core\Module as BaseModule;

/**
 * وحدة المخزون
 */
class Module extends BaseModule
{
    /**
     * اسم الوحدة
     */
    protected $name = 'Inventory';

    /**
     * وصف الوحدة
     */
    protected $description = 'إدارة المخزون والمنتجات';

    /**
     * إصدار الوحدة
     */
    protected $version = '1.0.0';

    /**
     * الوحدات المطلوبة
     */
    protected $dependencies = ['System.Companies', 'System.Users'];

    /**
     * تسجيل المسارات الخاصة بالوحدة
     */
    public function registerRoutes()
    {
        $namespace = 'Modules\Accounting\Inventory\Controllers\\';

        // المسارات الرئيسية
        add_route('GET', '/inventory', $namespace . 'InventoryController@index');
        add_route('GET', '/inventory/dashboard', $namespace . 'InventoryController@dashboard');
        add_route('GET', '/inventory/search', $namespace . 'InventoryController@search');
        add_route('GET', '/inventory/reports', $namespace . 'InventoryController@reports');
        add_route('GET', '/inventory/settings', $namespace . 'InventoryController@settings');
        add_route('GET', '/inventory/export/{type}', $namespace . 'InventoryController@export');

        // مسارات المنتجات
        add_route('GET', '/inventory/products', $namespace . 'ProductController@index');
        add_route('GET', '/inventory/products/create', $namespace . 'ProductController@create');
        add_route('POST', '/inventory/products/store', $namespace . 'ProductController@store');
        add_route('GET', '/inventory/products/{id}', $namespace . 'ProductController@show');
        add_route('GET', '/inventory/products/{id}/edit', $namespace . 'ProductController@edit');
        add_route('POST', '/inventory/products/{id}/update', $namespace . 'ProductController@update');
        add_route('POST', '/inventory/products/{id}/delete', $namespace . 'ProductController@delete');
        add_route('GET', '/inventory/products/reorder', $namespace . 'ProductController@reorder');

        // مسارات الفئات
        add_route('GET', '/inventory/categories', $namespace . 'CategoryController@index');
        add_route('GET', '/inventory/categories/create', $namespace . 'CategoryController@create');
        add_route('POST', '/inventory/categories/store', $namespace . 'CategoryController@store');
        add_route('GET', '/inventory/categories/{id}', $namespace . 'CategoryController@show');
        add_route('GET', '/inventory/categories/{id}/edit', $namespace . 'CategoryController@edit');
        add_route('POST', '/inventory/categories/{id}/update', $namespace . 'CategoryController@update');
        add_route('POST', '/inventory/categories/{id}/delete', $namespace . 'CategoryController@delete');
        add_route('GET', '/inventory/categories/hierarchy', $namespace . 'CategoryController@hierarchy');
        add_route('GET', '/inventory/categories/{id}/subcategories', $namespace . 'CategoryController@getSubCategories');

        // مسارات وحدات القياس
        add_route('GET', '/inventory/units', $namespace . 'UnitController@index');
        add_route('GET', '/inventory/units/create', $namespace . 'UnitController@create');
        add_route('POST', '/inventory/units/store', $namespace . 'UnitController@store');
        add_route('GET', '/inventory/units/{id}', $namespace . 'UnitController@show');
        add_route('GET', '/inventory/units/{id}/edit', $namespace . 'UnitController@edit');
        add_route('POST', '/inventory/units/{id}/update', $namespace . 'UnitController@update');
        add_route('POST', '/inventory/units/{id}/delete', $namespace . 'UnitController@delete');
        add_route('POST', '/inventory/units/convert', $namespace . 'UnitController@convert');

        // مسارات المخازن
        add_route('GET', '/inventory/warehouses', $namespace . 'WarehouseController@index');
        add_route('GET', '/inventory/warehouses/create', $namespace . 'WarehouseController@create');
        add_route('POST', '/inventory/warehouses/store', $namespace . 'WarehouseController@store');
        add_route('GET', '/inventory/warehouses/{id}', $namespace . 'WarehouseController@show');
        add_route('GET', '/inventory/warehouses/{id}/edit', $namespace . 'WarehouseController@edit');
        add_route('POST', '/inventory/warehouses/{id}/update', $namespace . 'WarehouseController@update');
        add_route('POST', '/inventory/warehouses/{id}/delete', $namespace . 'WarehouseController@delete');
        add_route('POST', '/inventory/warehouses/{id}/update-usage', $namespace . 'WarehouseController@updateUsage');
        add_route('GET', '/inventory/warehouses/type/{type}', $namespace . 'WarehouseController@getByType');

        // مسارات أرصدة المخزون
        add_route('GET', '/inventory/stock', $namespace . 'StockController@index');
        add_route('GET', '/inventory/stock/{product_id}/{warehouse_id}', $namespace . 'StockController@show');
        add_route('GET', '/inventory/stock/{product_id}/{warehouse_id}/adjust', $namespace . 'StockController@adjust');
        add_route('POST', '/inventory/stock/{product_id}/{warehouse_id}/process-adjustment', $namespace . 'StockController@processAdjustment');
        add_route('POST', '/inventory/stock/{product_id}/{warehouse_id}/reserve', $namespace . 'StockController@reserve');
        add_route('POST', '/inventory/stock/{product_id}/{warehouse_id}/unreserve', $namespace . 'StockController@unreserve');
        add_route('GET', '/inventory/stock/product/{product_id}/total', $namespace . 'StockController@totalStock');
        add_route('GET', '/inventory/stock/out-of-stock', $namespace . 'StockController@outOfStock');
        add_route('GET', '/inventory/stock/need-reorder', $namespace . 'StockController@needReorder');
    }

    /**
     * تهيئة الوحدة
     */
    public function boot()
    {
        parent::boot();

        // تسجيل المسارات
        $this->registerRoutes();

        // تسجيل الخدمات
        $this->registerServices();
    }

    /**
     * تسجيل الخدمات
     */
    protected function registerServices()
    {
        // يمكن تسجيل خدمات إضافية هنا
    }

    /**
     * التحقق من صلاحيات الوحدة
     */
    public function hasPermission($user_id, $company_id)
    {
        // التحقق من أن الشركة لديها اشتراك يسمح بوحدة المخزون
        return true;
    }

    /**
     * الحصول على معلومات الوحدة
     */
    public function getInfo()
    {
        return [
            'name' => $this->name,
            'description' => $this->description,
            'version' => $this->version,
            'dependencies' => $this->dependencies,
            'category' => 'Accounting',
            'icon' => 'fas fa-boxes',
            'status' => 'active'
        ];
    }

    /**
     * الحصول على صلاحيات الوحدة
     */
    public function getPermissions()
    {
        return [
            'inventory_view' => 'عرض المخزون',
            'inventory_create' => 'إنشاء عناصر المخزون',
            'inventory_edit' => 'تعديل المخزون',
            'inventory_delete' => 'حذف عناصر المخزون',
            'inventory_reports' => 'تقارير المخزون',
            'inventory_settings' => 'إعدادات المخزون',

            'products_view' => 'عرض المنتجات',
            'products_create' => 'إنشاء منتجات',
            'products_edit' => 'تعديل المنتجات',
            'products_delete' => 'حذف المنتجات',

            'categories_view' => 'عرض الفئات',
            'categories_create' => 'إنشاء فئات',
            'categories_edit' => 'تعديل الفئات',
            'categories_delete' => 'حذف الفئات',

            'units_view' => 'عرض وحدات القياس',
            'units_create' => 'إنشاء وحدات قياس',
            'units_edit' => 'تعديل وحدات القياس',
            'units_delete' => 'حذف وحدات القياس',

            'warehouses_view' => 'عرض المخازن',
            'warehouses_create' => 'إنشاء مخازن',
            'warehouses_edit' => 'تعديل المخازن',
            'warehouses_delete' => 'حذف المخازن',

            'stock_view' => 'عرض أرصدة المخزون',
            'stock_adjust' => 'تعديل أرصدة المخزون',
            'stock_reserve' => 'حجز المخزون',
            'stock_transfer' => 'نقل المخزون'
        ];
    }

    /**
     * الحصول على عناصر القائمة
     */
    public function getMenuItems()
    {
        return [
            [
                'title' => 'إدارة المخزون',
                'icon' => 'fas fa-boxes',
                'url' => '/inventory',
                'permission' => 'inventory_view',
                'children' => [
                    [
                        'title' => 'لوحة التحكم',
                        'icon' => 'fas fa-tachometer-alt',
                        'url' => '/inventory/dashboard',
                        'permission' => 'inventory_view'
                    ],
                    [
                        'title' => 'المنتجات',
                        'icon' => 'fas fa-cube',
                        'url' => '/inventory/products',
                        'permission' => 'products_view'
                    ],
                    [
                        'title' => 'الفئات',
                        'icon' => 'fas fa-tags',
                        'url' => '/inventory/categories',
                        'permission' => 'categories_view'
                    ],
                    [
                        'title' => 'وحدات القياس',
                        'icon' => 'fas fa-ruler',
                        'url' => '/inventory/units',
                        'permission' => 'units_view'
                    ],
                    [
                        'title' => 'المخازن',
                        'icon' => 'fas fa-warehouse',
                        'url' => '/inventory/warehouses',
                        'permission' => 'warehouses_view'
                    ],
                    [
                        'title' => 'أرصدة المخزون',
                        'icon' => 'fas fa-chart-bar',
                        'url' => '/inventory/stock',
                        'permission' => 'stock_view'
                    ],
                    [
                        'title' => 'التقارير',
                        'icon' => 'fas fa-chart-line',
                        'url' => '/inventory/reports',
                        'permission' => 'inventory_reports'
                    ]
                ]
            ]
        ];
    }
}
