<?php
/**
 * لوحة تحكم الوحدات
 * تعرض إحصائيات الوحدات والبرامج المنزلة
 */

$stats = $dashboard_data['modules_stats'] ?? [];
$activities = $dashboard_data['recent_activities'] ?? [];
$systemInfo = $dashboard_data['system_info'] ?? [];
$quickActions = $dashboard_data['quick_actions'] ?? [];
?>

<!-- تضمين CSS خاص بلوحة التحكم -->
<link rel="stylesheet" href="<?= base_url('public/css/modules/dashboard.css') ?>">

<!-- Dashboard Type Indicator -->
<div class="dashboard-type-indicator mb-3">
    <div class="d-flex align-items-center justify-content-between">
        <div class="d-flex align-items-center">
            <div class="dashboard-type-icon bg-success">
                <i class="fas fa-cubes"></i>
            </div>
            <div class="ms-3">
                <h1 class="page-title mb-0"><?= __('لوحة تحكم الوحدات') ?></h1>
                <p class="text-muted mb-0">نظرة شاملة على جميع الوحدات والبرامج المنزلة</p>
            </div>
        </div>
        <div class="dashboard-switcher">
            <a href="<?= base_url('dashboard') ?>?type=system" class="btn btn-outline-primary">
                <i class="fas fa-cogs me-1"></i>
                لوحة تحكم النظام
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <!-- إجمالي الوحدات -->
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-primary bg-gradient">
                        <i class="fas fa-cubes"></i>
                    </div>
                    <div class="ms-3">
                        <div class="stats-number"><?= $stats['total_modules'] ?? 0 ?></div>
                        <div class="stats-label">إجمالي الوحدات</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الوحدات النشطة -->
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-success bg-gradient">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="ms-3">
                        <div class="stats-number"><?= $stats['active_modules'] ?? 0 ?></div>
                        <div class="stats-label">الوحدات النشطة</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إجمالي البرامج -->
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-info bg-gradient">
                        <i class="fas fa-puzzle-piece"></i>
                    </div>
                    <div class="ms-3">
                        <div class="stats-number"><?= $stats['total_programs'] ?? 0 ?></div>
                        <div class="stats-label">إجمالي البرامج</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- المستخدمين النشطين -->
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-warning bg-gradient">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="ms-3">
                        <div class="stats-number"><?= $stats['total_users'] ?? 0 ?></div>
                        <div class="stats-label">المستخدمين النشطين</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- الوحدات المنزلة -->
    <div class="col-xl-8 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 pb-0">
                <div class="d-flex align-items-center justify-content-between">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cubes me-2 text-primary"></i>
                        الوحدات المنزلة
                    </h5>
                    <a href="<?= base_url('modules/manage') ?>" class="btn btn-sm btn-outline-primary">
                        عرض الكل
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div id="installedModules">
                    <!-- سيتم تحميل الوحدات هنا عبر AJAX -->
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الإجراءات السريعة -->
    <div class="col-xl-4 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2 text-warning"></i>
                    الإجراءات السريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <?php foreach ($quickActions as $action): ?>
                    <a href="<?= $action['url'] ?>" class="list-group-item list-group-item-action border-0 px-0">
                        <div class="d-flex align-items-center">
                            <div class="action-icon bg-<?= $action['color'] ?> bg-gradient">
                                <i class="<?= $action['icon'] ?>"></i>
                            </div>
                            <div class="ms-3">
                                <div class="fw-semibold"><?= htmlspecialchars($action['title']) ?></div>
                                <small class="text-muted"><?= htmlspecialchars($action['description']) ?></small>
                            </div>
                        </div>
                    </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- الأنشطة الحديثة -->
    <div class="col-xl-6 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2 text-info"></i>
                    الأنشطة الحديثة
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($activities)): ?>
                <div class="timeline">
                    <?php foreach ($activities as $activity): ?>
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title"><?= htmlspecialchars($activity['title']) ?></h6>
                            <p class="timeline-text text-muted mb-1">
                                تم تنزيل الوحدة بواسطة <?= htmlspecialchars($activity['user_name']) ?>
                            </p>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                <?= date('Y-m-d H:i', strtotime($activity['created_at'])) ?>
                            </small>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <div class="text-center py-4 text-muted">
                    <i class="fas fa-history fa-2x mb-3"></i>
                    <p>لا توجد أنشطة حديثة</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- معلومات النظام -->
    <div class="col-xl-6 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 pb-0">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2 text-success"></i>
                    معلومات النظام
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($systemInfo)): ?>
                <div class="system-info">
                    <div class="info-item">
                        <label>اسم الشركة:</label>
                        <span><?= htmlspecialchars($systemInfo['CompanyName'] ?? 'غير محدد') ?></span>
                    </div>
                    <div class="info-item">
                        <label>حالة الشركة:</label>
                        <span class="badge bg-<?= $systemInfo['CompanyStatus'] === 'Active' ? 'success' : 'warning' ?>">
                            <?= $systemInfo['CompanyStatus'] === 'Active' ? 'نشطة' : 'تجريبية' ?>
                        </span>
                    </div>
                    <div class="info-item">
                        <label>خطة الاشتراك:</label>
                        <span><?= htmlspecialchars($systemInfo['plan_name_ar'] ?? 'غير محدد') ?></span>
                    </div>
                    <?php if ($systemInfo['subscription_end']): ?>
                    <div class="info-item">
                        <label>انتهاء الاشتراك:</label>
                        <span><?= date('Y-m-d', strtotime($systemInfo['subscription_end'])) ?></span>
                    </div>
                    <?php endif; ?>
                </div>
                <?php else: ?>
                <div class="text-center py-4 text-muted">
                    <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                    <p>لا توجد معلومات متاحة</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحميل الوحدات المنزلة
    loadInstalledModules();
});

function loadInstalledModules() {
    // هنا يمكن إضافة AJAX لتحميل الوحدات
    const container = document.getElementById('installedModules');
    
    // محاكاة تحميل البيانات
    setTimeout(() => {
        container.innerHTML = `
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="module-card">
                        <div class="d-flex align-items-center">
                            <div class="module-icon bg-primary">
                                <i class="fas fa-boxes"></i>
                            </div>
                            <div class="ms-3">
                                <h6 class="mb-1">وحدة إدارة المخزون</h6>
                                <small class="text-muted">نشطة</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="module-card">
                        <div class="d-flex align-items-center">
                            <div class="module-icon bg-success">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="ms-3">
                                <h6 class="mb-1">وحدة المبيعات</h6>
                                <small class="text-muted">نشطة</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }, 1000);
}
</script>

<!-- CSS للتحسينات -->
<style>
.dashboard-type-indicator {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #dee2e6;
}

.dashboard-type-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.dashboard-switcher .btn {
    border-radius: 8px;
    font-weight: 500;
}

.page-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: #2c3e50;
}

.action-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}
</style>
