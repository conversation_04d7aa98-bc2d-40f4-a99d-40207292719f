<?php
/**
 * Dynamic Routes
 *
 * هذا الملف يحتوي على المسارات الديناميكية للنظام
 * يتم استخدامه لإنشاء مسارات CRUD بشكل تلقائي
 */

/**
 * إنشاء مسارات CRUD لمتحكم معين
 *
 * @param string $prefix بادئة المسار (مثل: companies)
 * @param string $controller اسم المتحكم (مثل: CompanyController)
 * @return void
 */
function register_crud_routes($prefix, $controller) {
    global $router;

    // قائمة العناصر
    $router->get("/{$prefix}", "{$controller}@index");

    // إنشاء عنصر جديد
    $router->get("/{$prefix}/create", "{$controller}@create");
    $router->post("/{$prefix}/store", "{$controller}@store");

    // عرض عنصر
    $router->get("/{$prefix}/:id", "{$controller}@show");

    // تعديل عنصر
    $router->get("/{$prefix}/:id/edit", "{$controller}@edit");
    $router->post("/{$prefix}/:id/update", "{$controller}@update");

    // حذف عنصر
    $router->post("/{$prefix}/:id/delete", "{$controller}@delete");
}

// تسجيل مسارات CRUD للشركات - تم نقلها إلى وحدة المحاسبة
// register_crud_routes('companies', 'CompanyController');

// تسجيل مسارات CRUD للمستخدمين
register_crud_routes('users', 'UserController');

// تسجيل مسارات CRUD للبرامج
register_crud_routes('programs', 'ProgramController');
// تسجيل مسارات CRUD للبرامج
register_crud_routes('subscriptions', 'SubscriptionController');
// مسارات إضافية للشركات - تم نقلها إلى وحدة المحاسبة
// $router->post('/companies/:id/accept-invitation', 'CompanyController@acceptInvitation');
// $router->post('/companies/:id/reject-invitation', 'CompanyController@rejectInvitation');
// $router->post('/companies/:id/deactivate', 'CompanyController@deactivate');

// مسارات إضافية للبرامج
$router->post('/programs/:id/install', 'ProgramController@install');
$router->post('/programs/:id/:company_id/uninstall', 'ProgramController@uninstall');

// يمكن إضافة المزيد من المسارات الديناميكية هنا
