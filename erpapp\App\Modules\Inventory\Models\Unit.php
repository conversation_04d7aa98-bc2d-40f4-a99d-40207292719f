<?php
namespace App\Modules\Inventory\Models;

use PDO;
use Exception;

/**
 * Unit Model - نموذج وحدات القياس
 */
class Unit
{
    /**
     * Database connection
     */
    protected $db;

    /**
     * Table name
     */
    protected $table = 'inventory_units';

    /**
     * Constructor
     */
    public function __construct()
    {
        global $db;
        $this->db = $db;
    }

    /**
     * الحصول على جميع وحدات القياس للشركة
     */
    public function getByCompany($company_id, $filters = [])
    {
        $sql = "SELECT * FROM {$this->table} WHERE company_id = ?";
        $params = [$company_id];

        // تطبيق الفلاتر
        if (isset($filters['is_active'])) {
            $sql .= " AND is_active = ?";
            $params[] = $filters['is_active'];
        }

        if (isset($filters['is_base_unit'])) {
            $sql .= " AND is_base_unit = ?";
            $params[] = $filters['is_base_unit'];
        }

        if (!empty($filters['search'])) {
            $sql .= " AND (unit_name_ar LIKE ? OR unit_name_en LIKE ? OR unit_code LIKE ?)";
            $search = '%' . $filters['search'] . '%';
            $params[] = $search;
            $params[] = $search;
            $params[] = $search;
        }

        $sql .= " ORDER BY is_base_unit DESC, unit_name_ar";

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على وحدة قياس بالمعرف
     */
    public function getById($unit_id, $company_id)
    {
        $sql = "SELECT u.*, bu.unit_name_ar as base_unit_name
                FROM {$this->table} u
                LEFT JOIN {$this->table} bu ON u.base_unit_id = bu.unit_id
                WHERE u.unit_id = ? AND u.company_id = ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$unit_id, $company_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على وحدة قياس بالكود
     */
    public function getByCode($unit_code, $company_id)
    {
        $sql = "SELECT * FROM {$this->table} WHERE unit_code = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$unit_code, $company_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * إنشاء وحدة قياس جديدة
     */
    public function create($data)
    {
        // التحقق من عدم تكرار الكود
        if ($this->getByCode($data['unit_code'], $data['company_id'])) {
            throw new Exception('كود وحدة القياس موجود مسبقاً');
        }

        $sql = "INSERT INTO {$this->table} (
                    company_id, module_code, unit_code,
                    unit_name_ar, unit_name_en, unit_symbol_ar, unit_symbol_en,
                    base_unit_id, conversion_factor, is_base_unit, is_active,
                    created_by, created_at
                ) VALUES (
                    ?, 'inventory', ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW()
                )";

        $stmt = $this->db->prepare($sql);
        $result = $stmt->execute([
            $data['company_id'],
            $data['unit_code'],
            $data['unit_name_ar'],
            $data['unit_name_en'] ?: null,
            $data['unit_symbol_ar'],
            $data['unit_symbol_en'] ?: null,
            $data['base_unit_id'] ?: null,
            $data['conversion_factor'] ?: 1.0000,
            $data['is_base_unit'] ?: 0,
            $data['is_active'] ?: 1,
            $data['created_by']
        ]);

        return $result ? $this->db->lastInsertId() : false;
    }

    /**
     * تحديث وحدة قياس
     */
    public function update($unit_id, $data, $company_id)
    {
        // التحقق من عدم تكرار الكود (إذا تم تغييره)
        if (isset($data['unit_code'])) {
            $existing = $this->getByCode($data['unit_code'], $company_id);
            if ($existing && $existing['unit_id'] != $unit_id) {
                throw new Exception('كود وحدة القياس موجود مسبقاً');
            }
        }

        $sql = "UPDATE {$this->table} SET
                    unit_code = ?, unit_name_ar = ?, unit_name_en = ?,
                    unit_symbol_ar = ?, unit_symbol_en = ?,
                    base_unit_id = ?, conversion_factor = ?,
                    is_base_unit = ?, is_active = ?,
                    updated_by = ?, updated_at = NOW()
                WHERE unit_id = ? AND company_id = ?";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            $data['unit_code'],
            $data['unit_name_ar'],
            $data['unit_name_en'] ?: null,
            $data['unit_symbol_ar'],
            $data['unit_symbol_en'] ?: null,
            $data['base_unit_id'] ?: null,
            $data['conversion_factor'] ?: 1.0000,
            $data['is_base_unit'] ?: 0,
            $data['is_active'] ?: 1,
            $data['updated_by'],
            $unit_id,
            $company_id
        ]);
    }

    /**
     * حذف وحدة قياس
     */
    public function delete($unit_id, $company_id)
    {
        // التحقق من عدم وجود منتجات تستخدم هذه الوحدة
        $sql = "SELECT COUNT(*) FROM inventory_products WHERE unit_id = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$unit_id, $company_id]);

        if ($stmt->fetchColumn() > 0) {
            throw new Exception('لا يمكن حذف وحدة القياس لوجود منتجات مرتبطة بها');
        }

        // التحقق من عدم وجود وحدات فرعية
        $sql = "SELECT COUNT(*) FROM {$this->table} WHERE base_unit_id = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$unit_id, $company_id]);

        if ($stmt->fetchColumn() > 0) {
            throw new Exception('لا يمكن حذف وحدة القياس لوجود وحدات فرعية تابعة لها');
        }

        // حذف وحدة القياس
        $sql = "DELETE FROM {$this->table} WHERE unit_id = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$unit_id, $company_id]);
    }

    /**
     * الحصول على الوحدات الأساسية
     */
    public function getBaseUnits($company_id)
    {
        $sql = "SELECT * FROM {$this->table} 
                WHERE company_id = ? AND is_base_unit = 1 AND is_active = 1
                ORDER BY unit_name_ar";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على الوحدات الفرعية
     */
    public function getSubUnits($base_unit_id, $company_id)
    {
        $sql = "SELECT * FROM {$this->table} 
                WHERE company_id = ? AND base_unit_id = ? AND is_active = 1
                ORDER BY conversion_factor, unit_name_ar";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id, $base_unit_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * تحويل الكمية بين الوحدات
     */
    public function convertQuantity($quantity, $from_unit_id, $to_unit_id, $company_id)
    {
        // إذا كانت نفس الوحدة
        if ($from_unit_id == $to_unit_id) {
            return $quantity;
        }

        // الحصول على معلومات الوحدتين
        $from_unit = $this->getById($from_unit_id, $company_id);
        $to_unit = $this->getById($to_unit_id, $company_id);

        if (!$from_unit || !$to_unit) {
            throw new Exception('وحدة القياس غير موجودة');
        }

        // التحقق من إمكانية التحويل (نفس الوحدة الأساسية)
        $from_base = $from_unit['is_base_unit'] ? $from_unit_id : $from_unit['base_unit_id'];
        $to_base = $to_unit['is_base_unit'] ? $to_unit_id : $to_unit['base_unit_id'];

        if ($from_base != $to_base) {
            throw new Exception('لا يمكن التحويل بين وحدات قياس مختلفة الأساس');
        }

        // تحويل إلى الوحدة الأساسية أولاً
        $base_quantity = $quantity * $from_unit['conversion_factor'];
        
        // ثم تحويل إلى الوحدة المطلوبة
        $converted_quantity = $base_quantity / $to_unit['conversion_factor'];

        return $converted_quantity;
    }

    /**
     * الحصول على إحصائيات وحدات القياس
     */
    public function getStats($company_id)
    {
        $stats = [];

        // إجمالي وحدات القياس
        $sql = "SELECT COUNT(*) FROM {$this->table} WHERE company_id = ? AND is_active = 1";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        $stats['total_units'] = $stmt->fetchColumn();

        // الوحدات الأساسية
        $sql = "SELECT COUNT(*) FROM {$this->table} WHERE company_id = ? AND is_base_unit = 1 AND is_active = 1";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        $stats['base_units'] = $stmt->fetchColumn();

        // الوحدات الفرعية
        $sql = "SELECT COUNT(*) FROM {$this->table} WHERE company_id = ? AND is_base_unit = 0 AND is_active = 1";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        $stats['sub_units'] = $stmt->fetchColumn();

        return $stats;
    }

    /**
     * الحصول على عدد المنتجات لكل وحدة قياس
     */
    public function getUnitsWithProductCount($company_id)
    {
        $sql = "SELECT u.*, COUNT(p.product_id) as product_count
                FROM {$this->table} u
                LEFT JOIN inventory_products p ON u.unit_id = p.unit_id AND p.company_id = u.company_id
                WHERE u.company_id = ? AND u.is_active = 1
                GROUP BY u.unit_id
                ORDER BY u.is_base_unit DESC, u.unit_name_ar";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
