<?php
/**
 * اختبار هيكل البرامج الرئيسية والفرعية
 */

// تحميل النظام
require_once 'loader.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار هيكل البرامج</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-4">
    <h1 class="text-center mb-4">
        <i class="fas fa-sitemap text-primary"></i> اختبار هيكل البرامج الرئيسية والفرعية
    </h1>

    <?php 
    $user = current_user();
    if (!$user): 
    ?>
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle"></i> يجب تسجيل الدخول لعرض البرامج
    </div>
    <?php 
    else:
        $companyId = $user['current_company_id'];
        if (!$companyId):
    ?>
    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i> يجب اختيار شركة لعرض البرامج
    </div>
    <?php 
        else:
            $hierarchy = getProgramsHierarchy($companyId);
    ?>

    <!-- معلومات المستخدم والشركة -->
    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5><i class="fas fa-user"></i> معلومات المستخدم والشركة</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <strong>المستخدم:</strong> <?= htmlspecialchars($user['username'] ?? 'غير محدد') ?><br>
                    <strong>معرف الشركة:</strong> <?= $companyId ?><br>
                    <strong>مالك الشركة:</strong> 
                    <span class="badge bg-<?= isCompanyOwner() ? 'success' : 'secondary' ?>">
                        <?= isCompanyOwner() ? 'نعم' : 'لا' ?>
                    </span>
                </div>
                <div class="col-md-6">
                    <strong>عدد البرامج الرئيسية:</strong> <?= count($hierarchy) ?><br>
                    <strong>إجمالي البرامج الفرعية:</strong> 
                    <?php 
                    $totalSub = 0;
                    foreach ($hierarchy as $item) {
                        $totalSub += count($item['sub_programs']);
                    }
                    echo $totalSub;
                    ?>
                </div>
            </div>
        </div>
    </div>

    <!-- هيكل البرامج -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5><i class="fas fa-sitemap"></i> هيكل البرامج المتاحة للشركة</h5>
        </div>
        <div class="card-body">
            <?php if (empty($hierarchy)): ?>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i> لا توجد برامج متاحة لهذه الشركة
            </div>
            <?php else: ?>
            <div class="row">
                <?php foreach ($hierarchy as $item): ?>
                <div class="col-md-6 mb-4">
                    <div class="card border-primary">
                        <!-- البرنامج الرئيسي -->
                        <div class="card-header bg-primary text-white">
                            <h6 class="mb-0">
                                <i class="<?= $item['program']['icon_name'] ?>"></i>
                                <?= htmlspecialchars($item['program']['name_ar']) ?>
                                <small class="text-light">(<?= htmlspecialchars($item['program']['name_en']) ?>)</small>
                            </h6>
                        </div>
                        <div class="card-body">
                            <!-- معلومات البرنامج الرئيسي -->
                            <div class="mb-3">
                                <strong>المسار:</strong> 
                                <code><?= htmlspecialchars($item['program']['page_url']) ?></code><br>
                                
                                <strong>الصلاحية:</strong>
                                <span class="badge bg-<?= canView($item['program']['name_en']) ? 'success' : 'danger' ?>">
                                    <?= canView($item['program']['name_en']) ? 'متاح' : 'غير متاح' ?>
                                </span><br>
                                
                                <strong>متوفر للشركة:</strong>
                                <span class="badge bg-<?= isProgramAvailable($item['program']['name_en']) ? 'success' : 'danger' ?>">
                                    <?= isProgramAvailable($item['program']['name_en']) ? 'نعم' : 'لا' ?>
                                </span>
                            </div>

                            <!-- البرامج الفرعية -->
                            <?php if (!empty($item['sub_programs'])): ?>
                            <h6 class="text-secondary">البرامج الفرعية:</h6>
                            <div class="list-group">
                                <?php foreach ($item['sub_programs'] as $subProgram): ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="<?= $subProgram['icon_name'] ?>"></i>
                                            <strong><?= htmlspecialchars($subProgram['name_ar']) ?></strong>
                                            <br>
                                            <small class="text-muted">
                                                <code><?= htmlspecialchars($subProgram['page_url']) ?></code>
                                            </small>
                                        </div>
                                        <div>
                                            <span class="badge bg-<?= canView($item['program']['name_en']) ? 'success' : 'danger' ?> me-1">
                                                <?= canView($item['program']['name_en']) ? 'متاح' : 'غير متاح' ?>
                                            </span>
                                            <?php if (!empty($subProgram['page_url'])): ?>
                                            <a href="<?= base_url($subProgram['page_url']) ?>" 
                                               class="btn btn-sm btn-outline-primary" 
                                               target="_blank">
                                                <i class="fas fa-external-link-alt"></i>
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <?php else: ?>
                            <p class="text-muted">لا توجد برامج فرعية</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- اختبار الصلاحيات -->
    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5><i class="fas fa-check-circle"></i> اختبار الصلاحيات للبرامج</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>البرنامج</th>
                            <th>النوع</th>
                            <th>متوفر للشركة</th>
                            <th>عرض</th>
                            <th>إنشاء</th>
                            <th>تعديل</th>
                            <th>حذف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($hierarchy as $item): ?>
                        <!-- البرنامج الرئيسي -->
                        <tr class="table-primary">
                            <td>
                                <strong>
                                    <i class="<?= $item['program']['icon_name'] ?>"></i>
                                    <?= htmlspecialchars($item['program']['name_ar']) ?>
                                </strong>
                            </td>
                            <td><span class="badge bg-primary">رئيسي</span></td>
                            <td>
                                <span class="badge bg-<?= isProgramAvailable($item['program']['name_en']) ? 'success' : 'danger' ?>">
                                    <?= isProgramAvailable($item['program']['name_en']) ? 'نعم' : 'لا' ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-<?= canView($item['program']['name_en']) ? 'success' : 'danger' ?>">
                                    <?= canView($item['program']['name_en']) ? '✓' : '✗' ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-<?= canCreate($item['program']['name_en']) ? 'success' : 'danger' ?>">
                                    <?= canCreate($item['program']['name_en']) ? '✓' : '✗' ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-<?= canEdit($item['program']['name_en']) ? 'success' : 'danger' ?>">
                                    <?= canEdit($item['program']['name_en']) ? '✓' : '✗' ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-<?= canDelete($item['program']['name_en']) ? 'success' : 'danger' ?>">
                                    <?= canDelete($item['program']['name_en']) ? '✓' : '✗' ?>
                                </span>
                            </td>
                        </tr>
                        
                        <!-- البرامج الفرعية -->
                        <?php foreach ($item['sub_programs'] as $subProgram): ?>
                        <tr>
                            <td class="ps-4">
                                <i class="<?= $subProgram['icon_name'] ?>"></i>
                                <?= htmlspecialchars($subProgram['name_ar']) ?>
                            </td>
                            <td><span class="badge bg-secondary">فرعي</span></td>
                            <td>
                                <span class="badge bg-<?= isProgramAvailable($subProgram['name_en']) ? 'success' : 'danger' ?>">
                                    <?= isProgramAvailable($subProgram['name_en']) ? 'نعم' : 'لا' ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-<?= canView($item['program']['name_en']) ? 'success' : 'danger' ?>">
                                    <?= canView($item['program']['name_en']) ? '✓' : '✗' ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-<?= canCreate($item['program']['name_en']) ? 'success' : 'danger' ?>">
                                    <?= canCreate($item['program']['name_en']) ? '✓' : '✗' ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-<?= canEdit($item['program']['name_en']) ? 'success' : 'danger' ?>">
                                    <?= canEdit($item['program']['name_en']) ? '✓' : '✗' ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-<?= canDelete($item['program']['name_en']) ? 'success' : 'danger' ?>">
                                    <?= canDelete($item['program']['name_en']) ? '✓' : '✗' ?>
                                </span>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- روابط اختبار -->
    <div class="card">
        <div class="card-header bg-warning text-dark">
            <h5><i class="fas fa-link"></i> روابط اختبار الوصول</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <?php foreach ($hierarchy as $item): ?>
                <div class="col-md-6 mb-3">
                    <h6><?= htmlspecialchars($item['program']['name_ar']) ?></h6>
                    <div class="d-grid gap-1">
                        <?php if (!empty($item['program']['page_url'])): ?>
                        <a href="<?= base_url($item['program']['page_url']) ?>" 
                           class="btn btn-sm btn-outline-primary" 
                           target="_blank">
                            <i class="fas fa-external-link-alt"></i> 
                            <?= htmlspecialchars($item['program']['page_url']) ?>
                        </a>
                        <?php endif; ?>
                        
                        <?php foreach ($item['sub_programs'] as $subProgram): ?>
                        <?php if (!empty($subProgram['page_url'])): ?>
                        <a href="<?= base_url($subProgram['page_url']) ?>" 
                           class="btn btn-sm btn-outline-secondary" 
                           target="_blank">
                            <i class="fas fa-external-link-alt"></i> 
                            <?= htmlspecialchars($subProgram['page_url']) ?>
                        </a>
                        <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <?php endif; ?>
    <?php endif; ?>
</div>

</body>
</html>
