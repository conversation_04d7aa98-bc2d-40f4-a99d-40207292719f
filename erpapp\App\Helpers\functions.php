<?php
/**
 * Helper Functions
 */

/**
 * Redirect to a URL
 *
 * @param string $url
 * @return void
 */
function redirect($url) {
    header("Location: " . $url);
    exit;
}

/**
 * Get base URL
 *
 * @return string
 */
function base_url($path = '') {
    return APP_URL . '/' . ltrim($path, '/');
}

/**
 * Get current URL
 *
 * @return string
 */
function current_url() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    return $protocol . "://" . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
}

/**
 * Escape HTML
 *
 * @param string $str
 * @return string
 */
function e($str) {
    return htmlspecialchars($str, ENT_QUOTES, 'UTF-8');
}

/**
 * Check if user is logged in
 *
 * @return bool
 */
function is_logged_in() {
    return isset($_SESSION['user_id']);
}

/**
 * Get current user ID
 *
 * @return int|null
 */
function current_user_id() {
    return $_SESSION['user_id'] ?? null;
}

/**
 * Get current user data
 *
 * @return array|null
 */
function current_user() {
    if (!is_logged_in()) {
        return null;
    }

    // إذا كانت بيانات المستخدم موجودة في الجلسة، أعدها
    if (isset($_SESSION['user'])) {
        return $_SESSION['user'];
    }

    // جلب بيانات المستخدم من قاعدة البيانات
    $user_id = current_user_id();

    global $db;
    $stmt = $db->prepare("SELECT * FROM users WHERE UserID = :user_id LIMIT 1");
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $user = $stmt->fetch(\PDO::FETCH_ASSOC);

    if ($user) {
        // تخزين بيانات المستخدم في الجلسة لتجنب استعلامات قاعدة البيانات المتكررة
        $_SESSION['user'] = $user;
        return $user;
    }

    return null;
}

/**
 * Flash message
 *
 * @param string $name
 * @param string $message
 * @param string $type
 * @return void
 */
function flash($name = '', $message = '', $type = 'info') {
    if (!empty($name)) {
        if (!empty($message) && empty($_SESSION[$name])) {
            $_SESSION[$name] = $message;
            $_SESSION[$name . '_type'] = $type;
        } else if (empty($message) && !empty($_SESSION[$name])) {
            $message = $_SESSION[$name];
            $type = $_SESSION[$name . '_type'];
            unset($_SESSION[$name]);
            unset($_SESSION[$name . '_type']);
            return ['message' => $message, 'type' => $type];
        }
    }

    return null;
}

/**
 * Display flash message
 *
 * @param string $name
 * @return void
 */
function display_flash($name) {
    $flash = flash($name);
    if ($flash) {
        echo '<div class="alert alert-' . $flash['type'] . '">' . $flash['message'] . '</div>';
    }
}

/**
 * Generate CSRF token
 *
 * @return string
 */
function csrf_token() {
    if (empty($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }

    return $_SESSION['csrf_token'];
}

/**
 * Check CSRF token
 *
 * @param string $token
 * @return bool
 */
function csrf_check($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Format date
 *
 * @param string $date
 * @param string $format
 * @return string
 */
function format_date($date, $format = 'Y-m-d H:i:s') {
    return date($format, strtotime($date));
}

/**
 * Format currency
 *
 * @param float $amount
 * @param string $currency
 * @return string
 */
function format_currency($amount, $currency = 'SAR') {
    return number_format($amount, 2) . ' ' . $currency;
}

/**
 * Get current language
 *
 * @return string
 */
function current_lang() {
    return $_SESSION['lang'] ?? DEFAULT_LANG;
}

/**
 * Translate text
 *
 * @param string $key
 * @return string
 */
function __($key) {
    global $translations;

    // Load translations if not loaded
    if (empty($translations)) {
        require_once BASE_PATH . '/resources/lang/load.php';
    }

    $lang = current_lang();

    if (isset($translations[$lang][$key])) {
        return $translations[$lang][$key];
    }

    return $key;
}

/**
 * Load view
 *
 * @param string $view
 * @param array $data
 * @return void
 */
function view($view, $data = []) {
    try {
        \App\Core\View::render($view, $data);
    } catch (\Exception $e) {
        echo "Error loading view: " . $e->getMessage();
    }
}

/**
 * Load partial view
 *
 * @param string $view
 * @param array $data
 * @return void
 */
function partial($view, $data = []) {
    extract($data);

    $view_path = BASE_PATH . '/App/Views/partials/' . $view . '.php';

    if (file_exists($view_path)) {
        include $view_path;
    } else {
        echo "Partial view not found: " . $view;
    }
}

/**
 * Generate pagination
 *
 * @param int $total
 * @param int $per_page
 * @param int $current_page
 * @param string $url
 * @return string
 */
function paginate($total, $per_page, $current_page, $url) {
    $total_pages = ceil($total / $per_page);

    if ($total_pages <= 1) {
        return '';
    }

    $pagination = '<ul class="pagination">';

    // Previous button
    if ($current_page > 1) {
        $pagination .= '<li class="page-item"><a class="page-link" href="' . $url . '?page=' . ($current_page - 1) . '">&laquo;</a></li>';
    } else {
        $pagination .= '<li class="page-item disabled"><a class="page-link" href="#">&laquo;</a></li>';
    }

    // Page numbers
    for ($i = 1; $i <= $total_pages; $i++) {
        if ($i == $current_page) {
            $pagination .= '<li class="page-item active"><a class="page-link" href="#">' . $i . '</a></li>';
        } else {
            $pagination .= '<li class="page-item"><a class="page-link" href="' . $url . '?page=' . $i . '">' . $i . '</a></li>';
        }
    }

    // Next button
    if ($current_page < $total_pages) {
        $pagination .= '<li class="page-item"><a class="page-link" href="' . $url . '?page=' . ($current_page + 1) . '">&raquo;</a></li>';
    } else {
        $pagination .= '<li class="page-item disabled"><a class="page-link" href="#">&raquo;</a></li>';
    }

    $pagination .= '</ul>';

    return $pagination;
}

/**
 * Check if current path matches given path
 *
 * @param string $path
 * @return bool
 */
function isCurrentPath($path) {
    $currentPath = $_SERVER['REQUEST_URI'];
    $currentPath = parse_url($currentPath, PHP_URL_PATH);
    $currentPath = str_replace('/erpapp/', '', $currentPath);
    $currentPath = trim($currentPath, '/');

    $path = trim($path, '/');

    return $currentPath === $path || strpos($currentPath, $path) === 0;
}

/**
 * Generate module URL
 *
 * @param string $path
 * @return string
 */
function moduleUrl($path = '') {
    $path = trim($path, '/');
    return base_url('erpapp/' . $path);
}

/**
 * Get current page type (system or module)
 *
 * @return string
 */
function getCurrentPageType() {
    $currentPath = $_SERVER['REQUEST_URI'];
    $path = parse_url($currentPath, PHP_URL_PATH);
    $path = str_replace('/erpapp/', '', $path);
    $path = trim($path, '/');

    // قائمة مسارات النظام
    $systemPaths = [
        'dashboard',
        'companies',
        'programs',
        'subscriptions',
        'users',
        'settings',
        'notifications',
        'chat',
        'login',
        'register',
        'logout'
    ];

    $firstSegment = explode('/', $path)[0];

    // فحص إذا كان المسار ينتمي للنظام
    if (in_array($firstSegment, $systemPaths)) {
        return 'system';
    }

    // فحص إذا كان المسار ينتمي لوحدة
    if (isModulePath($firstSegment)) {
        return 'module';
    }

    // افتراضي: نظام
    return 'system';
}

/**
 * Check if path belongs to a module
 *
 * @param string $path
 * @return bool
 */
function isModulePath($path) {
    // قائمة الوحدات المعروفة
    $knownModules = [
        'inventory',
        'sales',
        'accounting',
        'hr',
        'crm',
        'modules' // لوحة تحكم الوحدات
    ];

    return in_array($path, $knownModules);
}

/**
 * Get current module from path
 *
 * @return string|null
 */
function getCurrentModule() {
    $currentPath = $_SERVER['REQUEST_URI'];
    $path = parse_url($currentPath, PHP_URL_PATH);
    $path = str_replace('/erpapp/', '', $path);
    $path = trim($path, '/');

    $firstSegment = explode('/', $path)[0];

    if (isModulePath($firstSegment)) {
        return $firstSegment;
    }

    return null;
}

/**
 * Get system statistics for dashboard
 *
 * @param array $user
 * @return array
 */
function getSystemStats($user) {
    try {
        global $db;

        $stats = [];

        // إحصائيات الشركات
        $stmt = $db->prepare("SELECT COUNT(*) as total FROM companies");
        $stmt->execute();
        $stats['companies'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;

        // إحصائيات البرامج
        $stmt = $db->prepare("SELECT COUNT(*) as total FROM system_modules");
        $stmt->execute();
        $stats['programs'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;

        // إحصائيات المستخدمين
        $stmt = $db->prepare("SELECT COUNT(*) as total FROM users WHERE UserStatus = 'Active'");
        $stmt->execute();
        $stats['users'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;

        return $stats;

    } catch (Exception $e) {
        error_log("خطأ في جلب إحصائيات النظام: " . $e->getMessage());
        return ['companies' => 0, 'programs' => 0, 'users' => 0];
    }
}

/**
 * Get recent companies
 *
 * @param array $user
 * @param int $limit
 * @return array
 */
function getRecentCompanies($user, $limit = 5) {
    try {
        global $db;

        $stmt = $db->prepare("
            SELECT * FROM companies
            ORDER BY CompanyCreatedAt DESC
            LIMIT ?
        ");
        $stmt->execute([$limit]);

        return $stmt->fetchAll(PDO::FETCH_ASSOC);

    } catch (Exception $e) {
        error_log("خطأ في جلب الشركات الحديثة: " . $e->getMessage());
        return [];
    }
}

/**
 * Get recent notifications
 *
 * @param array $user
 * @param int $limit
 * @return array
 */
function getRecentNotifications($user, $limit = 5) {
    // مؤقت - إرجاع مصفوفة فارغة
    return [];
}

/**
 * Get recent tasks
 *
 * @param array $user
 * @param int $limit
 * @return array
 */
function getRecentTasks($user, $limit = 5) {
    // مؤقت - إرجاع مصفوفة فارغة
    return [];
}

/**
 * Get modules statistics for dashboard
 *
 * @param int $companyId
 * @return array
 */
function getModulesStats($companyId) {
    try {
        global $db;

        // إحصائيات الوحدات المنزلة (محاكاة)
        $stats = [
            'total_modules' => 3,
            'active_modules' => 2,
            'total_programs' => 8,
            'total_users' => 5
        ];

        return $stats;

    } catch (Exception $e) {
        error_log("خطأ في جلب إحصائيات الوحدات: " . $e->getMessage());
        return [
            'total_modules' => 0,
            'active_modules' => 0,
            'total_programs' => 0,
            'total_users' => 0
        ];
    }
}

/**
 * Get modules recent activities
 *
 * @param int $companyId
 * @return array
 */
function getModulesRecentActivities($companyId) {
    // محاكاة بيانات الأنشطة
    return [
        [
            'title' => 'وحدة إدارة المخزون',
            'user_name' => 'أحمد محمد',
            'created_at' => date('Y-m-d H:i:s', strtotime('-2 hours'))
        ],
        [
            'title' => 'وحدة المبيعات',
            'user_name' => 'سارة أحمد',
            'created_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
        ]
    ];
}

/**
 * Get company system info
 *
 * @param int $companyId
 * @return array
 */
function getCompanySystemInfo($companyId) {
    try {
        global $db;

        $stmt = $db->prepare("
            SELECT
                c.CompanyName,
                c.CompanyStatus
            FROM companies c
            WHERE c.CompanyID = ?
        ");
        $stmt->execute([$companyId]);

        return $stmt->fetch(PDO::FETCH_ASSOC) ?: [];

    } catch (Exception $e) {
        error_log("خطأ في جلب معلومات النظام: " . $e->getMessage());
        return [];
    }
}

/**
 * Get modules quick actions
 *
 * @return array
 */
function getModulesQuickActions() {
    return [
        [
            'title' => 'تنزيل وحدة جديدة',
            'description' => 'استعراض وتنزيل وحدات جديدة',
            'icon' => 'fas fa-download',
            'url' => base_url('modules/install'),
            'color' => 'primary'
        ],
        [
            'title' => 'إدارة الصلاحيات',
            'description' => 'تحديد صلاحيات المستخدمين',
            'icon' => 'fas fa-user-shield',
            'url' => base_url('permissions'),
            'color' => 'warning'
        ],
        [
            'title' => 'إعدادات الوحدات',
            'description' => 'تكوين إعدادات الوحدات',
            'icon' => 'fas fa-cogs',
            'url' => base_url('modules/settings'),
            'color' => 'info'
        ],
        [
            'title' => 'النسخ الاحتياطي',
            'description' => 'إنشاء نسخة احتياطية من البيانات',
            'icon' => 'fas fa-database',
            'url' => base_url('modules/backup'),
            'color' => 'success'
        ]
    ];
}