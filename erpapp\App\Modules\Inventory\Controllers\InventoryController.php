<?php
namespace App\Modules\Inventory\Controllers;

use App\Modules\Inventory\Services\InventoryService;

/**
 * Inventory Controller - المتحكم الرئيسي للمخزون
 */
class InventoryController
{
    /**
     * Route parameters
     */
    protected $params = [];

    /**
     * Inventory service
     */
    protected $inventoryService;

    /**
     * Constructor
     */
    public function __construct($params = [])
    {
        $this->params = $params;
        $this->inventoryService = new InventoryService();

        // التحقق من تسجيل الدخول (مؤقت - سيتم تطويره لاحقاً)
        // if (!is_logged_in()) {
        //     redirect(base_url('login'));
        // }
    }

    /**
     * عرض لوحة تحكم المخزون الرئيسية
     */
    public function index()
    {
        try {
            // الحصول على إحصائيات المخزون
            $stats = $this->inventoryService->getDashboardStats();

            $data = [
                'title' => 'وحدة إدارة المخزون',
                'stats' => $stats,
                'breadcrumb' => [
                    ['title' => 'المخزون', 'active' => true]
                ]
            ];

            // تحميل الواجهة مباشرة
            $viewPath = __DIR__ . '/../Views/dashboard/index.php';
            if (file_exists($viewPath)) {
                extract($data);
                include $viewPath;
            } else {
                echo "<h1>وحدة المخزون تعمل!</h1>";
                echo "<p>مسار الواجهة: $viewPath</p>";
                echo "<p>الملف موجود: " . (file_exists($viewPath) ? 'نعم' : 'لا') . "</p>";
                echo "<pre>" . print_r($stats, true) . "</pre>";
            }

        } catch (Exception $e) {
            echo "<h1>خطأ في وحدة المخزون</h1>";
            echo "<p>" . $e->getMessage() . "</p>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
        }
    }

    /**
     * عرض لوحة تحكم المخزون
     */
    public function dashboard()
    {
        return $this->index();
    }
}
