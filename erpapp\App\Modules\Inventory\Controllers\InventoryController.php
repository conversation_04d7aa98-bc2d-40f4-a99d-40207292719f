<?php
namespace App\Modules\Inventory\Controllers;

use App\Modules\Inventory\Services\InventoryService;

/**
 * Inventory Controller - المتحكم الرئيسي للمخزون
 */
class InventoryController 
{
    /**
     * Route parameters
     */
    protected $params = [];

    /**
     * Inventory service
     */
    protected $inventoryService;

    /**
     * Constructor
     */
    public function __construct($params = []) 
    {
        $this->params = $params;
        $this->inventoryService = new InventoryService();

        // التحقق من تسجيل الدخول
        if (!is_logged_in()) {
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'غير مصرح']);
                exit;
            } else {
                redirect(base_url('login'));
            }
        }
    }

    /**
     * عرض لوحة تحكم المخزون الرئيسية
     */
    public function index() 
    {
        try {
            // الحصول على إحصائيات المخزون
            $stats = $this->inventoryService->getDashboardStats();
            
            $data = [
                'title' => 'وحدة إدارة المخزون',
                'stats' => $stats,
                'breadcrumb' => [
                    ['title' => 'المخزون', 'active' => true]
                ]
            ];

            view('Inventory::dashboard/index', $data);

        } catch (Exception $e) {
            flash('inventory_error', 'حدث خطأ أثناء تحميل لوحة التحكم: ' . $e->getMessage(), 'danger');
            redirect(base_url('dashboard'));
        }
    }

    /**
     * عرض لوحة تحكم المخزون
     */
    public function dashboard() 
    {
        return $this->index();
    }
}
