<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title">
                    <i class="fas fa-plus-circle"></i> <?= __('إضافة منتج جديد') ?>
                </h1>
                <a href="<?= base_url('inventory/products') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> <?= __('العودة إلى قائمة المنتجات') ?>
                </a>
            </div>
        </div>
    </div>

    <?php display_flash('product_error'); ?>

    <div class="row">
        <div class="col-lg-10 mx-auto">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-boxes me-2"></i> <?= __('إضافة منتج جديد') ?>
                    </h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('inventory/products/store') ?>" method="post" enctype="multipart/form-data" id="product-form">
                        <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">

                        <!-- المعلومات الأساسية -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="product_code" class="form-label"><?= __('كود المنتج') ?> <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="product_code" name="product_code" required>
                                <div class="form-text"><?= __('كود فريد للمنتج') ?></div>
                            </div>
                            <div class="col-md-6">
                                <label for="product_name" class="form-label"><?= __('اسم المنتج') ?> <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="product_name" name="product_name" required>
                                <div class="form-text"><?= __('اسم المنتج باللغة العربية') ?></div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="category_id" class="form-label"><?= __('الفئة') ?> <span class="text-danger">*</span></label>
                                <select class="form-select" id="category_id" name="category_id" required>
                                    <option value=""><?= __('اختر الفئة') ?></option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?= $category['category_id'] ?>"><?= $category['category_name'] ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="unit_id" class="form-label"><?= __('وحدة القياس') ?> <span class="text-danger">*</span></label>
                                <select class="form-select" id="unit_id" name="unit_id" required>
                                    <option value=""><?= __('اختر وحدة القياس') ?></option>
                                    <?php foreach ($units as $unit): ?>
                                        <option value="<?= $unit['unit_id'] ?>"><?= $unit['unit_name'] ?> (<?= $unit['unit_symbol'] ?>)</option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label"><?= __('وصف المنتج') ?></label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                            <div class="form-text"><?= __('وصف تفصيلي للمنتج') ?></div>
                        </div>

                        <!-- الأسعار -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="cost_price" class="form-label"><?= __('سعر التكلفة') ?> <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="cost_price" name="cost_price" step="0.01" min="0" required>
                                <div class="form-text"><?= __('سعر شراء المنتج') ?></div>
                            </div>
                            <div class="col-md-6">
                                <label for="selling_price" class="form-label"><?= __('سعر البيع') ?> <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="selling_price" name="selling_price" step="0.01" min="0" required>
                                <div class="form-text"><?= __('سعر بيع المنتج') ?></div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="tax_rate" class="form-label"><?= __('معدل الضريبة (%)') ?></label>
                                <input type="number" class="form-control" id="tax_rate" name="tax_rate" step="0.01" min="0" max="100" value="15">
                                <div class="form-text"><?= __('معدل ضريبة القيمة المضافة') ?></div>
                            </div>
                            <div class="col-md-6">
                                <label for="profit_margin" class="form-label"><?= __('هامش الربح (%)') ?></label>
                                <input type="number" class="form-control" id="profit_margin" name="profit_margin" step="0.01" min="0" readonly>
                                <div class="form-text"><?= __('يتم حسابه تلقائياً') ?></div>
                            </div>
                        </div>

                        <!-- إعدادات المخزون -->
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="track_inventory" name="track_inventory" value="1" checked>
                                <label class="form-check-label" for="track_inventory">
                                    <?= __('تتبع المخزون') ?>
                                </label>
                                <div class="form-text"><?= __('تفعيل تتبع كميات المخزون لهذا المنتج') ?></div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="min_stock_level" class="form-label"><?= __('الحد الأدنى للمخزون') ?></label>
                                <input type="number" class="form-control" id="min_stock_level" name="min_stock_level" step="0.01" min="0" value="0">
                                <div class="form-text"><?= __('تنبيه عند الوصول لهذا المستوى') ?></div>
                            </div>
                            <div class="col-md-4">
                                <label for="max_stock_level" class="form-label"><?= __('الحد الأقصى للمخزون') ?></label>
                                <input type="number" class="form-control" id="max_stock_level" name="max_stock_level" step="0.01" min="0">
                                <div class="form-text"><?= __('الحد الأقصى المسموح تخزينه') ?></div>
                            </div>
                            <div class="col-md-4">
                                <label for="reorder_point" class="form-label"><?= __('نقطة إعادة الطلب') ?></label>
                                <input type="number" class="form-control" id="reorder_point" name="reorder_point" step="0.01" min="0" value="0">
                                <div class="form-text"><?= __('طلب مخزون جديد عند هذا المستوى') ?></div>
                            </div>
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="weight" class="form-label"><?= __('الوزن (كجم)') ?></label>
                                <input type="number" class="form-control" id="weight" name="weight" step="0.001" min="0">
                            </div>
                            <div class="col-md-6">
                                <label for="dimensions" class="form-label"><?= __('الأبعاد') ?></label>
                                <input type="text" class="form-control" id="dimensions" name="dimensions" placeholder="<?= __('الطول × العرض × الارتفاع') ?>">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="product_image" class="form-label"><?= __('صورة المنتج') ?></label>
                                <input type="file" class="form-control" id="product_image" name="product_image" accept="image/jpeg,image/png,image/gif">
                                <div class="form-text"><?= __('الحد الأقصى لحجم الملف: 5 ميجابايت. الصيغ المدعومة: JPG, PNG, GIF') ?></div>
                                <div class="mt-2" id="image-preview"></div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label"><?= __('حالة المنتج') ?></label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" checked>
                                    <label class="form-check-label" for="is_active">
                                        <?= __('منتج نشط') ?>
                                    </label>
                                    <div class="form-text"><?= __('المنتجات النشطة فقط تظهر في قوائم البيع') ?></div>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <a href="<?= base_url('inventory/products') ?>" class="btn btn-light me-md-2">
                                <i class="fas fa-times me-1"></i> <?= __('إلغاء') ?>
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> <?= __('حفظ المنتج') ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من صحة النموذج قبل الإرسال
    const form = document.getElementById('product-form');
    form.addEventListener('submit', function(event) {
        let isValid = true;

        // التحقق من كود المنتج
        const productCode = document.getElementById('product_code').value.trim();
        if (productCode === '') {
            isValid = false;
            document.getElementById('product_code').classList.add('is-invalid');
        } else {
            document.getElementById('product_code').classList.remove('is-invalid');
        }

        // التحقق من اسم المنتج
        const productName = document.getElementById('product_name').value.trim();
        if (productName === '') {
            isValid = false;
            document.getElementById('product_name').classList.add('is-invalid');
        } else {
            document.getElementById('product_name').classList.remove('is-invalid');
        }

        // التحقق من الأسعار
        const costPrice = parseFloat(document.getElementById('cost_price').value) || 0;
        const sellingPrice = parseFloat(document.getElementById('selling_price').value) || 0;
        
        if (costPrice <= 0) {
            isValid = false;
            document.getElementById('cost_price').classList.add('is-invalid');
        } else {
            document.getElementById('cost_price').classList.remove('is-invalid');
        }

        if (sellingPrice <= 0) {
            isValid = false;
            document.getElementById('selling_price').classList.add('is-invalid');
        } else {
            document.getElementById('selling_price').classList.remove('is-invalid');
        }

        // تحذير إذا كان سعر البيع أقل من التكلفة
        if (sellingPrice > 0 && costPrice > 0 && sellingPrice < costPrice) {
            if (!confirm('<?= __('سعر البيع أقل من سعر التكلفة. هل تريد المتابعة؟') ?>')) {
                isValid = false;
            }
        }

        if (!isValid) {
            event.preventDefault();
        }
    });

    // حساب هامش الربح تلقائياً
    function calculateProfitMargin() {
        const costPrice = parseFloat(document.getElementById('cost_price').value) || 0;
        const sellingPrice = parseFloat(document.getElementById('selling_price').value) || 0;

        if (costPrice > 0) {
            const margin = ((sellingPrice - costPrice) / costPrice * 100).toFixed(2);
            document.getElementById('profit_margin').value = margin;
        } else {
            document.getElementById('profit_margin').value = '0';
        }
    }

    // ربط الأحداث
    document.getElementById('cost_price').addEventListener('input', calculateProfitMargin);
    document.getElementById('selling_price').addEventListener('input', calculateProfitMargin);

    // معاينة الصورة قبل الرفع
    const productImage = document.getElementById('product_image');
    const imagePreview = document.getElementById('image-preview');

    productImage.addEventListener('change', function() {
        imagePreview.innerHTML = '';

        if (this.files && this.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = document.createElement('img');
                img.src = e.target.result;
                img.classList.add('img-thumbnail', 'mt-2');
                img.style.maxHeight = '150px';
                imagePreview.appendChild(img);
            };
            reader.readAsDataURL(this.files[0]);
        }
    });
});
</script>
