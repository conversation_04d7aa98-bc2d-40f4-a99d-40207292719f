<?php
namespace App\Modules\Inventory\Services;

use App\Modules\Inventory\Models\Product;
use PDO;
use Exception;

/**
 * Inventory Service - خدمة المخزون
 */
class InventoryService
{
    /**
     * Product model
     */
    protected $productModel;

    /**
     * Category model
     */
    protected $categoryModel;

    /**
     * Warehouse model
     */
    protected $warehouseModel;

    /**
     * Stock model
     */
    protected $stockModel;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->productModel = new Product();
        // سيتم إنشاء باقي النماذج لاحقاً
    }

    /**
     * الحصول على إحصائيات لوحة التحكم
     */
    public function getDashboardStats()
    {
        // الحصول على معرف الشركة الحالية من المستخدم المسجل
        $user = current_user();
        $company_id = $user['current_company_id'] ?? null;

        // التحقق من وجود شركة حالية
        if (!$company_id) {
            return [
                'total_products' => 0,
                'active_products' => 0,
                'low_stock_products' => 0,
                'out_of_stock_products' => 0,
                'reorder_products' => 0,
                'total_inventory_value' => 0,
                'today_movements' => 0,
                'error' => 'لم يتم تحديد شركة حالية للمستخدم'
            ];
        }

        $stats = [
            // إحصائيات المنتجات
            'total_products' => 0,
            'active_products' => 0,
            'low_stock_count' => 0,
            'out_of_stock_count' => 0,
            'reorder_count' => 0,

            // إحصائيات الفئات
            'total_categories' => 0,
            'active_categories' => 0,

            // إحصائيات المخازن
            'total_warehouses' => 0,
            'active_warehouses' => 0,

            // إحصائيات القيم
            'total_inventory_value' => 0,
            'total_cost_value' => 0,

            // حركات اليوم
            'today_movements' => 0,
            'today_in_movements' => 0,
            'today_out_movements' => 0
        ];

        try {
            // إحصائيات المنتجات
            $productStats = $this->productModel->getStats($company_id);
            $stats = array_merge($stats, $productStats);

            // إحصائيات إضافية
            $stats['reorder_count'] = $this->getReorderCount($company_id);
            $stats['total_inventory_value'] = $this->getTotalInventoryValue($company_id);
            $stats['total_cost_value'] = $this->getTotalCostValue($company_id);

            // إحصائيات الحركات اليومية
            $movementStats = $this->getTodayMovementStats($company_id);
            $stats = array_merge($stats, $movementStats);

        } catch (Exception $e) {
            error_log('Error getting dashboard stats: ' . $e->getMessage());
        }

        return $stats;
    }

    /**
     * الحصول على عدد المنتجات التي تحتاج إعادة طلب
     */
    private function getReorderCount($company_id)
    {
        try {
            global $db;

            $sql = "SELECT COUNT(*) FROM (
                        SELECT p.product_id
                        FROM inventory_products p
                        LEFT JOIN inventory_stock s ON p.product_id = s.product_id
                        WHERE p.company_id = ? AND p.is_active = 1 AND p.track_inventory = 1
                        GROUP BY p.product_id, p.reorder_point
                        HAVING COALESCE(SUM(s.quantity_on_hand), 0) <= p.reorder_point
                    ) as reorder_products";

            $stmt = $db->prepare($sql);
            $stmt->execute([$company_id]);
            return $stmt->fetchColumn() ?: 0;

        } catch (Exception $e) {
            error_log('Error getting reorder count: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * الحصول على إجمالي قيمة المخزون (بسعر البيع)
     */
    private function getTotalInventoryValue($company_id)
    {
        try {
            global $db;

            $sql = "SELECT SUM(s.quantity_on_hand * p.selling_price) as total_value
                    FROM inventory_stock s
                    JOIN inventory_products p ON s.product_id = p.product_id
                    WHERE s.company_id = ? AND p.is_active = 1";

            $stmt = $db->prepare($sql);
            $stmt->execute([$company_id]);
            return $stmt->fetchColumn() ?: 0;

        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * الحصول على إجمالي قيمة التكلفة
     */
    private function getTotalCostValue($company_id)
    {
        try {
            global $db;

            $sql = "SELECT SUM(s.quantity_on_hand * s.average_cost) as total_cost
                    FROM inventory_stock s
                    JOIN inventory_products p ON s.product_id = p.product_id
                    WHERE s.company_id = ? AND p.is_active = 1";

            $stmt = $db->prepare($sql);
            $stmt->execute([$company_id]);
            return $stmt->fetchColumn() ?: 0;

        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * الحصول على إحصائيات حركات اليوم
     */
    private function getTodayMovementStats($company_id)
    {
        try {
            global $db;

            $today = date('Y-m-d');

            // إجمالي الحركات
            $sql = "SELECT COUNT(*) FROM inventory_movements
                    WHERE company_id = ? AND DATE(movement_date) = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$company_id, $today]);
            $today_movements = $stmt->fetchColumn() ?: 0;

            // حركات الإدخال
            $sql = "SELECT COUNT(*) FROM inventory_movements
                    WHERE company_id = ? AND DATE(movement_date) = ? AND movement_type = 'in'";
            $stmt = $db->prepare($sql);
            $stmt->execute([$company_id, $today]);
            $today_in_movements = $stmt->fetchColumn() ?: 0;

            // حركات الإخراج
            $sql = "SELECT COUNT(*) FROM inventory_movements
                    WHERE company_id = ? AND DATE(movement_date) = ? AND movement_type = 'out'";
            $stmt = $db->prepare($sql);
            $stmt->execute([$company_id, $today]);
            $today_out_movements = $stmt->fetchColumn() ?: 0;

            return [
                'today_movements' => $today_movements,
                'today_in_movements' => $today_in_movements,
                'today_out_movements' => $today_out_movements
            ];

        } catch (Exception $e) {
            return [
                'today_movements' => 0,
                'today_in_movements' => 0,
                'today_out_movements' => 0
            ];
        }
    }

    /**
     * الحصول على المنتجات الأكثر حركة
     */
    public function getTopMovingProducts($company_id, $limit = 10)
    {
        try {
            global $db;

            $sql = "SELECT p.product_id, p.product_name_ar, p.product_code,
                           COUNT(m.movement_id) as movement_count,
                           SUM(CASE WHEN m.movement_type = 'out' THEN m.quantity ELSE 0 END) as total_out
                    FROM inventory_products p
                    LEFT JOIN inventory_movements m ON p.product_id = m.product_id
                        AND m.movement_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                    WHERE p.company_id = ? AND p.is_active = 1
                    GROUP BY p.product_id
                    ORDER BY movement_count DESC, total_out DESC
                    LIMIT ?";

            $stmt = $db->prepare($sql);
            $stmt->execute([$company_id, $limit]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * الحصول على تقرير سريع للمخزون
     */
    public function getQuickStockReport($company_id)
    {
        try {
            global $db;

            $sql = "SELECT
                        c.category_name_ar,
                        COUNT(p.product_id) as product_count,
                        SUM(s.quantity_on_hand) as total_quantity,
                        SUM(s.quantity_on_hand * p.selling_price) as total_value
                    FROM inventory_categories c
                    LEFT JOIN inventory_products p ON c.category_id = p.category_id AND p.is_active = 1
                    LEFT JOIN inventory_stock s ON p.product_id = s.product_id
                    WHERE c.company_id = ? AND c.is_active = 1
                    GROUP BY c.category_id
                    ORDER BY total_value DESC";

            $stmt = $db->prepare($sql);
            $stmt->execute([$company_id]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * التحقق من صحة المخزون
     */
    public function validateInventory($company_id)
    {
        $issues = [];

        try {
            global $db;

            // التحقق من المنتجات بدون أرصدة
            $sql = "SELECT p.product_id, p.product_name_ar
                    FROM inventory_products p
                    LEFT JOIN inventory_stock s ON p.product_id = s.product_id
                    WHERE p.company_id = ? AND p.is_active = 1 AND p.track_inventory = 1
                    AND s.product_id IS NULL";

            $stmt = $db->prepare($sql);
            $stmt->execute([$company_id]);
            $products_without_stock = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (!empty($products_without_stock)) {
                $issues[] = [
                    'type' => 'warning',
                    'title' => 'منتجات بدون أرصدة',
                    'message' => 'يوجد ' . count($products_without_stock) . ' منتج بدون أرصدة مخزون',
                    'data' => $products_without_stock
                ];
            }

            // التحقق من الكميات السالبة
            $sql = "SELECT p.product_name_ar, s.quantity_on_hand, w.warehouse_name_ar
                    FROM inventory_stock s
                    JOIN inventory_products p ON s.product_id = p.product_id
                    JOIN inventory_warehouses w ON s.warehouse_id = w.warehouse_id
                    WHERE s.company_id = ? AND s.quantity_on_hand < 0";

            $stmt = $db->prepare($sql);
            $stmt->execute([$company_id]);
            $negative_stock = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (!empty($negative_stock)) {
                $issues[] = [
                    'type' => 'error',
                    'title' => 'كميات سالبة',
                    'message' => 'يوجد ' . count($negative_stock) . ' رصيد بكمية سالبة',
                    'data' => $negative_stock
                ];
            }

        } catch (Exception $e) {
            $issues[] = [
                'type' => 'error',
                'title' => 'خطأ في التحقق',
                'message' => 'حدث خطأ أثناء التحقق من صحة المخزون: ' . $e->getMessage()
            ];
        }

        return $issues;
    }
}
