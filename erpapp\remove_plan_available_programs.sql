-- إزالة جدول plan_available_programs من قاعدة البيانات
-- هذا الجدول لن يُستخدم في نظام الصلاحيات الجديد

-- 1. التحقق من وجود الجدول
SELECT 'فحص وجود جدول plan_available_programs...' as message;

SELECT 
    TABLE_NAME,
    TABLE_ROWS,
    CREATE_TIME
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'plan_available_programs';

-- 2. عرض البيانات الموجودة (إن وجدت) قبل الحذف
SELECT 'البيانات الموجودة في الجدول (إن وجد):' as message;

-- استخدام IF EXISTS للتحقق من وجود الجدول
SET @table_exists = (
    SELECT COUNT(*) 
    FROM information_schema.TABLES 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'plan_available_programs'
);

-- عرض البيانات إذا كان الجدول موجود
SELECT 
    CASE 
        WHEN @table_exists > 0 THEN 'الجدول موجود - سيتم حذفه'
        ELSE 'الجدول غير موجود'
    END as status;

-- 3. حذف الجدول إذا كان موجود
DROP TABLE IF EXISTS `plan_available_programs`;

-- 4. التأكد من الحذف
SELECT 'تم حذف جدول plan_available_programs بنجاح!' as message;

-- 5. التحقق من عدم وجود الجدول بعد الحذف
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'تم الحذف بنجاح - الجدول غير موجود'
        ELSE 'خطأ - الجدول ما زال موجود'
    END as verification_result
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'plan_available_programs';

-- 6. عرض الجداول المتبقية المتعلقة بالاشتراكات
SELECT 'الجداول المتبقية لنظام الاشتراكات:' as message;

SELECT 
    TABLE_NAME,
    TABLE_ROWS,
    TABLE_COMMENT
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN (
    'subscription_plans',
    'subscriptions', 
    'subscription_features',
    'plan_features',
    'companies',
    'system_modules',
    'company_modules'
)
ORDER BY TABLE_NAME;

-- 7. عرض ملخص نظام الاشتراكات الجديد
SELECT 'ملخص نظام الاشتراكات المحدث:' as info;

SELECT '
نظام الاشتراكات الجديد يعتمد على:

1. subscription_plans - خطط الاشتراك (أساسية، متقدمة، احترافية)
2. subscriptions - اشتراكات الشركات
3. system_modules - الوحدات مع أنواعها (core, addon, premium)
4. company_modules - الوحدات المنزلة للشركات

منطق السماح:
- الخطة الأساسية: core modules فقط
- الخطة المتقدمة: core + addon modules  
- الخطة الاحترافية: core + addon + premium modules
- الفترة التجريبية: core + addon modules

تم إزالة جدول plan_available_programs نهائياً!
' as system_summary;
