<?php
namespace Modules\Accounting\Inventory\Controllers;

use Modules\Accounting\Inventory\Models\Category;

/**
 * Category Controller
 * متحكم فئات المنتجات
 */
class CategoryController {

    private $categoryModel;

    public function __construct() {
        $this->categoryModel = new Category();
    }

    /**
     * عرض قائمة الفئات
     */
    public function index() {
        try {
            $company_id = get_user_company_id();
            $categories = $this->categoryModel->getAll($company_id, false); // جميع الفئات بما في ذلك غير النشطة

            $data = [
                'title' => 'إدارة فئات المنتجات',
                'categories' => $categories,
                'total_count' => count($categories)
            ];

            view('Accounting.Inventory::categories/index', $data);

        } catch (Exception $e) {
            error_log("Error in CategoryController::index: " . $e->getMessage());
            show_error('حدث خطأ أثناء تحميل الفئات');
        }
    }

    /**
     * عرض تفاصيل فئة
     */
    public function show($category_id) {
        try {
            $company_id = get_user_company_id();
            $category = $this->categoryModel->getById($category_id, $company_id);

            if (!$category) {
                show_404('الفئة غير موجودة');
                return;
            }

            // الحصول على الفئات الفرعية
            $subcategories = $this->categoryModel->getSubCategories($category_id, $company_id);

            $data = [
                'title' => 'تفاصيل الفئة: ' . $category['category_name_ar'],
                'category' => $category,
                'subcategories' => $subcategories
            ];

            load_view('Modules/Accounting/Inventory/Views/categories/show', $data);

        } catch (Exception $e) {
            error_log("Error in CategoryController::show: " . $e->getMessage());
            show_error('حدث خطأ أثناء تحميل تفاصيل الفئة');
        }
    }

    /**
     * عرض نموذج إنشاء فئة جديدة
     */
    public function create() {
        try {
            $company_id = get_user_company_id();
            $main_categories = $this->categoryModel->getMainCategories($company_id);

            $data = [
                'title' => 'إضافة فئة جديدة',
                'main_categories' => $main_categories,
                'category' => [] // فئة فارغة للنموذج
            ];

            view('Accounting.Inventory::categories/create', $data);

        } catch (Exception $e) {
            error_log("Error in CategoryController::create: " . $e->getMessage());
            show_error('حدث خطأ أثناء تحميل نموذج إنشاء الفئة');
        }
    }

    /**
     * حفظ فئة جديدة
     */
    public function store() {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                redirect('/inventory/categories');
                return;
            }

            $company_id = get_user_company_id();
            $user_id = get_current_user_id();

            // التحقق من صحة البيانات
            $validation_errors = $this->validateCategoryData($_POST);

            if (!empty($validation_errors)) {
                set_flash_message('error', implode('<br>', $validation_errors));
                $this->create();
                return;
            }

            // التحقق من عدم تكرار كود الفئة
            if ($this->categoryModel->isCategoryCodeExists($_POST['category_code'], $company_id)) {
                set_flash_message('error', 'كود الفئة موجود مسبقاً');
                $this->create();
                return;
            }

            // إعداد بيانات الفئة
            $category_data = [
                'company_id' => $company_id,
                'parent_category_id' => !empty($_POST['parent_category_id']) ? $_POST['parent_category_id'] : null,
                'category_code' => $_POST['category_code'],
                'category_name_ar' => $_POST['category_name_ar'],
                'category_name_en' => $_POST['category_name_en'] ?? null,
                'description_ar' => $_POST['description_ar'] ?? null,
                'description_en' => $_POST['description_en'] ?? null,
                'image_url' => $_POST['image_url'] ?? null,
                'display_order' => (int)($_POST['display_order'] ?? 0),
                'is_active' => isset($_POST['is_active']) ? 1 : 0,
                'created_by' => $user_id
            ];

            $category_id = $this->categoryModel->create($category_data);

            if ($category_id) {
                set_flash_message('success', 'تم إنشاء الفئة بنجاح');
                redirect('/inventory/categories/' . $category_id);
            } else {
                set_flash_message('error', 'حدث خطأ أثناء إنشاء الفئة');
                $this->create();
            }

        } catch (Exception $e) {
            error_log("Error in CategoryController::store: " . $e->getMessage());
            set_flash_message('error', 'حدث خطأ أثناء حفظ الفئة');
            $this->create();
        }
    }

    /**
     * عرض نموذج تعديل فئة
     */
    public function edit($category_id) {
        try {
            $company_id = get_user_company_id();
            $category = $this->categoryModel->getById($category_id, $company_id);

            if (!$category) {
                show_404('الفئة غير موجودة');
                return;
            }

            // الحصول على الفئات الرئيسية (باستثناء الفئة الحالية وفئاتها الفرعية)
            $main_categories = $this->categoryModel->getMainCategories($company_id);
            $main_categories = array_filter($main_categories, function($cat) use ($category_id) {
                return $cat['category_id'] != $category_id;
            });

            $data = [
                'title' => 'تعديل الفئة: ' . $category['category_name_ar'],
                'category' => $category,
                'main_categories' => $main_categories
            ];

            load_view('Modules/Accounting/Inventory/Views/categories/edit', $data);

        } catch (Exception $e) {
            error_log("Error in CategoryController::edit: " . $e->getMessage());
            show_error('حدث خطأ أثناء تحميل نموذج تعديل الفئة');
        }
    }

    /**
     * تحديث فئة
     */
    public function update($category_id) {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                redirect('/inventory/categories');
                return;
            }

            $company_id = get_user_company_id();
            $user_id = get_current_user_id();

            // التحقق من وجود الفئة
            $category = $this->categoryModel->getById($category_id, $company_id);
            if (!$category) {
                show_404('الفئة غير موجودة');
                return;
            }

            // التحقق من صحة البيانات
            $validation_errors = $this->validateCategoryData($_POST);

            if (!empty($validation_errors)) {
                set_flash_message('error', implode('<br>', $validation_errors));
                $this->edit($category_id);
                return;
            }

            // التحقق من عدم تكرار كود الفئة
            if ($this->categoryModel->isCategoryCodeExists($_POST['category_code'], $company_id, $category_id)) {
                set_flash_message('error', 'كود الفئة موجود مسبقاً');
                $this->edit($category_id);
                return;
            }

            // التحقق من عدم جعل الفئة فرعية لنفسها
            if (!empty($_POST['parent_category_id']) && $_POST['parent_category_id'] == $category_id) {
                set_flash_message('error', 'لا يمكن جعل الفئة فرعية لنفسها');
                $this->edit($category_id);
                return;
            }

            // إعداد بيانات الفئة
            $category_data = [
                'parent_category_id' => !empty($_POST['parent_category_id']) ? $_POST['parent_category_id'] : null,
                'category_code' => $_POST['category_code'],
                'category_name_ar' => $_POST['category_name_ar'],
                'category_name_en' => $_POST['category_name_en'] ?? null,
                'description_ar' => $_POST['description_ar'] ?? null,
                'description_en' => $_POST['description_en'] ?? null,
                'image_url' => $_POST['image_url'] ?? null,
                'display_order' => (int)($_POST['display_order'] ?? 0),
                'is_active' => isset($_POST['is_active']) ? 1 : 0,
                'updated_by' => $user_id
            ];

            $success = $this->categoryModel->update($category_id, $category_data, $company_id);

            if ($success) {
                set_flash_message('success', 'تم تحديث الفئة بنجاح');
                redirect('/inventory/categories/' . $category_id);
            } else {
                set_flash_message('error', 'حدث خطأ أثناء تحديث الفئة');
                $this->edit($category_id);
            }

        } catch (Exception $e) {
            error_log("Error in CategoryController::update: " . $e->getMessage());
            set_flash_message('error', 'حدث خطأ أثناء تحديث الفئة');
            $this->edit($category_id);
        }
    }

    /**
     * حذف فئة
     */
    public function delete($category_id) {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                redirect('/inventory/categories');
                return;
            }

            $company_id = get_user_company_id();

            // التحقق من وجود الفئة
            $category = $this->categoryModel->getById($category_id, $company_id);
            if (!$category) {
                set_flash_message('error', 'الفئة غير موجودة');
                redirect('/inventory/categories');
                return;
            }

            $success = $this->categoryModel->delete($category_id, $company_id);

            if ($success) {
                set_flash_message('success', 'تم حذف الفئة بنجاح');
            } else {
                set_flash_message('error', 'لا يمكن حذف الفئة لوجود منتجات أو فئات فرعية مرتبطة بها');
            }

            redirect('/inventory/categories');

        } catch (Exception $e) {
            error_log("Error in CategoryController::delete: " . $e->getMessage());
            set_flash_message('error', 'حدث خطأ أثناء حذف الفئة');
            redirect('/inventory/categories');
        }
    }

    /**
     * عرض الفئات في شكل هرمي (JSON)
     */
    public function hierarchy() {
        try {
            $company_id = get_user_company_id();
            $categories = $this->categoryModel->getHierarchical($company_id);

            header('Content-Type: application/json');
            echo json_encode($categories, JSON_UNESCAPED_UNICODE);

        } catch (Exception $e) {
            error_log("Error in CategoryController::hierarchy: " . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['error' => 'حدث خطأ أثناء تحميل الفئات'], JSON_UNESCAPED_UNICODE);
        }
    }

    /**
     * الحصول على الفئات الفرعية (AJAX)
     */
    public function getSubCategories($parent_id) {
        try {
            $company_id = get_user_company_id();
            $subcategories = $this->categoryModel->getSubCategories($parent_id, $company_id);

            header('Content-Type: application/json');
            echo json_encode($subcategories, JSON_UNESCAPED_UNICODE);

        } catch (Exception $e) {
            error_log("Error in CategoryController::getSubCategories: " . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['error' => 'حدث خطأ أثناء تحميل الفئات الفرعية'], JSON_UNESCAPED_UNICODE);
        }
    }

    /**
     * التحقق من صحة بيانات الفئة
     */
    private function validateCategoryData($data) {
        $errors = [];

        if (empty($data['category_code'])) {
            $errors[] = 'كود الفئة مطلوب';
        }

        if (empty($data['category_name_ar'])) {
            $errors[] = 'اسم الفئة بالعربية مطلوب';
        }

        if (!empty($data['display_order']) && !is_numeric($data['display_order'])) {
            $errors[] = 'ترتيب العرض يجب أن يكون رقماً';
        }

        return $errors;
    }
}
