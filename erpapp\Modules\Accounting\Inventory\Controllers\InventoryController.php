<?php
namespace Modules\Accounting\Inventory\Controllers;

use Modules\Accounting\Inventory\Models\Product;
use Modules\Accounting\Inventory\Models\Category;
use Modules\Accounting\Inventory\Models\Unit;
use Modules\Accounting\Inventory\Models\Warehouse;
use Modules\Accounting\Inventory\Models\Stock;

/**
 * Main Inventory Controller
 * المتحكم الرئيسي لوحدة المخزون
 */
class InventoryController {
    
    private $productModel;
    private $categoryModel;
    private $unitModel;
    private $warehouseModel;
    private $stockModel;

    public function __construct() {
        $this->productModel = new Product();
        $this->categoryModel = new Category();
        $this->unitModel = new Unit();
        $this->warehouseModel = new Warehouse();
        $this->stockModel = new Stock();
    }

    /**
     * الصفحة الرئيسية لوحدة المخزون
     */
    public function index() {
        try {
            $company_id = get_user_company_id();

            // إحصائيات عامة
            $stats = $this->getInventoryStatistics($company_id);

            $data = [
                'title' => 'وحدة إدارة المخزون',
                'stats' => $stats
            ];

            load_view('Modules/Accounting/Inventory/Views/dashboard/index', $data);

        } catch (Exception $e) {
            error_log("Error in InventoryController::index: " . $e->getMessage());
            show_error('حدث خطأ أثناء تحميل لوحة تحكم المخزون');
        }
    }

    /**
     * لوحة التحكم مع الإحصائيات
     */
    public function dashboard() {
        try {
            $company_id = get_user_company_id();

            // إحصائيات مفصلة
            $stats = $this->getDetailedStatistics($company_id);

            // المنتجات التي تحتاج إعادة طلب
            $reorder_products = $this->productModel->getProductsNeedReorder($company_id);

            // المنتجات النافدة
            $out_of_stock = $this->stockModel->getAll($company_id, ['out_of_stock' => true]);

            // أحدث الحركات (سيتم إضافتها لاحقاً)
            $recent_movements = [];

            $data = [
                'title' => 'لوحة تحكم المخزون',
                'stats' => $stats,
                'reorder_products' => array_slice($reorder_products, 0, 10), // أول 10 منتجات
                'out_of_stock' => array_slice($out_of_stock, 0, 10), // أول 10 منتجات
                'recent_movements' => $recent_movements
            ];

            load_view('Modules/Accounting/Inventory/Views/dashboard/dashboard', $data);

        } catch (Exception $e) {
            error_log("Error in InventoryController::dashboard: " . $e->getMessage());
            show_error('حدث خطأ أثناء تحميل لوحة تحكم المخزون');
        }
    }

    /**
     * تقارير المخزون
     */
    public function reports() {
        try {
            $company_id = get_user_company_id();

            $data = [
                'title' => 'تقارير المخزون',
                'report_types' => [
                    'stock_summary' => 'ملخص أرصدة المخزون',
                    'stock_valuation' => 'تقييم المخزون',
                    'movement_report' => 'تقرير حركات المخزون',
                    'reorder_report' => 'تقرير إعادة الطلب',
                    'out_of_stock_report' => 'تقرير المنتجات النافدة',
                    'warehouse_report' => 'تقرير المخازن',
                    'category_report' => 'تقرير الفئات'
                ]
            ];

            load_view('Modules/Accounting/Inventory/Views/reports/index', $data);

        } catch (Exception $e) {
            error_log("Error in InventoryController::reports: " . $e->getMessage());
            show_error('حدث خطأ أثناء تحميل صفحة التقارير');
        }
    }

    /**
     * إعدادات وحدة المخزون
     */
    public function settings() {
        try {
            $company_id = get_user_company_id();

            $data = [
                'title' => 'إعدادات المخزون',
                'settings' => [
                    'auto_reorder' => false,
                    'track_serial_numbers' => false,
                    'allow_negative_stock' => false,
                    'default_cost_method' => 'average',
                    'low_stock_alert' => true,
                    'out_of_stock_alert' => true
                ]
            ];

            load_view('Modules/Accounting/Inventory/Views/settings/index', $data);

        } catch (Exception $e) {
            error_log("Error in InventoryController::settings: " . $e->getMessage());
            show_error('حدث خطأ أثناء تحميل إعدادات المخزون');
        }
    }

    /**
     * البحث السريع في المخزون
     */
    public function search() {
        try {
            $company_id = get_user_company_id();
            $query = $_GET['q'] ?? '';

            if (empty($query)) {
                header('Content-Type: application/json');
                echo json_encode([], JSON_UNESCAPED_UNICODE);
                return;
            }

            // البحث في المنتجات
            $products = $this->productModel->getAll($company_id, ['search' => $query]);
            $products = array_slice($products, 0, 10); // أول 10 نتائج

            $results = [];
            foreach ($products as $product) {
                $results[] = [
                    'id' => $product['product_id'],
                    'type' => 'product',
                    'title' => $product['product_name_ar'],
                    'subtitle' => $product['product_code'],
                    'url' => '/inventory/products/' . $product['product_id'],
                    'stock' => $product['total_stock'] ?? 0
                ];
            }

            header('Content-Type: application/json');
            echo json_encode($results, JSON_UNESCAPED_UNICODE);

        } catch (Exception $e) {
            error_log("Error in InventoryController::search: " . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['error' => 'حدث خطأ أثناء البحث'], JSON_UNESCAPED_UNICODE);
        }
    }

    /**
     * الحصول على إحصائيات المخزون الأساسية
     */
    private function getInventoryStatistics($company_id) {
        try {
            // عدد المنتجات
            $products = $this->productModel->getAll($company_id);
            $total_products = count($products);
            $active_products = count(array_filter($products, function($p) { return $p['is_active']; }));

            // عدد الفئات
            $categories = $this->categoryModel->getAll($company_id);
            $total_categories = count($categories);

            // عدد المخازن
            $warehouses = $this->warehouseModel->getAll($company_id);
            $total_warehouses = count($warehouses);

            // المنتجات التي تحتاج إعادة طلب
            $reorder_products = $this->productModel->getProductsNeedReorder($company_id);
            $reorder_count = count($reorder_products);

            // المنتجات النافدة
            $out_of_stock = $this->stockModel->getAll($company_id, ['out_of_stock' => true]);
            $out_of_stock_count = count($out_of_stock);

            return [
                'total_products' => $total_products,
                'active_products' => $active_products,
                'total_categories' => $total_categories,
                'total_warehouses' => $total_warehouses,
                'reorder_count' => $reorder_count,
                'out_of_stock_count' => $out_of_stock_count
            ];

        } catch (Exception $e) {
            error_log("Error in getInventoryStatistics: " . $e->getMessage());
            return [
                'total_products' => 0,
                'active_products' => 0,
                'total_categories' => 0,
                'total_warehouses' => 0,
                'reorder_count' => 0,
                'out_of_stock_count' => 0
            ];
        }
    }

    /**
     * الحصول على إحصائيات مفصلة
     */
    private function getDetailedStatistics($company_id) {
        try {
            $basic_stats = $this->getInventoryStatistics($company_id);

            // إحصائيات إضافية
            $stocks = $this->stockModel->getAll($company_id);
            
            $total_stock_value = 0;
            $total_quantity = 0;
            $low_stock_count = 0;

            foreach ($stocks as $stock) {
                $total_stock_value += $stock['total_value'] ?? 0;
                $total_quantity += $stock['quantity_on_hand'] ?? 0;
                
                if (($stock['quantity_available'] ?? 0) <= ($stock['min_stock_level'] ?? 0)) {
                    $low_stock_count++;
                }
            }

            return array_merge($basic_stats, [
                'total_stock_value' => $total_stock_value,
                'total_quantity' => $total_quantity,
                'low_stock_count' => $low_stock_count,
                'average_stock_value' => $basic_stats['active_products'] > 0 ? $total_stock_value / $basic_stats['active_products'] : 0
            ]);

        } catch (Exception $e) {
            error_log("Error in getDetailedStatistics: " . $e->getMessage());
            return $this->getInventoryStatistics($company_id);
        }
    }

    /**
     * تصدير البيانات
     */
    public function export($type = 'products') {
        try {
            $company_id = get_user_company_id();

            switch ($type) {
                case 'products':
                    $data = $this->productModel->getAll($company_id);
                    $filename = 'products_' . date('Y-m-d');
                    break;
                case 'stock':
                    $data = $this->stockModel->getAll($company_id);
                    $filename = 'stock_' . date('Y-m-d');
                    break;
                case 'categories':
                    $data = $this->categoryModel->getAll($company_id);
                    $filename = 'categories_' . date('Y-m-d');
                    break;
                case 'warehouses':
                    $data = $this->warehouseModel->getAll($company_id);
                    $filename = 'warehouses_' . date('Y-m-d');
                    break;
                default:
                    set_flash_message('error', 'نوع التصدير غير صحيح');
                    redirect('/inventory');
                    return;
            }

            // تصدير كـ CSV
            header('Content-Type: text/csv; charset=utf-8');
            header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
            
            $output = fopen('php://output', 'w');
            
            // إضافة BOM للدعم العربي
            fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
            
            if (!empty($data)) {
                // كتابة العناوين
                fputcsv($output, array_keys($data[0]));
                
                // كتابة البيانات
                foreach ($data as $row) {
                    fputcsv($output, $row);
                }
            }
            
            fclose($output);

        } catch (Exception $e) {
            error_log("Error in InventoryController::export: " . $e->getMessage());
            set_flash_message('error', 'حدث خطأ أثناء تصدير البيانات');
            redirect('/inventory');
        }
    }
}
