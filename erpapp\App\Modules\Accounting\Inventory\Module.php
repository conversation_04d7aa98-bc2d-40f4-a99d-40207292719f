<?php
namespace Modules\Accounting\Inventory;

use App\Core\Module as BaseModule;

/**
 * وحدة المخزون
 */
class Module extends BaseModule
{
    /**
     * اسم الوحدة
     */
    protected $name = 'Inventory';

    /**
     * وصف الوحدة
     */
    protected $description = 'إدارة المخزون والمنتجات';

    /**
     * إصدار الوحدة
     */
    protected $version = '1.0.0';

    /**
     * الوحدات المطلوبة
     */
    protected $dependencies = ['System.Companies', 'System.Users'];

    /**
     * تسجيل المسارات الخاصة بالوحدة
     */
    public function registerRoutes()
    {
        // المسارات الرئيسية
        add_route('GET', '/inventory', 'Modules\Accounting\Inventory\Controllers\InventoryController@index');
        add_route('GET', '/inventory/dashboard', 'Modules\Accounting\Inventory\Controllers\InventoryController@dashboard');
        add_route('GET', '/inventory/search', 'Modules\Accounting\Inventory\Controllers\InventoryController@search');
        add_route('GET', '/inventory/reports', 'Modules\Accounting\Inventory\Controllers\InventoryController@reports');
        add_route('GET', '/inventory/settings', 'Modules\Accounting\Inventory\Controllers\InventoryController@settings');
        add_route('GET', '/inventory/export/{type}', 'Modules\Accounting\Inventory\Controllers\InventoryController@export');

        // مسارات المنتجات
        add_route('GET', '/inventory/products', 'Modules\Accounting\Inventory\Controllers\ProductController@index');
        add_route('GET', '/inventory/products/create', 'Modules\Accounting\Inventory\Controllers\ProductController@create');
        add_route('POST', '/inventory/products/store', 'Modules\Accounting\Inventory\Controllers\ProductController@store');
        add_route('GET', '/inventory/products/{id}', 'Modules\Accounting\Inventory\Controllers\ProductController@show');
        add_route('GET', '/inventory/products/{id}/edit', 'Modules\Accounting\Inventory\Controllers\ProductController@edit');
        add_route('POST', '/inventory/products/{id}/update', 'Modules\Accounting\Inventory\Controllers\ProductController@update');
        add_route('POST', '/inventory/products/{id}/delete', 'Modules\Accounting\Inventory\Controllers\ProductController@delete');
        add_route('GET', '/inventory/products/reorder', 'Modules\Accounting\Inventory\Controllers\ProductController@reorder');

        // مسارات الفئات
        add_route('GET', '/inventory/categories', 'Modules\Accounting\Inventory\Controllers\CategoryController@index');
        add_route('GET', '/inventory/categories/create', 'Modules\Accounting\Inventory\Controllers\CategoryController@create');
        add_route('POST', '/inventory/categories/store', 'Modules\Accounting\Inventory\Controllers\CategoryController@store');
        add_route('GET', '/inventory/categories/{id}', 'Modules\Accounting\Inventory\Controllers\CategoryController@show');
        add_route('GET', '/inventory/categories/{id}/edit', 'Modules\Accounting\Inventory\Controllers\CategoryController@edit');
        add_route('POST', '/inventory/categories/{id}/update', 'Modules\Accounting\Inventory\Controllers\CategoryController@update');
        add_route('POST', '/inventory/categories/{id}/delete', 'Modules\Accounting\Inventory\Controllers\CategoryController@delete');
        add_route('GET', '/inventory/categories/hierarchy', 'Modules\Accounting\Inventory\Controllers\CategoryController@hierarchy');
        add_route('GET', '/inventory/categories/{id}/subcategories', 'Modules\Accounting\Inventory\Controllers\CategoryController@getSubCategories');

        // مسارات وحدات القياس
        add_route('GET', '/inventory/units', 'Modules\Accounting\Inventory\Controllers\UnitController@index');
        add_route('GET', '/inventory/units/create', 'Modules\Accounting\Inventory\Controllers\UnitController@create');
        add_route('POST', '/inventory/units/store', 'Modules\Accounting\Inventory\Controllers\UnitController@store');
        add_route('GET', '/inventory/units/{id}', 'Modules\Accounting\Inventory\Controllers\UnitController@show');
        add_route('GET', '/inventory/units/{id}/edit', 'Modules\Accounting\Inventory\Controllers\UnitController@edit');
        add_route('POST', '/inventory/units/{id}/update', 'Modules\Accounting\Inventory\Controllers\UnitController@update');
        add_route('POST', '/inventory/units/{id}/delete', 'Modules\Accounting\Inventory\Controllers\UnitController@delete');
        add_route('POST', '/inventory/units/convert', 'Modules\Accounting\Inventory\Controllers\UnitController@convert');

        // مسارات المخازن
        add_route('GET', '/inventory/warehouses', 'Modules\Accounting\Inventory\Controllers\WarehouseController@index');
        add_route('GET', '/inventory/warehouses/create', 'Modules\Accounting\Inventory\Controllers\WarehouseController@create');
        add_route('POST', '/inventory/warehouses/store', 'Modules\Accounting\Inventory\Controllers\WarehouseController@store');
        add_route('GET', '/inventory/warehouses/{id}', 'Modules\Accounting\Inventory\Controllers\WarehouseController@show');
        add_route('GET', '/inventory/warehouses/{id}/edit', 'Modules\Accounting\Inventory\Controllers\WarehouseController@edit');
        add_route('POST', '/inventory/warehouses/{id}/update', 'Modules\Accounting\Inventory\Controllers\WarehouseController@update');
        add_route('POST', '/inventory/warehouses/{id}/delete', 'Modules\Accounting\Inventory\Controllers\WarehouseController@delete');
        add_route('POST', '/inventory/warehouses/{id}/update-usage', 'Modules\Accounting\Inventory\Controllers\WarehouseController@updateUsage');
        add_route('GET', '/inventory/warehouses/type/{type}', 'Modules\Accounting\Inventory\Controllers\WarehouseController@getByType');

        // مسارات أرصدة المخزون
        add_route('GET', '/inventory/stock', 'Modules\Accounting\Inventory\Controllers\StockController@index');
        add_route('GET', '/inventory/stock/{product_id}/{warehouse_id}', 'Modules\Accounting\Inventory\Controllers\StockController@show');
        add_route('GET', '/inventory/stock/{product_id}/{warehouse_id}/adjust', 'Modules\Accounting\Inventory\Controllers\StockController@adjust');
        add_route('POST', '/inventory/stock/{product_id}/{warehouse_id}/process-adjustment', 'Modules\Accounting\Inventory\Controllers\StockController@processAdjustment');
        add_route('POST', '/inventory/stock/{product_id}/{warehouse_id}/reserve', 'Modules\Accounting\Inventory\Controllers\StockController@reserve');
        add_route('POST', '/inventory/stock/{product_id}/{warehouse_id}/unreserve', 'Modules\Accounting\Inventory\Controllers\StockController@unreserve');
        add_route('GET', '/inventory/stock/product/{product_id}/total', 'Modules\Accounting\Inventory\Controllers\StockController@totalStock');
        add_route('GET', '/inventory/stock/out-of-stock', 'Modules\Accounting\Inventory\Controllers\StockController@outOfStock');
        add_route('GET', '/inventory/stock/need-reorder', 'Modules\Accounting\Inventory\Controllers\StockController@needReorder');
    }

    /**
     * تهيئة الوحدة
     */
    public function boot()
    {
        parent::boot();

        // تسجيل المسارات
        $this->registerRoutes();

        // تسجيل الخدمات
        $this->registerServices();
    }

    /**
     * تسجيل الخدمات
     */
    protected function registerServices()
    {
        // يمكن تسجيل خدمات إضافية هنا
    }

    /**
     * التحقق من صلاحيات الوحدة
     */
    public function hasPermission($user_id, $company_id)
    {
        // التحقق من أن الشركة لديها اشتراك يسمح بوحدة المخزون
        return true;
    }

    /**
     * الحصول على معلومات الوحدة
     */
    public function getInfo()
    {
        return [
            'name' => $this->name,
            'description' => $this->description,
            'version' => $this->version,
            'dependencies' => $this->dependencies,
            'category' => 'Accounting',
            'icon' => 'fas fa-boxes',
            'status' => 'active'
        ];
    }

    /**
     * الحصول على صلاحيات الوحدة
     */
    public function getPermissions()
    {
        return [
            'inventory_view' => 'عرض المخزون',
            'inventory_create' => 'إنشاء عناصر المخزون',
            'inventory_edit' => 'تعديل المخزون',
            'inventory_delete' => 'حذف عناصر المخزون',
            'inventory_reports' => 'تقارير المخزون',
            'inventory_settings' => 'إعدادات المخزون',

            'products_view' => 'عرض المنتجات',
            'products_create' => 'إنشاء منتجات',
            'products_edit' => 'تعديل المنتجات',
            'products_delete' => 'حذف المنتجات',

            'categories_view' => 'عرض الفئات',
            'categories_create' => 'إنشاء فئات',
            'categories_edit' => 'تعديل الفئات',
            'categories_delete' => 'حذف الفئات',

            'units_view' => 'عرض وحدات القياس',
            'units_create' => 'إنشاء وحدات قياس',
            'units_edit' => 'تعديل وحدات القياس',
            'units_delete' => 'حذف وحدات القياس',

            'warehouses_view' => 'عرض المخازن',
            'warehouses_create' => 'إنشاء مخازن',
            'warehouses_edit' => 'تعديل المخازن',
            'warehouses_delete' => 'حذف المخازن',

            'stock_view' => 'عرض أرصدة المخزون',
            'stock_adjust' => 'تعديل أرصدة المخزون',
            'stock_reserve' => 'حجز المخزون',
            'stock_transfer' => 'نقل المخزون'
        ];
    }

    /**
     * الحصول على عناصر القائمة
     */
    public function getMenuItems()
    {
        return [
            [
                'title' => 'إدارة المخزون',
                'icon' => 'fas fa-boxes',
                'url' => '/inventory',
                'permission' => 'inventory_view',
                'children' => [
                    [
                        'title' => 'لوحة التحكم',
                        'icon' => 'fas fa-tachometer-alt',
                        'url' => '/inventory/dashboard',
                        'permission' => 'inventory_view'
                    ],
                    [
                        'title' => 'المنتجات',
                        'icon' => 'fas fa-cube',
                        'url' => '/inventory/products',
                        'permission' => 'products_view'
                    ],
                    [
                        'title' => 'الفئات',
                        'icon' => 'fas fa-tags',
                        'url' => '/inventory/categories',
                        'permission' => 'categories_view'
                    ],
                    [
                        'title' => 'وحدات القياس',
                        'icon' => 'fas fa-ruler',
                        'url' => '/inventory/units',
                        'permission' => 'units_view'
                    ],
                    [
                        'title' => 'المخازن',
                        'icon' => 'fas fa-warehouse',
                        'url' => '/inventory/warehouses',
                        'permission' => 'warehouses_view'
                    ],
                    [
                        'title' => 'أرصدة المخزون',
                        'icon' => 'fas fa-chart-bar',
                        'url' => '/inventory/stock',
                        'permission' => 'stock_view'
                    ],
                    [
                        'title' => 'التقارير',
                        'icon' => 'fas fa-chart-line',
                        'url' => '/inventory/reports',
                        'permission' => 'inventory_reports'
                    ]
                ]
            ]
        ];
    }
}
