<?php
/**
 * سايدبار الوحدات والبرامج
 * يظهر في صفحات الوحدات (inventory, sales, accounting, etc.)
 */

// جلب المستخدم الحالي
$user = current_user();
if (!$user) return;

$companyId = $user['current_company_id'];
if (!$companyId) return;

// جلب الوحدات والبرامج
$modules = getCompanyModulesWithPrograms($companyId);
$currentModule = getCurrentModule();
?>

<!-- Sidebar -->
<aside class="sidebar <?= is_logged_in() && current_user()['sidebar_mode'] == 'hide' ? 'collapsed' : '' ?>" id="sidebar">
    <div class="sidebar-header">
        <a href="<?= base_url('dashboard') ?>" class="sidebar-brand">
            <i class="fas fa-cubes"></i>
            <span class="sidebar-brand-text"><?= APP_NAME ?></span>
        </a>
        <button class="sidebar-toggle" id="sidebarToggle">
            <i class="fas fa-bars"></i>
        </button>
        <!-- زر إغلاق السايدبار على الأجهزة المحمولة -->
        <button class="mobile-sidebar-close" id="mobileSidebarClose">
            <i class="fas fa-times"></i>
        </button>
    </div>

    <ul class="sidebar-menu">
        <?php if (empty($modules)): ?>
        <li class="sidebar-menu-item">
            <div class="sidebar-menu-link" style="color: #6c757d; cursor: default;">
                <i class="fas fa-info-circle sidebar-menu-icon"></i>
                <span class="sidebar-menu-text">لا توجد وحدات منزلة</span>
            </div>
        </li>
        <?php else: ?>

        <?php foreach ($modules as $module): ?>
        <!-- عنوان الوحدة -->
        <li class="sidebar-menu-item">
            <a href="<?= moduleUrl($module['base_url']) ?>"
               class="sidebar-menu-link <?= $currentModule === $module['module_code'] ? 'active' : '' ?>"
               data-title="<?= htmlspecialchars($module['module_name_ar']) ?>"
               <?php if (!empty($module['programs'])): ?>
               data-bs-toggle="collapse"
               data-bs-target="#module<?= $module['module_id'] ?>Submenu"
               aria-expanded="<?= $currentModule === $module['module_code'] ? 'true' : 'false' ?>"
               <?php endif; ?>>
                <i class="<?= htmlspecialchars($module['icon_name']) ?> sidebar-menu-icon"></i>
                <span class="sidebar-menu-text"><?= htmlspecialchars($module['module_name_ar']) ?></span>
                <?php if (!empty($module['programs'])): ?>
                <i class="fas fa-angle-down sidebar-dropdown-icon"></i>
                <?php endif; ?>
            </a>

            <!-- البرامج الفرعية -->
            <?php if (!empty($module['programs'])): ?>
            <ul class="collapse <?= $currentModule === $module['module_code'] ? 'show' : '' ?>"
                id="module<?= $module['module_id'] ?>Submenu"
                style="list-style: none; padding-left: 3rem;">
                <?php foreach ($module['programs'] as $program): ?>

                <?php if ($program['program_type'] === 'Main'): ?>
                <!-- البرنامج الرئيسي -->
                <?php if (!empty($program['page_url']) && hasUserPermissionForProgram($program['program_id'], 'view')): ?>
                <li>
                    <a href="<?= moduleUrl($program['page_url']) ?>"
                       class="sidebar-menu-link <?= isCurrentPath($program['page_url']) ? 'active' : '' ?>"
                       style="font-size: 0.9rem; padding: 0.5rem 1rem;"
                       data-title="<?= htmlspecialchars($program['program_name_ar']) ?>">
                        <i class="<?= htmlspecialchars($program['icon_name']) ?> sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text"><?= htmlspecialchars($program['program_name_ar']) ?></span>
                    </a>
                </li>
                <?php endif; ?>

                <!-- البرامج الفرعية تحت الرئيسي -->
                <?php if (!empty($program['sub_programs'])): ?>
                <?php foreach ($program['sub_programs'] as $subProgram): ?>
                <?php if (hasUserPermissionForProgram($subProgram['program_id'], 'view')): ?>
                <li>
                    <a href="<?= moduleUrl($subProgram['page_url']) ?>"
                       class="sidebar-menu-link <?= isCurrentPath($subProgram['page_url']) ? 'active' : '' ?>"
                       style="font-size: 0.85rem; padding: 0.4rem 1rem; padding-right: 4rem;"
                       data-title="<?= htmlspecialchars($subProgram['program_name_ar']) ?>">
                        <i class="<?= htmlspecialchars($subProgram['icon_name']) ?> sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text"><?= htmlspecialchars($subProgram['program_name_ar']) ?></span>
                    </a>
                </li>
                <?php endif; ?>
                <?php endforeach; ?>
                <?php endif; ?>

                <?php else: ?>
                <!-- برنامج فرعي مستقل (بدون رئيسي) -->
                <?php if (hasUserPermissionForProgram($program['program_id'], 'view')): ?>
                <li>
                    <a href="<?= moduleUrl($program['page_url']) ?>"
                       class="sidebar-menu-link <?= isCurrentPath($program['page_url']) ? 'active' : '' ?>"
                       style="font-size: 0.9rem; padding: 0.5rem 1rem;"
                       data-title="<?= htmlspecialchars($program['program_name_ar']) ?>">
                        <i class="<?= htmlspecialchars($program['icon_name']) ?> sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text"><?= htmlspecialchars($program['program_name_ar']) ?></span>
                    </a>
                </li>
                <?php endif; ?>
                <?php endif; ?>

                <?php endforeach; ?>
            </ul>
            <?php endif; ?>
        </li>
        <?php endforeach; ?>

        <?php endif; ?>
    </ul>
</aside>
