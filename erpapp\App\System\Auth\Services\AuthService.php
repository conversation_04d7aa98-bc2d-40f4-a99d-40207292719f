<?php
namespace App\System\Auth\Services;

use App\System\Auth\Models\User;

/**
 * خدمة المصادقة
 */
class AuthService
{
    /**
     * نموذج المستخدم
     */
    protected $userModel;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->userModel = new User();
    }

    /**
     * تسجيل الدخول
     *
     * @param string $email البريد الإلكتروني
     * @param string $password كلمة المرور
     * @param bool $remember تذكرني
     * @return array
     */
    public function login($email, $password, $remember = false)
    {
        // التحقق من البيانات
        if (empty($email) || empty($password)) {
            return [
                'success' => false,
                'message' => __('يرجى إدخال البريد الإلكتروني وكلمة المرور')
            ];
        }

        // البحث عن المستخدم
        $user = $this->userModel->getByEmail($email);

        if (!$user) {
            return [
                'success' => false,
                'message' => __('البريد الإلكتروني أو كلمة المرور غير صحيحة')
            ];
        }

        // التحقق من كلمة المرور
        if (!password_verify($password, $user['PasswordHash'])) {
            return [
                'success' => false,
                'message' => __('البريد الإلكتروني أو كلمة المرور غير صحيحة')
            ];
        }

        // التحقق من حالة الحساب
        if ($user['AccountStatus'] !== 'Active') {
            return [
                'success' => false,
                'message' => __('حسابك غير نشط. يرجى الاتصال بالإدارة.')
            ];
        }

        // تسجيل الدخول
        $_SESSION['user_id'] = $user['UserID'];
        $_SESSION['user'] = $user;

        // تحديث آخر نشاط
        $this->userModel->updateLastActivity($user['UserID']);

        // تعيين اللغة
        $_SESSION['lang'] = $user['language'] === 'العربية' ? 'ar' : 'en';

        return [
            'success' => true,
            'message' => __('تم تسجيل الدخول بنجاح')
        ];
    }

    /**
     * تسجيل مستخدم جديد
     *
     * @param array $userData بيانات المستخدم
     * @return array
     */
    public function register($userData)
    {
        // التحقق من البيانات
        $errors = $this->validateRegistrationData($userData);

        if (!empty($errors)) {
            return [
                'success' => false,
                'errors' => $errors
            ];
        }

        // التحقق من وجود البريد الإلكتروني أو اسم المستخدم
        $existingUser = $this->userModel->getByEmailOrUsername($userData['email'], $userData['username']);

        if ($existingUser) {
            $errors = [];

            if ($existingUser['Email'] === $userData['email']) {
                $errors[] = __('البريد الإلكتروني مستخدم بالفعل');
            }

            if ($existingUser['UserName'] === $userData['username']) {
                $errors[] = __('اسم المستخدم مستخدم بالفعل');
            }

            return [
                'success' => false,
                'errors' => $errors
            ];
        }

        // إنشاء المستخدم
        $user_code = bin2hex(random_bytes(16));
        $password_hash = password_hash($userData['password'], PASSWORD_DEFAULT);

        $newUser = [
            'UserCode' => $user_code,
            'UserName' => $userData['username'],
            'FirstName' => $userData['first_name'],
            'LastName' => $userData['last_name'],
            'PhoneNumber' => $userData['phone'],
            'Email' => $userData['email'],
            'PasswordHash' => $password_hash,
            'AccountStatus' => 'Active',
            'language' => 'العربية',
            'theme' => 'light',
            'sidebar_mode' => 'hide',
            'Content_Mode' => 'large',
            'SoundNotifications' => 'Enabled',
            'email_verified' => 0
        ];

        $userId = $this->userModel->create($newUser);

        if ($userId) {
            // إرسال بريد الترحيب
            $language = 'ar'; // اللغة الافتراضية
            send_welcome_email($userData['email'], $userData['first_name'], $language);

            return [
                'success' => true,
                'message' => __('تم إنشاء الحساب بنجاح. يمكنك الآن تسجيل الدخول.')
            ];
        } else {
            return [
                'success' => false,
                'message' => __('حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى.')
            ];
        }
    }

    /**
     * التحقق من بيانات التسجيل
     *
     * @param array $userData بيانات المستخدم
     * @return array
     */
    private function validateRegistrationData($userData)
    {
        $errors = [];

        if (empty($userData['first_name'])) {
            $errors[] = __('الاسم الأول مطلوب');
        }

        if (empty($userData['last_name'])) {
            $errors[] = __('الاسم الأخير مطلوب');
        }

        if (empty($userData['username'])) {
            $errors[] = __('اسم المستخدم مطلوب');
        }

        if (empty($userData['email'])) {
            $errors[] = __('البريد الإلكتروني مطلوب');
        } elseif (!filter_var($userData['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = __('البريد الإلكتروني غير صالح');
        }

        if (empty($userData['password'])) {
            $errors[] = __('كلمة المرور مطلوبة');
        } elseif (strlen($userData['password']) < 8) {
            $errors[] = __('كلمة المرور يجب أن تكون على الأقل 8 أحرف');
        }

        if ($userData['password'] !== $userData['password_confirm']) {
            $errors[] = __('كلمة المرور وتأكيد كلمة المرور غير متطابقين');
        }

        return $errors;
    }

    /**
     * تسجيل الخروج
     *
     * @return void
     */
    public function logout()
    {
        // تدمير الجلسة
        session_destroy();
    }

    /**
     * استعادة كلمة المرور
     *
     * @param string $email البريد الإلكتروني
     * @return array
     */
    public function forgotPassword($email)
    {
        // التحقق من البيانات
        if (empty($email)) {
            return [
                'success' => false,
                'message' => __('البريد الإلكتروني مطلوب')
            ];
        }

        // البحث عن المستخدم
        $user = $this->userModel->getByEmail($email);

        if (!$user) {
            return [
                'success' => false,
                'message' => __('البريد الإلكتروني غير مسجل')
            ];
        }

        // إنشاء رمز إعادة تعيين كلمة المرور
        $token = bin2hex(random_bytes(32));
        $expires_at = date('Y-m-d H:i:s', strtotime('+1 hour'));

        // حفظ الرمز في قاعدة البيانات
        $this->userModel->createPasswordResetToken($user['UserID'], $token, $expires_at);

        // إرسال بريد إعادة تعيين كلمة المرور
        $language = $user['language'] === 'العربية' ? 'ar' : 'en';
        send_password_reset_email($email, $token, $language);

        return [
            'success' => true,
            'message' => __('تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني.')
        ];
    }

    /**
     * إعادة تعيين كلمة المرور
     *
     * @param string $token رمز إعادة تعيين كلمة المرور
     * @param string $password كلمة المرور الجديدة
     * @param string $password_confirm تأكيد كلمة المرور
     * @return array
     */
    public function resetPassword($token, $password, $password_confirm)
    {
        // التحقق من البيانات
        if (empty($token)) {
            return [
                'success' => false,
                'message' => __('رمز إعادة تعيين كلمة المرور غير صالح')
            ];
        }

        if (empty($password)) {
            return [
                'success' => false,
                'message' => __('كلمة المرور مطلوبة')
            ];
        } elseif (strlen($password) < 8) {
            return [
                'success' => false,
                'message' => __('كلمة المرور يجب أن تكون على الأقل 8 أحرف')
            ];
        }

        if ($password !== $password_confirm) {
            return [
                'success' => false,
                'message' => __('كلمة المرور وتأكيد كلمة المرور غير متطابقين')
            ];
        }

        // التحقق من صلاحية الرمز
        $reset = $this->userModel->getPasswordResetToken($token);

        if (!$reset) {
            return [
                'success' => false,
                'message' => __('رمز إعادة تعيين كلمة المرور غير صالح أو منتهي الصلاحية')
            ];
        }

        // الحصول على المستخدم
        $user = $this->userModel->getById($reset['UserID']);

        if (!$user) {
            return [
                'success' => false,
                'message' => __('المستخدم غير موجود')
            ];
        }

        // تحديث كلمة المرور
        $password_hash = password_hash($password, PASSWORD_DEFAULT);
        $updated = $this->userModel->updatePassword($user['UserID'], $password_hash);

        if ($updated) {
            // حذف رمز إعادة تعيين كلمة المرور
            $this->userModel->deletePasswordResetToken($token);

            return [
                'success' => true,
                'message' => __('تم إعادة تعيين كلمة المرور بنجاح. يمكنك الآن تسجيل الدخول.')
            ];
        } else {
            return [
                'success' => false,
                'message' => __('حدث خطأ أثناء إعادة تعيين كلمة المرور. يرجى المحاولة مرة أخرى.')
            ];
        }
    }
}
