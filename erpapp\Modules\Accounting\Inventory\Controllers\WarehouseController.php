<?php
namespace Modules\Accounting\Inventory\Controllers;

use Modules\Accounting\Inventory\Models\Warehouse;

/**
 * Warehouse Controller
 * متحكم المخازن
 */
class WarehouseController {

    private $warehouseModel;

    public function __construct() {
        $this->warehouseModel = new Warehouse();
    }

    /**
     * عرض قائمة المخازن
     */
    public function index() {
        try {
            $company_id = get_user_company_id();
            $warehouses = $this->warehouseModel->getAll($company_id, false); // جميع المخازن بما في ذلك غير النشطة

            $data = [
                'title' => 'إدارة المخازن',
                'warehouses' => $warehouses,
                'total_count' => count($warehouses)
            ];

            view('Accounting.Inventory::warehouses/index', $data);

        } catch (Exception $e) {
            error_log("Error in WarehouseController::index: " . $e->getMessage());
            show_error('حدث خطأ أثناء تحميل المخازن');
        }
    }

    /**
     * عرض تفاصيل مخزن
     */
    public function show($warehouse_id) {
        try {
            $company_id = get_user_company_id();
            $warehouse = $this->warehouseModel->getById($warehouse_id, $company_id);

            if (!$warehouse) {
                show_404('المخزن غير موجود');
                return;
            }

            // الحصول على إحصائيات المخزن
            $statistics = $this->warehouseModel->getStatistics($warehouse_id, $company_id);

            $data = [
                'title' => 'تفاصيل المخزن: ' . $warehouse['warehouse_name_ar'],
                'warehouse' => $warehouse,
                'statistics' => $statistics
            ];

            load_view('Modules/Accounting/Inventory/Views/warehouses/show', $data);

        } catch (Exception $e) {
            error_log("Error in WarehouseController::show: " . $e->getMessage());
            show_error('حدث خطأ أثناء تحميل تفاصيل المخزن');
        }
    }

    /**
     * عرض نموذج إنشاء مخزن جديد
     */
    public function create() {
        try {
            $data = [
                'title' => 'إضافة مخزن جديد',
                'warehouse' => [], // مخزن فارغ للنموذج
                'warehouse_types' => [
                    'main' => 'رئيسي',
                    'branch' => 'فرع',
                    'virtual' => 'افتراضي',
                    'external' => 'خارجي'
                ]
            ];

            load_view('Modules/Accounting/Inventory/Views/warehouses/create', $data);

        } catch (Exception $e) {
            error_log("Error in WarehouseController::create: " . $e->getMessage());
            show_error('حدث خطأ أثناء تحميل نموذج إنشاء المخزن');
        }
    }

    /**
     * حفظ مخزن جديد
     */
    public function store() {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                redirect('/inventory/warehouses');
                return;
            }

            $company_id = get_user_company_id();
            $user_id = get_current_user_id();

            // التحقق من صحة البيانات
            $validation_errors = $this->validateWarehouseData($_POST);

            if (!empty($validation_errors)) {
                set_flash_message('error', implode('<br>', $validation_errors));
                $this->create();
                return;
            }

            // التحقق من عدم تكرار كود المخزن
            if ($this->warehouseModel->isWarehouseCodeExists($_POST['warehouse_code'], $company_id)) {
                set_flash_message('error', 'كود المخزن موجود مسبقاً');
                $this->create();
                return;
            }

            // إعداد بيانات المخزن
            $warehouse_data = [
                'company_id' => $company_id,
                'warehouse_code' => $_POST['warehouse_code'],
                'warehouse_name_ar' => $_POST['warehouse_name_ar'],
                'warehouse_name_en' => $_POST['warehouse_name_en'] ?? null,
                'address' => $_POST['address'] ?? null,
                'phone' => $_POST['phone'] ?? null,
                'email' => $_POST['email'] ?? null,
                'manager_name' => $_POST['manager_name'] ?? null,
                'capacity' => !empty($_POST['capacity']) ? (float)$_POST['capacity'] : null,
                'warehouse_type' => $_POST['warehouse_type'] ?? 'main',
                'is_active' => isset($_POST['is_active']) ? 1 : 0,
                'created_by' => $user_id
            ];

            $warehouse_id = $this->warehouseModel->create($warehouse_data);

            if ($warehouse_id) {
                set_flash_message('success', 'تم إنشاء المخزن بنجاح');
                redirect('/inventory/warehouses/' . $warehouse_id);
            } else {
                set_flash_message('error', 'حدث خطأ أثناء إنشاء المخزن');
                $this->create();
            }

        } catch (Exception $e) {
            error_log("Error in WarehouseController::store: " . $e->getMessage());
            set_flash_message('error', 'حدث خطأ أثناء حفظ المخزن');
            $this->create();
        }
    }

    /**
     * عرض نموذج تعديل مخزن
     */
    public function edit($warehouse_id) {
        try {
            $company_id = get_user_company_id();
            $warehouse = $this->warehouseModel->getById($warehouse_id, $company_id);

            if (!$warehouse) {
                show_404('المخزن غير موجود');
                return;
            }

            $data = [
                'title' => 'تعديل المخزن: ' . $warehouse['warehouse_name_ar'],
                'warehouse' => $warehouse,
                'warehouse_types' => [
                    'main' => 'رئيسي',
                    'branch' => 'فرع',
                    'virtual' => 'افتراضي',
                    'external' => 'خارجي'
                ]
            ];

            load_view('Modules/Accounting/Inventory/Views/warehouses/edit', $data);

        } catch (Exception $e) {
            error_log("Error in WarehouseController::edit: " . $e->getMessage());
            show_error('حدث خطأ أثناء تحميل نموذج تعديل المخزن');
        }
    }

    /**
     * تحديث مخزن
     */
    public function update($warehouse_id) {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                redirect('/inventory/warehouses');
                return;
            }

            $company_id = get_user_company_id();
            $user_id = get_current_user_id();

            // التحقق من وجود المخزن
            $warehouse = $this->warehouseModel->getById($warehouse_id, $company_id);
            if (!$warehouse) {
                show_404('المخزن غير موجود');
                return;
            }

            // التحقق من صحة البيانات
            $validation_errors = $this->validateWarehouseData($_POST);

            if (!empty($validation_errors)) {
                set_flash_message('error', implode('<br>', $validation_errors));
                $this->edit($warehouse_id);
                return;
            }

            // التحقق من عدم تكرار كود المخزن
            if ($this->warehouseModel->isWarehouseCodeExists($_POST['warehouse_code'], $company_id, $warehouse_id)) {
                set_flash_message('error', 'كود المخزن موجود مسبقاً');
                $this->edit($warehouse_id);
                return;
            }

            // إعداد بيانات المخزن
            $warehouse_data = [
                'warehouse_code' => $_POST['warehouse_code'],
                'warehouse_name_ar' => $_POST['warehouse_name_ar'],
                'warehouse_name_en' => $_POST['warehouse_name_en'] ?? null,
                'address' => $_POST['address'] ?? null,
                'phone' => $_POST['phone'] ?? null,
                'email' => $_POST['email'] ?? null,
                'manager_name' => $_POST['manager_name'] ?? null,
                'capacity' => !empty($_POST['capacity']) ? (float)$_POST['capacity'] : null,
                'warehouse_type' => $_POST['warehouse_type'] ?? 'main',
                'is_active' => isset($_POST['is_active']) ? 1 : 0,
                'updated_by' => $user_id
            ];

            $success = $this->warehouseModel->update($warehouse_id, $warehouse_data, $company_id);

            if ($success) {
                set_flash_message('success', 'تم تحديث المخزن بنجاح');
                redirect('/inventory/warehouses/' . $warehouse_id);
            } else {
                set_flash_message('error', 'حدث خطأ أثناء تحديث المخزن');
                $this->edit($warehouse_id);
            }

        } catch (Exception $e) {
            error_log("Error in WarehouseController::update: " . $e->getMessage());
            set_flash_message('error', 'حدث خطأ أثناء تحديث المخزن');
            $this->edit($warehouse_id);
        }
    }

    /**
     * حذف مخزن
     */
    public function delete($warehouse_id) {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                redirect('/inventory/warehouses');
                return;
            }

            $company_id = get_user_company_id();

            // التحقق من وجود المخزن
            $warehouse = $this->warehouseModel->getById($warehouse_id, $company_id);
            if (!$warehouse) {
                set_flash_message('error', 'المخزن غير موجود');
                redirect('/inventory/warehouses');
                return;
            }

            $success = $this->warehouseModel->delete($warehouse_id, $company_id);

            if ($success) {
                set_flash_message('success', 'تم حذف المخزن بنجاح');
            } else {
                set_flash_message('error', 'لا يمكن حذف المخزن لوجود أرصدة مخزون مرتبطة به');
            }

            redirect('/inventory/warehouses');

        } catch (Exception $e) {
            error_log("Error in WarehouseController::delete: " . $e->getMessage());
            set_flash_message('error', 'حدث خطأ أثناء حذف المخزن');
            redirect('/inventory/warehouses');
        }
    }

    /**
     * تحديث الاستخدام الحالي للمخزن
     */
    public function updateUsage($warehouse_id) {
        try {
            $company_id = get_user_company_id();

            $success = $this->warehouseModel->updateCurrentUsage($warehouse_id, $company_id);

            if ($success) {
                set_flash_message('success', 'تم تحديث الاستخدام الحالي للمخزن');
            } else {
                set_flash_message('error', 'حدث خطأ أثناء تحديث الاستخدام');
            }

            redirect('/inventory/warehouses/' . $warehouse_id);

        } catch (Exception $e) {
            error_log("Error in WarehouseController::updateUsage: " . $e->getMessage());
            set_flash_message('error', 'حدث خطأ أثناء تحديث الاستخدام');
            redirect('/inventory/warehouses/' . $warehouse_id);
        }
    }

    /**
     * الحصول على المخازن حسب النوع (AJAX)
     */
    public function getByType($type) {
        try {
            $company_id = get_user_company_id();
            $warehouses = $this->warehouseModel->getByType($company_id, $type);

            header('Content-Type: application/json');
            echo json_encode($warehouses, JSON_UNESCAPED_UNICODE);

        } catch (Exception $e) {
            error_log("Error in WarehouseController::getByType: " . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['error' => 'حدث خطأ أثناء تحميل المخازن'], JSON_UNESCAPED_UNICODE);
        }
    }

    /**
     * التحقق من صحة بيانات المخزن
     */
    private function validateWarehouseData($data) {
        $errors = [];

        if (empty($data['warehouse_code'])) {
            $errors[] = 'كود المخزن مطلوب';
        }

        if (empty($data['warehouse_name_ar'])) {
            $errors[] = 'اسم المخزن بالعربية مطلوب';
        }

        if (!empty($data['capacity']) && !is_numeric($data['capacity'])) {
            $errors[] = 'سعة المخزن يجب أن تكون رقماً';
        }

        if (!empty($data['capacity']) && (float)$data['capacity'] <= 0) {
            $errors[] = 'سعة المخزن يجب أن تكون أكبر من الصفر';
        }

        if (!empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'البريد الإلكتروني غير صحيح';
        }

        $valid_types = ['main', 'branch', 'virtual', 'external'];
        if (!empty($data['warehouse_type']) && !in_array($data['warehouse_type'], $valid_types)) {
            $errors[] = 'نوع المخزن غير صحيح';
        }

        return $errors;
    }
}
