<?php
namespace Modules\Accounting\Inventory\Controllers;

use Modules\Accounting\Inventory\Models\Unit;

/**
 * Unit Controller
 * متحكم وحدات القياس
 */
class UnitController {
    
    private $unitModel;

    public function __construct() {
        $this->unitModel = new Unit();
    }

    /**
     * عرض قائمة وحدات القياس
     */
    public function index() {
        try {
            $company_id = get_user_company_id();
            $units = $this->unitModel->getAll($company_id, false); // جميع الوحدات بما في ذلك غير النشطة

            $data = [
                'title' => 'إدارة وحدات القياس',
                'units' => $units,
                'total_count' => count($units)
            ];

            load_view('Modules/Accounting/Inventory/Views/units/index', $data);

        } catch (Exception $e) {
            error_log("Error in UnitController::index: " . $e->getMessage());
            show_error('حدث خطأ أثناء تحميل وحدات القياس');
        }
    }

    /**
     * عرض تفاصيل وحدة قياس
     */
    public function show($unit_id) {
        try {
            $company_id = get_user_company_id();
            $unit = $this->unitModel->getById($unit_id, $company_id);

            if (!$unit) {
                show_404('وحدة القياس غير موجودة');
                return;
            }

            $data = [
                'title' => 'تفاصيل وحدة القياس: ' . $unit['unit_name_ar'],
                'unit' => $unit
            ];

            load_view('Modules/Accounting/Inventory/Views/units/show', $data);

        } catch (Exception $e) {
            error_log("Error in UnitController::show: " . $e->getMessage());
            show_error('حدث خطأ أثناء تحميل تفاصيل وحدة القياس');
        }
    }

    /**
     * عرض نموذج إنشاء وحدة قياس جديدة
     */
    public function create() {
        try {
            $company_id = get_user_company_id();
            $base_units = $this->unitModel->getBaseUnits($company_id);

            $data = [
                'title' => 'إضافة وحدة قياس جديدة',
                'base_units' => $base_units,
                'unit' => [] // وحدة فارغة للنموذج
            ];

            load_view('Modules/Accounting/Inventory/Views/units/create', $data);

        } catch (Exception $e) {
            error_log("Error in UnitController::create: " . $e->getMessage());
            show_error('حدث خطأ أثناء تحميل نموذج إنشاء وحدة القياس');
        }
    }

    /**
     * حفظ وحدة قياس جديدة
     */
    public function store() {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                redirect('/inventory/units');
                return;
            }

            $company_id = get_user_company_id();
            $user_id = get_current_user_id();

            // التحقق من صحة البيانات
            $validation_errors = $this->validateUnitData($_POST);
            
            if (!empty($validation_errors)) {
                set_flash_message('error', implode('<br>', $validation_errors));
                $this->create();
                return;
            }

            // التحقق من عدم تكرار كود الوحدة
            if ($this->unitModel->isUnitCodeExists($_POST['unit_code'], $company_id)) {
                set_flash_message('error', 'كود وحدة القياس موجود مسبقاً');
                $this->create();
                return;
            }

            // إعداد بيانات الوحدة
            $unit_data = [
                'company_id' => $company_id,
                'unit_code' => $_POST['unit_code'],
                'unit_name_ar' => $_POST['unit_name_ar'],
                'unit_name_en' => $_POST['unit_name_en'] ?? null,
                'unit_symbol_ar' => $_POST['unit_symbol_ar'],
                'unit_symbol_en' => $_POST['unit_symbol_en'] ?? null,
                'base_unit_id' => !empty($_POST['base_unit_id']) ? $_POST['base_unit_id'] : null,
                'conversion_factor' => (float)($_POST['conversion_factor'] ?? 1.0000),
                'is_base_unit' => isset($_POST['is_base_unit']) ? 1 : 0,
                'is_active' => isset($_POST['is_active']) ? 1 : 0,
                'created_by' => $user_id
            ];

            // إذا كانت وحدة أساسية، تعيين base_unit_id إلى null
            if ($unit_data['is_base_unit']) {
                $unit_data['base_unit_id'] = null;
                $unit_data['conversion_factor'] = 1.0000;
            }

            $unit_id = $this->unitModel->create($unit_data);

            if ($unit_id) {
                set_flash_message('success', 'تم إنشاء وحدة القياس بنجاح');
                redirect('/inventory/units/' . $unit_id);
            } else {
                set_flash_message('error', 'حدث خطأ أثناء إنشاء وحدة القياس');
                $this->create();
            }

        } catch (Exception $e) {
            error_log("Error in UnitController::store: " . $e->getMessage());
            set_flash_message('error', 'حدث خطأ أثناء حفظ وحدة القياس');
            $this->create();
        }
    }

    /**
     * عرض نموذج تعديل وحدة قياس
     */
    public function edit($unit_id) {
        try {
            $company_id = get_user_company_id();
            $unit = $this->unitModel->getById($unit_id, $company_id);

            if (!$unit) {
                show_404('وحدة القياس غير موجودة');
                return;
            }

            $base_units = $this->unitModel->getBaseUnits($company_id);
            // إزالة الوحدة الحالية من قائمة الوحدات الأساسية
            $base_units = array_filter($base_units, function($u) use ($unit_id) {
                return $u['unit_id'] != $unit_id;
            });

            $data = [
                'title' => 'تعديل وحدة القياس: ' . $unit['unit_name_ar'],
                'unit' => $unit,
                'base_units' => $base_units
            ];

            load_view('Modules/Accounting/Inventory/Views/units/edit', $data);

        } catch (Exception $e) {
            error_log("Error in UnitController::edit: " . $e->getMessage());
            show_error('حدث خطأ أثناء تحميل نموذج تعديل وحدة القياس');
        }
    }

    /**
     * تحديث وحدة قياس
     */
    public function update($unit_id) {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                redirect('/inventory/units');
                return;
            }

            $company_id = get_user_company_id();
            $user_id = get_current_user_id();

            // التحقق من وجود الوحدة
            $unit = $this->unitModel->getById($unit_id, $company_id);
            if (!$unit) {
                show_404('وحدة القياس غير موجودة');
                return;
            }

            // التحقق من صحة البيانات
            $validation_errors = $this->validateUnitData($_POST);
            
            if (!empty($validation_errors)) {
                set_flash_message('error', implode('<br>', $validation_errors));
                $this->edit($unit_id);
                return;
            }

            // التحقق من عدم تكرار كود الوحدة
            if ($this->unitModel->isUnitCodeExists($_POST['unit_code'], $company_id, $unit_id)) {
                set_flash_message('error', 'كود وحدة القياس موجود مسبقاً');
                $this->edit($unit_id);
                return;
            }

            // إعداد بيانات الوحدة
            $unit_data = [
                'unit_code' => $_POST['unit_code'],
                'unit_name_ar' => $_POST['unit_name_ar'],
                'unit_name_en' => $_POST['unit_name_en'] ?? null,
                'unit_symbol_ar' => $_POST['unit_symbol_ar'],
                'unit_symbol_en' => $_POST['unit_symbol_en'] ?? null,
                'base_unit_id' => !empty($_POST['base_unit_id']) ? $_POST['base_unit_id'] : null,
                'conversion_factor' => (float)($_POST['conversion_factor'] ?? 1.0000),
                'is_base_unit' => isset($_POST['is_base_unit']) ? 1 : 0,
                'is_active' => isset($_POST['is_active']) ? 1 : 0,
                'updated_by' => $user_id
            ];

            // إذا كانت وحدة أساسية، تعيين base_unit_id إلى null
            if ($unit_data['is_base_unit']) {
                $unit_data['base_unit_id'] = null;
                $unit_data['conversion_factor'] = 1.0000;
            }

            $success = $this->unitModel->update($unit_id, $unit_data, $company_id);

            if ($success) {
                set_flash_message('success', 'تم تحديث وحدة القياس بنجاح');
                redirect('/inventory/units/' . $unit_id);
            } else {
                set_flash_message('error', 'حدث خطأ أثناء تحديث وحدة القياس');
                $this->edit($unit_id);
            }

        } catch (Exception $e) {
            error_log("Error in UnitController::update: " . $e->getMessage());
            set_flash_message('error', 'حدث خطأ أثناء تحديث وحدة القياس');
            $this->edit($unit_id);
        }
    }

    /**
     * حذف وحدة قياس
     */
    public function delete($unit_id) {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                redirect('/inventory/units');
                return;
            }

            $company_id = get_user_company_id();

            // التحقق من وجود الوحدة
            $unit = $this->unitModel->getById($unit_id, $company_id);
            if (!$unit) {
                set_flash_message('error', 'وحدة القياس غير موجودة');
                redirect('/inventory/units');
                return;
            }

            $success = $this->unitModel->delete($unit_id, $company_id);

            if ($success) {
                set_flash_message('success', 'تم حذف وحدة القياس بنجاح');
            } else {
                set_flash_message('error', 'لا يمكن حذف وحدة القياس لوجود منتجات أو وحدات فرعية مرتبطة بها');
            }

            redirect('/inventory/units');

        } catch (Exception $e) {
            error_log("Error in UnitController::delete: " . $e->getMessage());
            set_flash_message('error', 'حدث خطأ أثناء حذف وحدة القياس');
            redirect('/inventory/units');
        }
    }

    /**
     * تحويل الكميات (AJAX)
     */
    public function convert() {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                header('Content-Type: application/json');
                echo json_encode(['error' => 'طريقة الطلب غير صحيحة'], JSON_UNESCAPED_UNICODE);
                return;
            }

            $company_id = get_user_company_id();
            $quantity = (float)($_POST['quantity'] ?? 0);
            $from_unit_id = (int)($_POST['from_unit_id'] ?? 0);
            $to_unit_id = (int)($_POST['to_unit_id'] ?? 0);

            if ($quantity <= 0 || $from_unit_id <= 0 || $to_unit_id <= 0) {
                header('Content-Type: application/json');
                echo json_encode(['error' => 'بيانات غير صحيحة'], JSON_UNESCAPED_UNICODE);
                return;
            }

            $converted_quantity = $this->unitModel->convertQuantity($quantity, $from_unit_id, $to_unit_id, $company_id);

            if ($converted_quantity === false) {
                header('Content-Type: application/json');
                echo json_encode(['error' => 'لا يمكن التحويل بين هذين النوعين من الوحدات'], JSON_UNESCAPED_UNICODE);
                return;
            }

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'converted_quantity' => $converted_quantity,
                'original_quantity' => $quantity,
                'from_unit_id' => $from_unit_id,
                'to_unit_id' => $to_unit_id
            ], JSON_UNESCAPED_UNICODE);

        } catch (Exception $e) {
            error_log("Error in UnitController::convert: " . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['error' => 'حدث خطأ أثناء التحويل'], JSON_UNESCAPED_UNICODE);
        }
    }

    /**
     * التحقق من صحة بيانات وحدة القياس
     */
    private function validateUnitData($data) {
        $errors = [];

        if (empty($data['unit_code'])) {
            $errors[] = 'كود وحدة القياس مطلوب';
        }

        if (empty($data['unit_name_ar'])) {
            $errors[] = 'اسم وحدة القياس بالعربية مطلوب';
        }

        if (empty($data['unit_symbol_ar'])) {
            $errors[] = 'رمز وحدة القياس بالعربية مطلوب';
        }

        if (!isset($data['is_base_unit']) && empty($data['base_unit_id'])) {
            $errors[] = 'يجب تحديد الوحدة الأساسية أو جعل هذه الوحدة أساسية';
        }

        if (!empty($data['conversion_factor']) && !is_numeric($data['conversion_factor'])) {
            $errors[] = 'معامل التحويل يجب أن يكون رقماً';
        }

        if (!empty($data['conversion_factor']) && (float)$data['conversion_factor'] <= 0) {
            $errors[] = 'معامل التحويل يجب أن يكون أكبر من الصفر';
        }

        return $errors;
    }
}
