-- مثال على إعداد البرامج والصلاحيات في قاعدة البيانات
-- يجب تشغيل هذا السكريبت لكل شركة

-- 1. إضافة البرامج الأساسية للشركة (مثال: company_id = 1)
INSERT INTO `company_all_programs` (`company_id`, `name_ar`, `name_en`, `page_url`, `icon_name`, `program_type`, `display_order`, `category`) VALUES
-- برامج المخزون
(1, 'إدارة المخزون', 'inventory_management', 'inventory', 'fas fa-boxes', 'Main', 1, 'Programs'),
(1, 'المنتجات', 'inventory_products', 'inventory/products', 'fas fa-box', 'Sub', 2, 'Programs'),
(1, 'الفئات', 'inventory_categories', 'inventory/categories', 'fas fa-tags', 'Sub', 3, 'Programs'),
(1, 'وحدات القياس', 'inventory_units', 'inventory/units', 'fas fa-ruler', 'Sub', 4, 'Programs'),

-- برامج الشركات
(1, 'إدارة الشركات', 'company_management', 'companies', 'fas fa-building', 'Main', 5, 'Programs'),

-- برامج المستخدمين
(1, 'إدارة المستخدمين', 'user_management', 'users', 'fas fa-users', 'Main', 6, 'Programs'),

-- برامج المحاسبة
(1, 'المحاسبة', 'accounting', 'accounting', 'fas fa-calculator', 'Main', 7, 'Programs'),
(1, 'الفواتير', 'accounting_invoices', 'accounting/invoices', 'fas fa-file-invoice', 'Sub', 8, 'Programs'),

-- برامج الموارد البشرية
(1, 'الموارد البشرية', 'hr_management', 'hr', 'fas fa-user-tie', 'Main', 9, 'Programs'),
(1, 'الموظفين', 'hr_employees', 'hr/employees', 'fas fa-users', 'Sub', 10, 'Programs'),

-- برامج التقارير
(1, 'التقارير', 'reports', 'reports', 'fas fa-chart-bar', 'Main', 11, 'Programs'),

-- برامج الإعدادات
(1, 'إعدادات النظام', 'system_settings', 'settings', 'fas fa-cogs', 'Main', 12, 'Settings'),
(1, 'إدارة المناصب', 'positions_management', 'settings/positions', 'fas fa-user-tag', 'Sub', 13, 'Settings'),
(1, 'إدارة الصلاحيات', 'permissions_management', 'settings/permissions', 'fas fa-key', 'Sub', 14, 'Settings');

-- 2. إضافة المناصب الأساسية
INSERT INTO `positions` (`CompanyID`, `PositionNameAR`, `PositionNameEN`, `CreatedBy`, `Description`) VALUES
(1, 'مدير عام', 'General Manager', 1, 'صلاحيات كاملة على جميع البرامج'),
(1, 'مدير مخزون', 'Inventory Manager', 1, 'إدارة كاملة للمخزون والمنتجات'),
(1, 'محاسب', 'Accountant', 1, 'إدارة الحسابات والفواتير'),
(1, 'مدير موارد بشرية', 'HR Manager', 1, 'إدارة الموظفين والموارد البشرية'),
(1, 'موظف مخزون', 'Inventory Employee', 1, 'عرض وإضافة المنتجات فقط'),
(1, 'مشاهد', 'Viewer', 1, 'عرض البيانات فقط بدون تعديل');

-- 3. إعداد الصلاحيات للمناصب

-- صلاحيات المدير العام (PositionID = 1) - صلاحيات كاملة
INSERT INTO `permissions` (`PositionID`, `CompanyID`, `CreatedBy`, `ProgramID`, `CanView`, `CanCreate`, `CanEdit`, `CanDelete`, `CanApprove`) 
SELECT 1, 1, 1, program_id, 1, 1, 1, 1, 1 
FROM company_all_programs 
WHERE company_id = 1;

-- صلاحيات مدير المخزون (PositionID = 2) - صلاحيات كاملة للمخزون فقط
INSERT INTO `permissions` (`PositionID`, `CompanyID`, `CreatedBy`, `ProgramID`, `CanView`, `CanCreate`, `CanEdit`, `CanDelete`, `CanApprove`) 
SELECT 2, 1, 1, program_id, 1, 1, 1, 1, 0 
FROM company_all_programs 
WHERE company_id = 1 
AND name_en LIKE 'inventory%';

-- صلاحيات المحاسب (PositionID = 3) - صلاحيات المحاسبة والعرض للمخزون
INSERT INTO `permissions` (`PositionID`, `CompanyID`, `CreatedBy`, `ProgramID`, `CanView`, `CanCreate`, `CanEdit`, `CanDelete`, `CanApprove`) 
SELECT 3, 1, 1, program_id, 1, 1, 1, 0, 1 
FROM company_all_programs 
WHERE company_id = 1 
AND name_en LIKE 'accounting%';

INSERT INTO `permissions` (`PositionID`, `CompanyID`, `CreatedBy`, `ProgramID`, `CanView`, `CanCreate`, `CanEdit`, `CanDelete`, `CanApprove`) 
SELECT 3, 1, 1, program_id, 1, 0, 0, 0, 0 
FROM company_all_programs 
WHERE company_id = 1 
AND name_en LIKE 'inventory%';

-- صلاحيات مدير الموارد البشرية (PositionID = 4)
INSERT INTO `permissions` (`PositionID`, `CompanyID`, `CreatedBy`, `ProgramID`, `CanView`, `CanCreate`, `CanEdit`, `CanDelete`, `CanApprove`) 
SELECT 4, 1, 1, program_id, 1, 1, 1, 1, 0 
FROM company_all_programs 
WHERE company_id = 1 
AND (name_en LIKE 'hr%' OR name_en = 'user_management');

-- صلاحيات موظف المخزون (PositionID = 5) - عرض وإضافة فقط
INSERT INTO `permissions` (`PositionID`, `CompanyID`, `CreatedBy`, `ProgramID`, `CanView`, `CanCreate`, `CanEdit`, `CanDelete`, `CanApprove`) 
SELECT 5, 1, 1, program_id, 1, 1, 0, 0, 0 
FROM company_all_programs 
WHERE company_id = 1 
AND name_en LIKE 'inventory%';

-- صلاحيات المشاهد (PositionID = 6) - عرض فقط
INSERT INTO `permissions` (`PositionID`, `CompanyID`, `CreatedBy`, `ProgramID`, `CanView`, `CanCreate`, `CanEdit`, `CanDelete`, `CanApprove`) 
SELECT 6, 1, 1, program_id, 1, 0, 0, 0, 0 
FROM company_all_programs 
WHERE company_id = 1;

-- 4. مثال على الصلاحيات المخصصة (JSON)
UPDATE `permissions` 
SET `CustomPermissions` = '{"export_data": true, "import_data": true, "bulk_operations": true, "advanced_reports": true}'
WHERE `PositionID` = 1 AND `CompanyID` = 1; -- المدير العام

UPDATE `permissions` 
SET `CustomPermissions` = '{"export_data": true, "import_data": false, "bulk_operations": true, "advanced_reports": false}'
WHERE `PositionID` = 2 AND `CompanyID` = 1; -- مدير المخزون

UPDATE `permissions` 
SET `CustomPermissions` = '{"export_data": false, "import_data": false, "bulk_operations": false, "advanced_reports": false}'
WHERE `PositionID` = 6 AND `CompanyID` = 1; -- المشاهد

-- 5. ربط المستخدمين بالمناصب (مثال)
-- يجب تحديث جدول company_users ليربط المستخدمين بمناصبهم
-- UPDATE company_users SET position_id = 1 WHERE user_id = 1 AND company_id = 1; -- مدير عام
-- UPDATE company_users SET position_id = 2 WHERE user_id = 2 AND company_id = 1; -- مدير مخزون
-- UPDATE company_users SET position_id = 3 WHERE user_id = 3 AND company_id = 1; -- محاسب

-- ملاحظات مهمة:
-- 1. يجب تكرار هذا السكريبت لكل شركة مع تغيير company_id
-- 2. يجب التأكد من وجود الحقول المطلوبة في جدول company_users
-- 3. يمكن إضافة المزيد من البرامج والمناصب حسب الحاجة
-- 4. الصلاحيات المخصصة (CustomPermissions) اختيارية ويمكن تركها فارغة
