<?php
/**
 * دوال مساعدة للوحة التحكم
 * جلب البيانات والإحصائيات للنظام والوحدات
 */

/**
 * جلب إحصائيات النظام للوحة التحكم
 *
 * @param array $user
 * @return array
 */
function getSystemStats($user) {
    try {
        global $db;
        
        $stats = [];
        
        // إحصائيات الشركات
        $stmt = $db->prepare("SELECT COUNT(*) as total FROM companies");
        $stmt->execute();
        $stats['companies'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;
        
        // إحصائيات البرامج
        $stmt = $db->prepare("SELECT COUNT(*) as total FROM system_modules");
        $stmt->execute();
        $stats['programs'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;
        
        // إحصائيات المستخدمين
        $stmt = $db->prepare("SELECT COUNT(*) as total FROM users WHERE UserStatus = 'Active'");
        $stmt->execute();
        $stats['users'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;
        
        return $stats;
        
    } catch (Exception $e) {
        error_log("خطأ في جلب إحصائيات النظام: " . $e->getMessage());
        return ['companies' => 0, 'programs' => 0, 'users' => 0];
    }
}

/**
 * جلب الشركات الحديثة
 *
 * @param array $user
 * @param int $limit
 * @return array
 */
function getRecentCompanies($user, $limit = 5) {
    try {
        global $db;
        
        $stmt = $db->prepare("
            SELECT * FROM companies 
            ORDER BY CompanyCreatedAt DESC 
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("خطأ في جلب الشركات الحديثة: " . $e->getMessage());
        return [];
    }
}

/**
 * جلب الإشعارات الحديثة
 *
 * @param array $user
 * @param int $limit
 * @return array
 */
function getRecentNotifications($user, $limit = 5) {
    // مؤقت - إرجاع مصفوفة فارغة
    return [];
}

/**
 * جلب المهام الحديثة
 *
 * @param array $user
 * @param int $limit
 * @return array
 */
function getRecentTasks($user, $limit = 5) {
    // مؤقت - إرجاع مصفوفة فارغة
    return [];
}

/**
 * جلب إحصائيات الوحدات للوحة التحكم
 *
 * @param int $companyId
 * @return array
 */
function getModulesStats($companyId) {
    try {
        global $db;

        // إحصائيات الوحدات المنزلة (محاكاة)
        $stats = [
            'total_modules' => 3,
            'active_modules' => 2,
            'total_programs' => 8,
            'total_users' => 5
        ];

        return $stats;

    } catch (Exception $e) {
        error_log("خطأ في جلب إحصائيات الوحدات: " . $e->getMessage());
        return [
            'total_modules' => 0,
            'active_modules' => 0,
            'total_programs' => 0,
            'total_users' => 0
        ];
    }
}

/**
 * جلب الأنشطة الحديثة للوحدات
 *
 * @param int $companyId
 * @return array
 */
function getModulesRecentActivities($companyId) {
    // محاكاة بيانات الأنشطة
    return [
        [
            'title' => 'وحدة إدارة المخزون',
            'user_name' => 'أحمد محمد',
            'created_at' => date('Y-m-d H:i:s', strtotime('-2 hours'))
        ],
        [
            'title' => 'وحدة المبيعات',
            'user_name' => 'سارة أحمد',
            'created_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
        ]
    ];
}

/**
 * جلب معلومات النظام للشركة
 *
 * @param int $companyId
 * @return array
 */
function getCompanySystemInfo($companyId) {
    try {
        global $db;

        $stmt = $db->prepare("
            SELECT 
                c.CompanyName,
                c.CompanyStatus
            FROM companies c
            WHERE c.CompanyID = ?
        ");
        $stmt->execute([$companyId]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: [];

    } catch (Exception $e) {
        error_log("خطأ في جلب معلومات النظام: " . $e->getMessage());
        return [];
    }
}

/**
 * جلب الإجراءات السريعة للوحدات
 *
 * @return array
 */
function getModulesQuickActions() {
    return [
        [
            'title' => 'إدارة المخزون',
            'description' => 'الوصول إلى وحدة إدارة المخزون',
            'icon' => 'fas fa-boxes',
            'url' => base_url('inventory'),
            'color' => 'primary'
        ],
        [
            'title' => 'إدارة الصلاحيات',
            'description' => 'تحديد صلاحيات المستخدمين',
            'icon' => 'fas fa-user-shield',
            'url' => base_url('permissions'),
            'color' => 'warning'
        ],
        [
            'title' => 'إعدادات النظام',
            'description' => 'تكوين إعدادات النظام',
            'icon' => 'fas fa-cogs',
            'url' => base_url('settings'),
            'color' => 'info'
        ],
        [
            'title' => 'إدارة الشركات',
            'description' => 'عرض وإدارة الشركات',
            'icon' => 'fas fa-building',
            'url' => base_url('companies'),
            'color' => 'success'
        ]
    ];
}
