<?php
namespace Modules\Accounting\Inventory\Models;

/**
 * Warehouse Model
 * نموذج المخازن
 */
class Warehouse {
    /**
     * Database connection
     *
     * @var \PDO
     */
    protected $db;

    /**
     * Constructor
     */
    public function __construct() {
        global $db;
        $this->db = $db;
    }

    /**
     * الحصول على جميع المخازن للشركة
     *
     * @param int $company_id معرف الشركة
     * @param bool $active_only الحصول على المخازن النشطة فقط
     * @return array
     */
    public function getAll($company_id, $active_only = true) {
        $where_clause = 'company_id = :company_id';
        if ($active_only) {
            $where_clause .= ' AND is_active = 1';
        }

        $sql = "
            SELECT w.*, 
                   COUNT(DISTINCT s.product_id) as product_count,
                   COALESCE(SUM(s.quantity_on_hand), 0) as total_quantity,
                   ROUND((w.current_usage / NULLIF(w.capacity, 0)) * 100, 2) as usage_percentage
            FROM inventory_warehouses w
            LEFT JOIN inventory_stock s ON w.warehouse_id = s.warehouse_id
            WHERE {$where_clause}
            GROUP BY w.warehouse_id
            ORDER BY w.warehouse_type ASC, w.warehouse_name_ar ASC
        ";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':company_id', $company_id);
        $stmt->execute();
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على مخزن بواسطة المعرف
     *
     * @param int $warehouse_id معرف المخزن
     * @param int $company_id معرف الشركة
     * @return array|false
     */
    public function getById($warehouse_id, $company_id) {
        $sql = "
            SELECT w.*, 
                   creator.UserName as created_by_name,
                   updater.UserName as updated_by_name,
                   COUNT(DISTINCT s.product_id) as product_count,
                   COALESCE(SUM(s.quantity_on_hand), 0) as total_quantity
            FROM inventory_warehouses w
            LEFT JOIN users creator ON w.created_by = creator.UserID
            LEFT JOIN users updater ON w.updated_by = updater.UserID
            LEFT JOIN inventory_stock s ON w.warehouse_id = s.warehouse_id
            WHERE w.warehouse_id = :warehouse_id AND w.company_id = :company_id
            GROUP BY w.warehouse_id
        ";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':warehouse_id', $warehouse_id);
        $stmt->bindParam(':company_id', $company_id);
        $stmt->execute();
        return $stmt->fetch(\PDO::FETCH_ASSOC);
    }

    /**
     * إنشاء مخزن جديد
     *
     * @param array $data بيانات المخزن
     * @return int|false معرف المخزن الجديد أو false في حالة الفشل
     */
    public function create($data) {
        $sql = "
            INSERT INTO inventory_warehouses (
                company_id, module_code, warehouse_code, warehouse_name_ar, warehouse_name_en,
                address, phone, email, manager_name, capacity, current_usage, warehouse_type,
                is_active, created_by
            ) VALUES (
                :company_id, :module_code, :warehouse_code, :warehouse_name_ar, :warehouse_name_en,
                :address, :phone, :email, :manager_name, :capacity, :current_usage, :warehouse_type,
                :is_active, :created_by
            )
        ";

        $stmt = $this->db->prepare($sql);
        
        // تعيين القيم الافتراضية
        $data['module_code'] = $data['module_code'] ?? 'inventory';
        $data['is_active'] = $data['is_active'] ?? 1;
        $data['current_usage'] = $data['current_usage'] ?? 0.00;
        $data['warehouse_type'] = $data['warehouse_type'] ?? 'main';

        $stmt->bindParam(':company_id', $data['company_id']);
        $stmt->bindParam(':module_code', $data['module_code']);
        $stmt->bindParam(':warehouse_code', $data['warehouse_code']);
        $stmt->bindParam(':warehouse_name_ar', $data['warehouse_name_ar']);
        $stmt->bindParam(':warehouse_name_en', $data['warehouse_name_en']);
        $stmt->bindParam(':address', $data['address']);
        $stmt->bindParam(':phone', $data['phone']);
        $stmt->bindParam(':email', $data['email']);
        $stmt->bindParam(':manager_name', $data['manager_name']);
        $stmt->bindParam(':capacity', $data['capacity']);
        $stmt->bindParam(':current_usage', $data['current_usage']);
        $stmt->bindParam(':warehouse_type', $data['warehouse_type']);
        $stmt->bindParam(':is_active', $data['is_active']);
        $stmt->bindParam(':created_by', $data['created_by']);

        if ($stmt->execute()) {
            return $this->db->lastInsertId();
        }

        return false;
    }

    /**
     * تحديث مخزن
     *
     * @param int $warehouse_id معرف المخزن
     * @param array $data بيانات المخزن
     * @param int $company_id معرف الشركة
     * @return bool
     */
    public function update($warehouse_id, $data, $company_id) {
        $sql = "
            UPDATE inventory_warehouses SET
                warehouse_code = :warehouse_code,
                warehouse_name_ar = :warehouse_name_ar,
                warehouse_name_en = :warehouse_name_en,
                address = :address,
                phone = :phone,
                email = :email,
                manager_name = :manager_name,
                capacity = :capacity,
                warehouse_type = :warehouse_type,
                is_active = :is_active,
                updated_by = :updated_by,
                updated_at = NOW()
            WHERE warehouse_id = :warehouse_id AND company_id = :company_id
        ";

        $stmt = $this->db->prepare($sql);
        
        $stmt->bindParam(':warehouse_id', $warehouse_id);
        $stmt->bindParam(':company_id', $company_id);
        $stmt->bindParam(':warehouse_code', $data['warehouse_code']);
        $stmt->bindParam(':warehouse_name_ar', $data['warehouse_name_ar']);
        $stmt->bindParam(':warehouse_name_en', $data['warehouse_name_en']);
        $stmt->bindParam(':address', $data['address']);
        $stmt->bindParam(':phone', $data['phone']);
        $stmt->bindParam(':email', $data['email']);
        $stmt->bindParam(':manager_name', $data['manager_name']);
        $stmt->bindParam(':capacity', $data['capacity']);
        $stmt->bindParam(':warehouse_type', $data['warehouse_type']);
        $stmt->bindParam(':is_active', $data['is_active']);
        $stmt->bindParam(':updated_by', $data['updated_by']);

        return $stmt->execute();
    }

    /**
     * حذف مخزن
     *
     * @param int $warehouse_id معرف المخزن
     * @param int $company_id معرف الشركة
     * @return bool
     */
    public function delete($warehouse_id, $company_id) {
        // التحقق من عدم وجود أرصدة في هذا المخزن
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as stock_count 
            FROM inventory_stock 
            WHERE warehouse_id = :warehouse_id AND company_id = :company_id AND quantity_on_hand > 0
        ");
        $stmt->bindParam(':warehouse_id', $warehouse_id);
        $stmt->bindParam(':company_id', $company_id);
        $stmt->execute();
        $result = $stmt->fetch(\PDO::FETCH_ASSOC);

        if ($result['stock_count'] > 0) {
            return false; // لا يمكن حذف مخزن يحتوي على أرصدة
        }

        // حذف أرصدة فارغة أولاً
        $stmt = $this->db->prepare("
            DELETE FROM inventory_stock 
            WHERE warehouse_id = :warehouse_id AND company_id = :company_id
        ");
        $stmt->bindParam(':warehouse_id', $warehouse_id);
        $stmt->bindParam(':company_id', $company_id);
        $stmt->execute();

        // حذف المخزن
        $stmt = $this->db->prepare("
            DELETE FROM inventory_warehouses 
            WHERE warehouse_id = :warehouse_id AND company_id = :company_id
        ");
        $stmt->bindParam(':warehouse_id', $warehouse_id);
        $stmt->bindParam(':company_id', $company_id);

        return $stmt->execute();
    }

    /**
     * التحقق من وجود كود المخزن
     *
     * @param string $warehouse_code كود المخزن
     * @param int $company_id معرف الشركة
     * @param int|null $exclude_id معرف المخزن المستثنى (للتحديث)
     * @return bool
     */
    public function isWarehouseCodeExists($warehouse_code, $company_id, $exclude_id = null) {
        $sql = "SELECT COUNT(*) as count FROM inventory_warehouses WHERE warehouse_code = :warehouse_code AND company_id = :company_id";
        $params = [':warehouse_code' => $warehouse_code, ':company_id' => $company_id];

        if ($exclude_id) {
            $sql .= " AND warehouse_id != :exclude_id";
            $params[':exclude_id'] = $exclude_id;
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetch(\PDO::FETCH_ASSOC);

        return $result['count'] > 0;
    }

    /**
     * الحصول على المخازن حسب النوع
     *
     * @param int $company_id معرف الشركة
     * @param string $type نوع المخزن
     * @return array
     */
    public function getByType($company_id, $type) {
        $sql = "
            SELECT * FROM inventory_warehouses 
            WHERE company_id = :company_id 
            AND warehouse_type = :type 
            AND is_active = 1
            ORDER BY warehouse_name_ar ASC
        ";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':company_id', $company_id);
        $stmt->bindParam(':type', $type);
        $stmt->execute();
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * تحديث الاستخدام الحالي للمخزن
     *
     * @param int $warehouse_id معرف المخزن
     * @param int $company_id معرف الشركة
     * @return bool
     */
    public function updateCurrentUsage($warehouse_id, $company_id) {
        // حساب الاستخدام الحالي بناءً على الأرصدة
        $sql = "
            UPDATE inventory_warehouses w
            SET current_usage = (
                SELECT COALESCE(SUM(s.quantity_on_hand), 0)
                FROM inventory_stock s
                WHERE s.warehouse_id = w.warehouse_id
            )
            WHERE w.warehouse_id = :warehouse_id AND w.company_id = :company_id
        ";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':warehouse_id', $warehouse_id);
        $stmt->bindParam(':company_id', $company_id);

        return $stmt->execute();
    }

    /**
     * الحصول على إحصائيات المخزن
     *
     * @param int $warehouse_id معرف المخزن
     * @param int $company_id معرف الشركة
     * @return array
     */
    public function getStatistics($warehouse_id, $company_id) {
        $sql = "
            SELECT 
                COUNT(DISTINCT s.product_id) as total_products,
                COALESCE(SUM(s.quantity_on_hand), 0) as total_quantity,
                COALESCE(SUM(s.quantity_reserved), 0) as total_reserved,
                COALESCE(SUM(s.quantity_available), 0) as total_available,
                COALESCE(SUM(s.quantity_on_hand * s.average_cost), 0) as total_value,
                COUNT(CASE WHEN s.quantity_on_hand <= 0 THEN 1 END) as out_of_stock_count,
                COUNT(CASE WHEN s.quantity_available <= p.reorder_point THEN 1 END) as reorder_needed_count
            FROM inventory_stock s
            LEFT JOIN inventory_products p ON s.product_id = p.product_id
            WHERE s.warehouse_id = :warehouse_id AND s.company_id = :company_id
        ";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':warehouse_id', $warehouse_id);
        $stmt->bindParam(':company_id', $company_id);
        $stmt->execute();
        return $stmt->fetch(\PDO::FETCH_ASSOC);
    }
}
