<?php
/**
 * لوحة تحكم النظام
 * تعرض إحصائيات الشركات والمستخدمين والبرامج
 */

// جلب بيانات المستخدم الحالي
$user = current_user();
if (!$user) {
    redirect('login');
    return;
}

// جلب إحصائيات النظام
$stats = getSystemStats($user);
$companies = getRecentCompanies($user, 5);
$notifications = getRecentNotifications($user, 5);
$tasks = getRecentTasks($user, 5);
?>

<!-- Dashboard Type Indicator -->
<div class="dashboard-type-indicator mb-3">
    <div class="d-flex align-items-center justify-content-between">
        <div class="d-flex align-items-center">
            <div class="dashboard-type-icon bg-primary">
                <i class="fas fa-cogs"></i>
            </div>
            <div class="ms-3">
                <h1 class="page-title mb-0"><?= __('لوحة تحكم النظام') ?></h1>
                <p class="text-muted mb-0">إدارة الشركات والمستخدمين والبرامج</p>
            </div>
        </div>
        <div class="dashboard-switcher">
            <a href="<?= base_url('modules') ?>" class="btn btn-outline-success">
                <i class="fas fa-cubes me-1"></i>
                لوحة تحكم الوحدات
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-6 col-sm-6 mb-3">
        <div class="card bg-primary text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-uppercase fw-bold mb-1"><?= __('الشركات') ?></h6>
                        <h2 class="display-6 fw-bold mb-0"><?= $stats['companies'] ?? 0 ?></h2>
                    </div>
                    <i class="fas fa-building fa-3x opacity-50"></i>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a href="<?= base_url('companies') ?>" class="text-white text-decoration-none"><?= __('عرض التفاصيل') ?></a>
                <i class="fas fa-arrow-circle-right text-white"></i>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-sm-6 mb-3">
        <div class="card bg-success text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-uppercase fw-bold mb-1"><?= __('البرامج') ?></h6>
                        <h2 class="display-6 fw-bold mb-0"><?= $stats['programs'] ?? 0 ?></h2>
                    </div>
                    <i class="fas fa-puzzle-piece fa-3x opacity-50"></i>
                </div>
            </div>
            <div class="card-footer d-flex align-items-center justify-content-between">
                <a href="<?= base_url('programs') ?>" class="text-white text-decoration-none"><?= __('عرض التفاصيل') ?></a>
                <i class="fas fa-arrow-circle-right text-white"></i>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="row">
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><?= __('الشركات الحديثة') ?></h5>
                <a href="<?= base_url('companies/create') ?>" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus"></i> <?= __('إضافة شركة') ?>
                </a>
            </div>
            <div class="card-body">
                <?php if (empty($companies)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-building fa-4x text-muted mb-3"></i>
                        <p class="mb-0"><?= __('لا توجد شركات حتى الآن') ?></p>
                        <a href="<?= base_url('companies/create') ?>" class="btn btn-primary mt-3">
                            <?= __('إضافة شركة جديدة') ?>
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th><?= __('الشركة') ?></th>
                                    <th><?= __('البريد الإلكتروني') ?></th>
                                    <th><?= __('الحالة') ?></th>
                                    <th><?= __('الإجراءات') ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($companies as $company): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php if (!empty($company['CompanyLogo'])): ?>
                                                    <img src="<?= base_url($company['CompanyLogo']) ?>" alt="<?= e($company['CompanyName']) ?>" class="rounded-circle me-2" width="40" height="40" style="object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 40px; height: 40px;">
                                                        <?= strtoupper(substr($company['CompanyName'], 0, 1)) ?>
                                                    </div>
                                                <?php endif; ?>
                                                <div>
                                                    <div class="fw-bold"><?= $company['CompanyName'] ?></div>
                                                    <small class="text-muted"><?= $company['CompanyNameEN'] ?? '' ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?= $company['CompanyEmail'] ?></td>
                                        <td>
                                            <?php if ($company['CompanyStatus'] == 'Active'): ?>
                                                <span class="badge bg-success"><?= __('نشط') ?></span>
                                            <?php elseif ($company['CompanyStatus'] == 'Inactive'): ?>
                                                <span class="badge bg-danger"><?= __('غير نشط') ?></span>
                                            <?php elseif ($company['CompanyStatus'] == 'Trial'): ?>
                                                <span class="badge bg-warning"><?= __('تجريبي') ?></span>
                                            <?php elseif ($company['CompanyStatus'] == 'Expired'): ?>
                                                <span class="badge bg-secondary"><?= __('منتهي') ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a href="<?= base_url('companies/' . $company['CompanyID']) ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?= base_url('companies/' . $company['CompanyID'] . '/edit') ?>" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><?= __('آخر الإشعارات') ?></h5>
                <a href="<?= base_url('notifications') ?>" class="btn btn-sm btn-outline-primary">
                    <?= __('عرض الكل') ?>
                </a>
            </div>
            <div class="card-body p-0">
                <?php if (empty($notifications)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-bell fa-4x text-muted mb-3"></i>
                        <p class="mb-0"><?= __('لا توجد إشعارات حتى الآن') ?></p>
                    </div>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($notifications as $notification): ?>
                            <a href="#" class="list-group-item list-group-item-action <?= $notification['is_read'] ? '' : 'bg-light' ?>">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1"><?= $notification['title'] ?></h6>
                                    <small class="text-muted"><?= format_date($notification['created_at'], 'd/m/Y H:i') ?></small>
                                </div>
                                <p class="mb-1"><?= $notification['message'] ?></p>
                            </a>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><?= __('الإجراءات السريعة') ?></h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?= base_url('companies/create') ?>" class="btn btn-outline-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة شركة جديدة
                    </a>
                    <a href="<?= base_url('programs') ?>" class="btn btn-outline-info">
                        <i class="fas fa-puzzle-piece me-2"></i>
                        إدارة البرامج
                    </a>
                    <a href="<?= base_url('settings') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-cogs me-2"></i>
                        إعدادات النظام
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- CSS للتحسينات -->
<style>
.dashboard-type-indicator {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #dee2e6;
}

.dashboard-type-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.dashboard-switcher .btn {
    border-radius: 8px;
    font-weight: 500;
}

.page-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: #2c3e50;
}
</style>
