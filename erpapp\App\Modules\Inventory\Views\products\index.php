<?php
/**
 * صفحة قائمة المنتجات
 */

// التحقق من وجود البيانات
$products = $products ?? [];
$company = $company ?? [];
$stats = $stats ?? [];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'إدارة المنتجات' ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            transition: transform 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }
        .table-container {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn-action {
            padding: 0.25rem 0.5rem;
            margin: 0 0.1rem;
            border-radius: 5px;
        }
        .product-image {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 5px;
        }
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .status-active {
            background-color: #d4edda;
            color: #155724;
        }
        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }
        .stock-low {
            color: #dc3545;
            font-weight: bold;
        }
        .stock-normal {
            color: #28a745;
        }
        .breadcrumb-item + .breadcrumb-item::before {
            content: "←";
        }
    </style>
</head>
<body>

<!-- Header -->
<div class="main-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="mb-0">
                    <i class="fas fa-boxes me-3"></i>
                    إدارة المنتجات
                </h1>
                <p class="mb-0 mt-2 opacity-75">
                    <?= $company['CompanyName'] ?? 'الشركة' ?> - إدارة وتتبع جميع المنتجات
                </p>
            </div>
            <div class="col-md-6 text-end">
                <a href="<?= base_url('inventory') ?>" class="btn btn-light me-2">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للمخزون
                </a>
                <a href="<?= base_url('inventory/products/create') ?>" class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>
                    إضافة منتج جديد
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('inventory') ?>">المخزون</a></li>
            <li class="breadcrumb-item active">المنتجات</li>
        </ol>
    </nav>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="stats-icon" style="background: linear-gradient(45deg, #667eea, #764ba2);">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div class="ms-3">
                        <h3 class="mb-0"><?= count($products) ?></h3>
                        <p class="text-muted mb-0">إجمالي المنتجات</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="stats-icon" style="background: linear-gradient(45deg, #28a745, #20c997);">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="ms-3">
                        <h3 class="mb-0"><?= count(array_filter($products, fn($p) => $p['is_active'] == 1)) ?></h3>
                        <p class="text-muted mb-0">منتجات نشطة</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="stats-icon" style="background: linear-gradient(45deg, #ffc107, #fd7e14);">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="ms-3">
                        <h3 class="mb-0"><?= $stats['low_stock_products'] ?? 0 ?></h3>
                        <p class="text-muted mb-0">منخفض المخزون</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="stats-icon" style="background: linear-gradient(45deg, #dc3545, #e83e8c);">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div class="ms-3">
                        <h3 class="mb-0"><?= $stats['out_of_stock_products'] ?? 0 ?></h3>
                        <p class="text-muted mb-0">نافد المخزون</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول المنتجات -->
    <div class="table-container">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4 class="mb-0">
                <i class="fas fa-list me-2"></i>
                قائمة المنتجات
            </h4>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-primary btn-sm" onclick="exportProducts()">
                    <i class="fas fa-download me-1"></i>
                    تصدير
                </button>
                <button class="btn btn-outline-secondary btn-sm" onclick="printProducts()">
                    <i class="fas fa-print me-1"></i>
                    طباعة
                </button>
            </div>
        </div>

        <?php if (empty($products)): ?>
            <div class="text-center py-5">
                <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد منتجات</h5>
                <p class="text-muted">ابدأ بإضافة منتجات جديدة لشركتك</p>
                <a href="<?= base_url('inventory/products/create') ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة أول منتج
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table id="productsTable" class="table table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>الصورة</th>
                            <th>كود المنتج</th>
                            <th>اسم المنتج</th>
                            <th>الفئة</th>
                            <th>الوحدة</th>
                            <th>سعر التكلفة</th>
                            <th>سعر البيع</th>
                            <th>المخزون</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($products as $product): ?>
                            <tr>
                                <td>
                                    <?php if (!empty($product['image_url'])): ?>
                                        <img src="<?= base_url($product['image_url']) ?>" 
                                             alt="<?= htmlspecialchars($product['product_name_ar']) ?>" 
                                             class="product-image">
                                    <?php else: ?>
                                        <div class="product-image bg-light d-flex align-items-center justify-content-center">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong><?= htmlspecialchars($product['product_code']) ?></strong>
                                    <?php if (!empty($product['barcode'])): ?>
                                        <br><small class="text-muted"><?= htmlspecialchars($product['barcode']) ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong><?= htmlspecialchars($product['product_name_ar']) ?></strong>
                                    <?php if (!empty($product['product_name_en'])): ?>
                                        <br><small class="text-muted"><?= htmlspecialchars($product['product_name_en']) ?></small>
                                    <?php endif; ?>
                                </td>
                                <td><?= htmlspecialchars($product['category_name_ar'] ?? 'غير محدد') ?></td>
                                <td><?= htmlspecialchars($product['unit_name_ar'] ?? 'غير محدد') ?></td>
                                <td><?= number_format($product['cost_price'], 2) ?> ر.س</td>
                                <td><?= number_format($product['selling_price'], 2) ?> ر.س</td>
                                <td>
                                    <?php 
                                    $stock = $product['total_stock'] ?? 0;
                                    $min_stock = $product['min_stock_level'] ?? 0;
                                    $stock_class = $stock <= $min_stock ? 'stock-low' : 'stock-normal';
                                    ?>
                                    <span class="<?= $stock_class ?>">
                                        <?= number_format($stock, 2) ?>
                                        <?php if ($stock <= $min_stock): ?>
                                            <i class="fas fa-exclamation-triangle ms-1"></i>
                                        <?php endif; ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($product['is_active']): ?>
                                        <span class="status-badge status-active">نشط</span>
                                    <?php else: ?>
                                        <span class="status-badge status-inactive">غير نشط</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= base_url('inventory/products/' . $product['product_id']) ?>" 
                                           class="btn btn-outline-info btn-action" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?= base_url('inventory/products/' . $product['product_id'] . '/edit') ?>" 
                                           class="btn btn-outline-warning btn-action" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button onclick="deleteProduct(<?= $product['product_id'] ?>)" 
                                                class="btn btn-outline-danger btn-action" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>

<script>
$(document).ready(function() {
    // تهيئة DataTable
    $('#productsTable').DataTable({
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        pageLength: 25,
        order: [[1, 'asc']],
        columnDefs: [
            { orderable: false, targets: [0, 9] }
        ]
    });
});

// حذف منتج
function deleteProduct(productId) {
    if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
        fetch(`<?= base_url('inventory/products/') ?>${productId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء حذف المنتج');
            }
        })
        .catch(error => {
            alert('حدث خطأ أثناء حذف المنتج');
        });
    }
}

// تصدير المنتجات
function exportProducts() {
    window.open('<?= base_url('inventory/products/export') ?>', '_blank');
}

// طباعة المنتجات
function printProducts() {
    window.print();
}
</script>

</body>
</html>
