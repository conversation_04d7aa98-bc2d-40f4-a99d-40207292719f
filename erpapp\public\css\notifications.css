/* نظام الإشعارات الموحد */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    width: 350px;
    max-width: 90%;
}

.rtl .notification-container {
    right: auto;
    left: 20px;
}

.notification {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin-bottom: 15px;
    overflow: hidden;
    transform: translateX(120%);
    transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    display: flex;
    align-items: stretch;
    max-width: 100%;
    opacity: 0;
}

.rtl .notification {
    transform: translateX(-120%);
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    flex-shrink: 0;
    color: white;
}

.notification-content {
    padding: 15px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.notification-title {
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 5px;
    color: #333;
}

.notification-message {
    font-size: 0.9rem;
    color: #666;
    line-height: 1.4;
}

.notification-close {
    position: absolute;
    top: 10px;
    right: 10px;
    color: #999;
    cursor: pointer;
    font-size: 0.9rem;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.rtl .notification-close {
    right: auto;
    left: 10px;
}

.notification-close:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: #666;
}

.notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.1);
}

.notification-progress-bar {
    height: 100%;
    width: 100%;
    transform-origin: left;
    animation: progress-animation linear forwards;
}

@keyframes progress-animation {
    0% {
        transform: scaleX(1);
    }
    100% {
        transform: scaleX(0);
    }
}

/* أنواع الإشعارات */
.notification-success .notification-icon {
    background-color: var(--success-color, #10b981);
}

.notification-success .notification-title {
    color: var(--success-color, #10b981);
}

.notification-success .notification-progress-bar {
    background-color: var(--success-color, #10b981);
}

.notification-error .notification-icon {
    background-color: var(--danger-color, #ef4444);
}

.notification-error .notification-title {
    color: var(--danger-color, #ef4444);
}

.notification-error .notification-progress-bar {
    background-color: var(--danger-color, #ef4444);
}

.notification-warning .notification-icon {
    background-color: var(--warning-color, #f59e0b);
}

.notification-warning .notification-title {
    color: var(--warning-color, #f59e0b);
}

.notification-warning .notification-progress-bar {
    background-color: var(--warning-color, #f59e0b);
}

.notification-info .notification-icon {
    background-color: var(--info-color, #3b82f6);
}

.notification-info .notification-title {
    color: var(--info-color, #3b82f6);
}

.notification-info .notification-progress-bar {
    background-color: var(--info-color, #3b82f6);
}

/* تأثير الظهور المتتالي */
.notification:nth-child(1) {
    transition-delay: 0s;
}

.notification:nth-child(2) {
    transition-delay: 0.1s;
}

.notification:nth-child(3) {
    transition-delay: 0.2s;
}

.notification:nth-child(4) {
    transition-delay: 0.3s;
}

.notification:nth-child(5) {
    transition-delay: 0.4s;
}

/* دعم الوضع الداكن */
body.dark-theme .notification {
    background-color: var(--dark-card-bg, #1f2937);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

body.dark-theme .notification-title {
    color: #f3f4f6;
}

body.dark-theme .notification-message {
    color: #d1d5db;
}

body.dark-theme .notification-close {
    color: #9ca3af;
}

body.dark-theme .notification-close:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #f3f4f6;
}

body.dark-theme .notification-progress {
    background-color: rgba(255, 255, 255, 0.1);
}
