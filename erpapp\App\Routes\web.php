<?php
/**
 * Web Routes
 *
 * This file defines all the routes for the web application.
 */

// Home routes
$router->add('/', ['controller' => 'HomeController', 'action' => 'index']);
$router->add('/dashboard', ['controller' => 'DashboardController', 'action' => 'index']);

// Authentication routes
$router->add('/login', ['controller' => 'AuthController', 'action' => 'login']);
$router->add('/register', ['controller' => 'AuthController', 'action' => 'register']);
$router->add('/logout', ['controller' => 'AuthController', 'action' => 'logout']);
$router->add('/forgot-password', ['controller' => 'AuthController', 'action' => 'forgotPassword']);
$router->add('/reset-password/{token}', ['controller' => 'AuthController', 'action' => 'resetPassword']);

// Language routes
$router->add('/language/{lang}', ['controller' => 'LanguageController', 'action' => 'switch']);

// Settings routes
$router->add('/settings', ['controller' => 'App\Modules\Users\Controllers\SettingsController', 'action' => 'index']);
$router->add('/settings/theme/{theme}', ['controller' => 'App\Modules\Users\Controllers\SettingsController', 'action' => 'theme']);
$router->add('/settings/sidebar/{mode}', ['controller' => 'App\Modules\Users\Controllers\SettingsController', 'action' => 'sidebar']);
$router->add('/settings/content/{mode}', ['controller' => 'App\Modules\Users\Controllers\SettingsController', 'action' => 'content']);
$router->add('/settings/sound-notifications/{status}', ['controller' => 'App\Modules\Users\Controllers\SettingsController', 'action' => 'soundNotifications']);

// Profile route
$router->add('/profile', ['controller' => 'UserController', 'action' => 'profile']);

// Company routes
$router->add('/companies', ['controller' => 'CompanyController', 'action' => 'index']);
$router->add('/companies/create', ['controller' => 'CompanyController', 'action' => 'create']);
$router->add('/companies/store', ['controller' => 'CompanyController', 'action' => 'store']);
$router->add('/companies/{id}', ['controller' => 'CompanyController', 'action' => 'show']);
$router->add('/companies/{id}/edit', ['controller' => 'CompanyController', 'action' => 'edit']);
$router->add('/companies/{id}/update', ['controller' => 'CompanyController', 'action' => 'update']);
$router->add('/companies/{id}/deactivate', ['controller' => 'CompanyController', 'action' => 'deactivate']);
$router->add('/companies/accept-invitation/{id}', ['controller' => 'CompanyController', 'action' => 'acceptInvitation']);
$router->add('/companies/reject-invitation/{id}', ['controller' => 'CompanyController', 'action' => 'rejectInvitation']);
$router->add('/companies/{id}/users', ['controller' => 'CompanyUserController', 'action' => 'index']);
$router->add('/companies/{id}/programs', ['controller' => 'CompanyProgramController', 'action' => 'index']);
$router->add('/companies/{id}/positions', ['controller' => 'CompanyPositionController', 'action' => 'index']);
$router->add('/companies/{id}/settings', ['controller' => 'CompanySettingController', 'action' => 'index']);

// Program routes
$router->add('/programs', ['controller' => 'ProgramController', 'action' => 'index']);
$router->add('/programs/create', ['controller' => 'ProgramController', 'action' => 'create']);
$router->add('/programs/store', ['controller' => 'ProgramController', 'action' => 'store']);
$router->add('/programs/{id:\d+}', ['controller' => 'ProgramController', 'action' => 'show']);
$router->add('/programs/{id:\d+}/edit', ['controller' => 'ProgramController', 'action' => 'edit']);
$router->add('/programs/{id:\d+}/update', ['controller' => 'ProgramController', 'action' => 'update']);
$router->add('/programs/{id:\d+}/delete', ['controller' => 'ProgramController', 'action' => 'delete']);
$router->add('/programs/{id:\d+}/install', ['controller' => 'ProgramController', 'action' => 'install']);
$router->add('/programs/{id:\d+}/{company_id:\d+}/uninstall', ['controller' => 'ProgramController', 'action' => 'uninstall']);

// Error routes
$router->add('/404', ['controller' => 'ErrorController', 'action' => 'notFound']);
$router->add('/500', ['controller' => 'ErrorController', 'action' => 'serverError']);
