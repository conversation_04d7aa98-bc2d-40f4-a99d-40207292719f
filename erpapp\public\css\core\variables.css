:root {
    /* Main Colors - Modern Palette */
    --primary-color: #6366f1;
    --primary-light: #818cf8;
    --primary-dark: #4f46e5;
    --primary-gradient: linear-gradient(135deg, #6366f1, #4f46e5);

    --secondary-color: #ec4899;
    --secondary-light: #f472b6;
    --secondary-dark: #db2777;
    --secondary-gradient: linear-gradient(135deg, #ec4899, #db2777);

    /* Accent Colors */
    --accent-1: #14b8a6; /* Teal */
    --accent-2: #8b5cf6; /* Purple */
    --accent-3: #f59e0b; /* Amber */
    --accent-4: #06b6d4; /* Cyan */

    /* Neutral Colors - Refined Grays */
    --dark-color: #111827;
    --gray-900: #1f2937;
    --gray-800: #374151;
    --gray-700: #4b5563;
    --gray-600: #6b7280;
    --gray-500: #9ca3af;
    --gray-400: #d1d5db;
    --gray-300: #e5e7eb;
    --gray-200: #f3f4f6;
    --gray-100: #f9fafb;
    --light-color: #ffffff;

    /* Feedback Colors - Vibrant & Clear */
    --success-color: #10b981;
    --success-light: #34d399;
    --success-dark: #059669;
    --success-gradient: linear-gradient(135deg, #10b981, #059669);

    --info-color: #3b82f6;
    --info-light: #60a5fa;
    --info-dark: #2563eb;
    --info-gradient: linear-gradient(135deg, #3b82f6, #2563eb);

    --warning-color: #f59e0b;
    --warning-light: #fbbf24;
    --warning-dark: #d97706;
    --warning-gradient: linear-gradient(135deg, #f59e0b, #d97706);

    --danger-color: #ef4444;
    --danger-light: #f87171;
    --danger-dark: #dc2626;
    --danger-gradient: linear-gradient(135deg, #ef4444, #dc2626);

    /* Layout Variables - Enhanced */
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;
    --topbar-height: 70px;
    --border-radius-xs: 0.125rem;
    --border-radius-sm: 0.375rem;
    --border-radius: 0.5rem;
    --border-radius-md: 0.75rem;
    --border-radius-lg: 1rem;
    --border-radius-xl: 1.5rem;
    --border-radius-2xl: 2rem;
    --border-radius-full: 9999px;

    /* Modern Shadows */
    --box-shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
    --box-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --box-shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --box-shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --box-shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --box-shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

    /* Transitions */
    --transition-fast: 0.15s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
    --transition-bezier: cubic-bezier(0.4, 0, 0.2, 1);

    /* Spacing Scale */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    --spacing-16: 4rem;
    --spacing-20: 5rem;

    /* Light Theme Variables - Enhanced */
    --light-bg-color: #f9fafb;
    --light-bg-secondary: #f3f4f6;
    --light-text-color: #111827;
    --light-text-muted: #6b7280;
    --light-card-bg: #ffffff;
    --light-card-border: #e5e7eb;
    --light-border-color: #e5e7eb;
    --light-input-bg: #ffffff;
    --light-input-border: #d1d5db;
    --light-sidebar-bg: #ffffff;
    --light-topbar-bg: rgba(255, 255, 255, 0.9);
    --light-hover-bg: rgba(99, 102, 241, 0.05);
    --light-active-bg: rgba(99, 102, 241, 0.1);
    --light-scrollbar-thumb: #d1d5db;
    --light-scrollbar-track: #f3f4f6;

    /* Dark Theme Variables - Enhanced */
    --dark-bg-color: #111827;
    --dark-bg-secondary: #1f2937;
    --dark-text-color: #f9fafb;
    --dark-text-muted: #9ca3af;
    --dark-card-bg: #1f2937;
    --dark-card-border: #374151;
    --dark-border-color: #374151;
    --dark-input-bg: #374151;
    --dark-input-border: #4b5563;
    --dark-sidebar-bg: #1f2937;
    --dark-topbar-bg: rgba(31, 41, 55, 0.9);
    --dark-hover-bg: rgba(99, 102, 241, 0.15);
    --dark-active-bg: rgba(99, 102, 241, 0.2);
    --dark-scrollbar-thumb: #4b5563;
    --dark-scrollbar-track: #1f2937;
}
