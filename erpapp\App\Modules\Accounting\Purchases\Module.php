<?php
namespace Modules\Accounting\Purchases;

use App\Core\Module as BaseModule;

/**
 * وحدة المشتريات
 */
class Module extends BaseModule
{
    /**
     * اسم الوحدة
     */
    protected $name = 'Purchases';

    /**
     * وصف الوحدة
     */
    protected $description = 'إدارة المشتريات والموردين';

    /**
     * إصدار الوحدة
     */
    protected $version = '1.0.0';

    /**
     * الوحدات المطلوبة
     */
    protected $dependencies = ['System.Companies', 'System.Users'];

    /**
     * تسجيل المسارات الخاصة بالوحدة
     */
    public function registerRoutes()
    {
        // مسارات المشتريات
        add_route('GET', '/purchases', 'App\Modules\Accounting\Purchases\Controllers\PurchasesController@index');
        add_route('GET', '/purchases/orders', 'App\Modules\Accounting\Purchases\Controllers\PurchaseOrderController@index');
        add_route('GET', '/purchases/orders/create', 'App\Modules\Accounting\Purchases\Controllers\PurchaseOrderController@create');
        add_route('POST', '/purchases/orders/store', 'App\Modules\Accounting\Purchases\Controllers\PurchaseOrderController@store');
        add_route('GET', '/purchases/orders/{id}', 'App\Modules\Accounting\Purchases\Controllers\PurchaseOrderController@show');
        add_route('GET', '/purchases/orders/{id}/edit', 'App\Modules\Accounting\Purchases\Controllers\PurchaseOrderController@edit');
        add_route('POST', '/purchases/orders/{id}/update', 'App\Modules\Accounting\Purchases\Controllers\PurchaseOrderController@update');

        // مسارات الموردين
        add_route('GET', '/purchases/suppliers', 'App\Modules\Accounting\Purchases\Controllers\SupplierController@index');
        add_route('GET', '/purchases/suppliers/create', 'App\Modules\Accounting\Purchases\Controllers\SupplierController@create');
        add_route('POST', '/purchases/suppliers/store', 'App\Modules\Accounting\Purchases\Controllers\SupplierController@store');
        add_route('GET', '/purchases/suppliers/{id}', 'App\Modules\Accounting\Purchases\Controllers\SupplierController@show');
        add_route('GET', '/purchases/suppliers/{id}/edit', 'App\Modules\Accounting\Purchases\Controllers\SupplierController@edit');
        add_route('POST', '/purchases/suppliers/{id}/update', 'App\Modules\Accounting\Purchases\Controllers\SupplierController@update');

        // مسارات التقارير
        add_route('GET', '/purchases/reports', 'App\Modules\Accounting\Purchases\Controllers\ReportController@index');
        add_route('GET', '/purchases/reports/monthly', 'App\Modules\Accounting\Purchases\Controllers\ReportController@monthly');
        add_route('GET', '/purchases/reports/supplier', 'App\Modules\Accounting\Purchases\Controllers\ReportController@bySupplier');
    }

    /**
     * تهيئة الوحدة
     */
    public function boot()
    {
        parent::boot();

        // تسجيل المسارات
        $this->registerRoutes();

        // تسجيل الخدمات
        $this->registerServices();
    }

    /**
     * تسجيل الخدمات
     */
    protected function registerServices()
    {
        // يمكن تسجيل خدمات إضافية هنا
    }

    /**
     * التحقق من صلاحيات الوحدة
     */
    public function hasPermission($user_id, $company_id)
    {
        // التحقق من أن الشركة لديها اشتراك يسمح بوحدة المشتريات
        return true;
    }

    /**
     * الحصول على معلومات الوحدة
     */
    public function getInfo()
    {
        return [
            'name' => $this->name,
            'description' => $this->description,
            'version' => $this->version,
            'dependencies' => $this->dependencies,
            'category' => 'Accounting'
        ];
    }
}
