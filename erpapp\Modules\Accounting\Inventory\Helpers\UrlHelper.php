<?php
/**
 * URL Helper for Inventory Module
 * مساعد المسارات لوحدة المخزون
 */

class InventoryUrlHelper {
    
    private static $base_url = 'https://qqo.shq.mybluehost.me/';
    private static $module_path = 'inventory/';
    
    /**
     * Get base URL
     */
    public static function getBaseUrl() {
        return self::$base_url;
    }
    
    /**
     * Get inventory module URL
     */
    public static function getInventoryUrl($path = '') {
        return self::$base_url . self::$module_path . ltrim($path, '/');
    }
    
    /**
     * Get general base URL with path
     */
    public static function baseUrl($path = '') {
        return self::$base_url . ltrim($path, '/');
    }
    
    /**
     * Product URLs
     */
    public static function productsUrl($action = '', $id = null) {
        $url = self::getInventoryUrl('products');
        
        switch ($action) {
            case 'create':
                return $url . '/create';
            case 'show':
                return $url . '/' . $id;
            case 'edit':
                return $url . '/' . $id . '/edit';
            case 'update':
                return $url . '/' . $id . '/update';
            case 'delete':
                return $url . '/' . $id . '/delete';
            case 'reorder':
                return $url . '/reorder';
            default:
                return $url;
        }
    }
    
    /**
     * Category URLs
     */
    public static function categoriesUrl($action = '', $id = null) {
        $url = self::getInventoryUrl('categories');
        
        switch ($action) {
            case 'create':
                return $url . '/create';
            case 'show':
                return $url . '/' . $id;
            case 'edit':
                return $url . '/' . $id . '/edit';
            case 'update':
                return $url . '/' . $id . '/update';
            case 'delete':
                return $url . '/' . $id . '/delete';
            case 'hierarchy':
                return $url . '/hierarchy';
            case 'subcategories':
                return $url . '/' . $id . '/subcategories';
            default:
                return $url;
        }
    }
    
    /**
     * Unit URLs
     */
    public static function unitsUrl($action = '', $id = null) {
        $url = self::getInventoryUrl('units');
        
        switch ($action) {
            case 'create':
                return $url . '/create';
            case 'show':
                return $url . '/' . $id;
            case 'edit':
                return $url . '/' . $id . '/edit';
            case 'update':
                return $url . '/' . $id . '/update';
            case 'delete':
                return $url . '/' . $id . '/delete';
            case 'convert':
                return $url . '/convert';
            default:
                return $url;
        }
    }
    
    /**
     * Warehouse URLs
     */
    public static function warehousesUrl($action = '', $id = null, $type = null) {
        $url = self::getInventoryUrl('warehouses');
        
        switch ($action) {
            case 'create':
                return $url . '/create';
            case 'show':
                return $url . '/' . $id;
            case 'edit':
                return $url . '/' . $id . '/edit';
            case 'update':
                return $url . '/' . $id . '/update';
            case 'delete':
                return $url . '/' . $id . '/delete';
            case 'update_usage':
                return $url . '/' . $id . '/update-usage';
            case 'by_type':
                return $url . '/type/' . $type;
            default:
                return $url;
        }
    }
    
    /**
     * Stock URLs
     */
    public static function stockUrl($action = '', $product_id = null, $warehouse_id = null) {
        $url = self::getInventoryUrl('stock');
        
        switch ($action) {
            case 'show':
                return $url . '/' . $product_id . '/' . $warehouse_id;
            case 'adjust':
                return $url . '/' . $product_id . '/' . $warehouse_id . '/adjust';
            case 'process_adjustment':
                return $url . '/' . $product_id . '/' . $warehouse_id . '/process-adjustment';
            case 'reserve':
                return $url . '/' . $product_id . '/' . $warehouse_id . '/reserve';
            case 'unreserve':
                return $url . '/' . $product_id . '/' . $warehouse_id . '/unreserve';
            case 'total':
                return $url . '/product/' . $product_id . '/total';
            case 'out_of_stock':
                return $url . '/out-of-stock';
            case 'need_reorder':
                return $url . '/need-reorder';
            default:
                return $url;
        }
    }
    
    /**
     * Report URLs
     */
    public static function reportsUrl($type = '') {
        $url = self::getInventoryUrl('reports');
        
        if ($type) {
            return $url . '/' . $type;
        }
        
        return $url;
    }
    
    /**
     * Export URLs
     */
    public static function exportUrl($type) {
        return self::getInventoryUrl('export/' . $type);
    }
    
    /**
     * Search URL
     */
    public static function searchUrl() {
        return self::getInventoryUrl('search');
    }
    
    /**
     * Settings URL
     */
    public static function settingsUrl() {
        return self::getInventoryUrl('settings');
    }
    
    /**
     * Dashboard URLs
     */
    public static function dashboardUrl($page = '') {
        if ($page) {
            return self::getInventoryUrl($page);
        }
        return self::getInventoryUrl('');
    }
    
    /**
     * API URLs
     */
    public static function apiUrl($endpoint) {
        return self::getInventoryUrl('api/' . $endpoint);
    }
    
    /**
     * Asset URLs
     */
    public static function assetUrl($path) {
        return self::$base_url . 'assets/' . ltrim($path, '/');
    }
    
    /**
     * Upload URLs
     */
    public static function uploadUrl($type, $filename = '') {
        $base = self::$base_url . 'uploads/inventory/' . $type . '/';
        if ($filename) {
            return $base . $filename;
        }
        return $base;
    }
    
    /**
     * Generate breadcrumb for current page
     */
    public static function getBreadcrumb($page, $params = []) {
        $breadcrumbs = [
            [
                'title' => 'الرئيسية',
                'url' => self::baseUrl('dashboard'),
                'icon' => 'fas fa-home'
            ],
            [
                'title' => 'المخزون',
                'url' => self::getInventoryUrl(''),
                'icon' => 'fas fa-boxes'
            ]
        ];
        
        switch ($page) {
            case 'products':
                $breadcrumbs[] = ['title' => 'المنتجات', 'icon' => 'fas fa-cube'];
                break;
                
            case 'products_create':
                $breadcrumbs[] = ['title' => 'المنتجات', 'url' => self::productsUrl(), 'icon' => 'fas fa-cube'];
                $breadcrumbs[] = ['title' => 'إضافة منتج جديد', 'icon' => 'fas fa-plus'];
                break;
                
            case 'products_show':
                $breadcrumbs[] = ['title' => 'المنتجات', 'url' => self::productsUrl(), 'icon' => 'fas fa-cube'];
                $breadcrumbs[] = ['title' => 'تفاصيل المنتج', 'icon' => 'fas fa-eye'];
                break;
                
            case 'products_edit':
                $breadcrumbs[] = ['title' => 'المنتجات', 'url' => self::productsUrl(), 'icon' => 'fas fa-cube'];
                $breadcrumbs[] = ['title' => 'تعديل المنتج', 'icon' => 'fas fa-edit'];
                break;
                
            case 'categories':
                $breadcrumbs[] = ['title' => 'الفئات', 'icon' => 'fas fa-tags'];
                break;
                
            case 'categories_create':
                $breadcrumbs[] = ['title' => 'الفئات', 'url' => self::categoriesUrl(), 'icon' => 'fas fa-tags'];
                $breadcrumbs[] = ['title' => 'إضافة فئة جديدة', 'icon' => 'fas fa-plus'];
                break;
                
            case 'warehouses':
                $breadcrumbs[] = ['title' => 'المخازن', 'icon' => 'fas fa-warehouse'];
                break;
                
            case 'warehouses_create':
                $breadcrumbs[] = ['title' => 'المخازن', 'url' => self::warehousesUrl(), 'icon' => 'fas fa-warehouse'];
                $breadcrumbs[] = ['title' => 'إضافة مخزن جديد', 'icon' => 'fas fa-plus'];
                break;
                
            case 'stock':
                $breadcrumbs[] = ['title' => 'أرصدة المخزون', 'icon' => 'fas fa-chart-bar'];
                break;
                
            case 'reports':
                $breadcrumbs[] = ['title' => 'التقارير', 'icon' => 'fas fa-chart-line'];
                break;
        }
        
        return $breadcrumbs;
    }
    
    /**
     * Check if current URL matches pattern
     */
    public static function isCurrentUrl($pattern) {
        $current_url = $_SERVER['REQUEST_URI'] ?? '';
        return strpos($current_url, $pattern) !== false;
    }
    
    /**
     * Get current page name from URL
     */
    public static function getCurrentPage() {
        $current_url = $_SERVER['REQUEST_URI'] ?? '';
        
        if (strpos($current_url, '/inventory/products') !== false) {
            if (strpos($current_url, '/create') !== false) return 'products_create';
            if (strpos($current_url, '/edit') !== false) return 'products_edit';
            if (preg_match('/\/inventory\/products\/\d+$/', $current_url)) return 'products_show';
            return 'products';
        }
        
        if (strpos($current_url, '/inventory/categories') !== false) {
            if (strpos($current_url, '/create') !== false) return 'categories_create';
            if (strpos($current_url, '/edit') !== false) return 'categories_edit';
            if (preg_match('/\/inventory\/categories\/\d+$/', $current_url)) return 'categories_show';
            return 'categories';
        }
        
        if (strpos($current_url, '/inventory/warehouses') !== false) {
            if (strpos($current_url, '/create') !== false) return 'warehouses_create';
            if (strpos($current_url, '/edit') !== false) return 'warehouses_edit';
            if (preg_match('/\/inventory\/warehouses\/\d+$/', $current_url)) return 'warehouses_show';
            return 'warehouses';
        }
        
        if (strpos($current_url, '/inventory/stock') !== false) {
            return 'stock';
        }
        
        if (strpos($current_url, '/inventory/reports') !== false) {
            return 'reports';
        }
        
        if (strpos($current_url, '/inventory') !== false) {
            return 'dashboard';
        }
        
        return 'unknown';
    }
}

// Global helper functions for easier use
if (!function_exists('inventory_url')) {
    function inventory_url($path = '') {
        return InventoryUrlHelper::getInventoryUrl($path);
    }
}

if (!function_exists('base_url')) {
    function base_url($path = '') {
        return InventoryUrlHelper::baseUrl($path);
    }
}

if (!function_exists('products_url')) {
    function products_url($action = '', $id = null) {
        return InventoryUrlHelper::productsUrl($action, $id);
    }
}

if (!function_exists('categories_url')) {
    function categories_url($action = '', $id = null) {
        return InventoryUrlHelper::categoriesUrl($action, $id);
    }
}

if (!function_exists('warehouses_url')) {
    function warehouses_url($action = '', $id = null, $type = null) {
        return InventoryUrlHelper::warehousesUrl($action, $id, $type);
    }
}

if (!function_exists('stock_url')) {
    function stock_url($action = '', $product_id = null, $warehouse_id = null) {
        return InventoryUrlHelper::stockUrl($action, $product_id, $warehouse_id);
    }
}

if (!function_exists('reports_url')) {
    function reports_url($type = '') {
        return InventoryUrlHelper::reportsUrl($type);
    }
}

if (!function_exists('export_url')) {
    function export_url($type) {
        return InventoryUrlHelper::exportUrl($type);
    }
}
