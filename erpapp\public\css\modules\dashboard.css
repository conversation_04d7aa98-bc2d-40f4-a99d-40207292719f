/* Dashboard Module Styles */

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-8);
}

/* Dashboard Welcome */
.dashboard-welcome {
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-8);
    margin-bottom: var(--spacing-8);
    position: relative;
    overflow: hidden;
}

.dashboard-welcome::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(50%, -50%);
}

.dashboard-welcome-content {
    position: relative;
    z-index: 1;
}

.dashboard-welcome h1 {
    font-size: 2.25rem;
    font-weight: 700;
    margin-bottom: var(--spacing-2);
}

.dashboard-welcome p {
    font-size: 1.125rem;
    opacity: 0.9;
    margin-bottom: var(--spacing-6);
}

.dashboard-welcome-actions {
    display: flex;
    gap: var(--spacing-4);
    flex-wrap: wrap;
}

.dashboard-welcome-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: var(--spacing-3) var(--spacing-5);
    border-radius: var(--border-radius-lg);
    text-decoration: none;
    font-weight: 500;
    transition: all var(--transition-normal) var(--transition-bezier);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.dashboard-welcome-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    color: white;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-8);
}

.quick-action {
    background: var(--light-card-bg);
    border: 1px solid var(--light-border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-5);
    text-decoration: none;
    color: var(--light-text-color);
    transition: all var(--transition-normal) var(--transition-bezier);
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
    overflow: hidden;
}

body.dark-theme .quick-action {
    background: var(--dark-card-bg);
    border-color: var(--dark-border-color);
    color: var(--dark-text-color);
}

.quick-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: transform var(--transition-normal) var(--transition-bezier);
}

.quick-action:hover::before {
    transform: scaleX(1);
}

.quick-action:hover {
    transform: translateY(-4px);
    box-shadow: var(--box-shadow-lg);
    color: var(--light-text-color);
}

body.dark-theme .quick-action:hover {
    color: var(--dark-text-color);
}

.quick-action-icon {
    width: 3.5rem;
    height: 3.5rem;
    border-radius: var(--border-radius-lg);
    background: var(--primary-gradient);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: var(--spacing-4);
    box-shadow: var(--box-shadow-sm);
}

.quick-action-title {
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: var(--spacing-2);
}

.quick-action-desc {
    font-size: 0.875rem;
    color: var(--light-text-muted);
    margin: 0;
}

body.dark-theme .quick-action-desc {
    color: var(--dark-text-muted);
}

/* Recent Activity */
.recent-activity {
    background: var(--light-card-bg);
    border: 1px solid var(--light-border-color);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
}

body.dark-theme .recent-activity {
    background: var(--dark-card-bg);
    border-color: var(--dark-border-color);
}

.recent-activity-header {
    padding: var(--spacing-5) var(--spacing-6);
    border-bottom: 1px solid var(--light-border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

body.dark-theme .recent-activity-header {
    border-color: var(--dark-border-color);
}

.recent-activity-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--light-text-color);
    margin: 0;
}

body.dark-theme .recent-activity-title {
    color: var(--dark-text-color);
}

.recent-activity-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.recent-activity-item {
    padding: var(--spacing-4) var(--spacing-6);
    border-bottom: 1px solid var(--light-border-color);
    display: flex;
    align-items: center;
    transition: all var(--transition-normal) var(--transition-bezier);
}

body.dark-theme .recent-activity-item {
    border-color: var(--dark-border-color);
}

.recent-activity-item:last-child {
    border-bottom: none;
}

.recent-activity-item:hover {
    background-color: var(--light-hover-bg);
}

body.dark-theme .recent-activity-item:hover {
    background-color: var(--dark-hover-bg);
}

.recent-activity-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: var(--border-radius-full);
    background: var(--primary-gradient);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--spacing-4);
    flex-shrink: 0;
}

.rtl .recent-activity-icon {
    margin-right: 0;
    margin-left: var(--spacing-4);
}

.recent-activity-content {
    flex: 1;
}

.recent-activity-text {
    font-size: 0.9rem;
    color: var(--light-text-color);
    margin-bottom: var(--spacing-1);
}

body.dark-theme .recent-activity-text {
    color: var(--dark-text-color);
}

.recent-activity-time {
    font-size: 0.8rem;
    color: var(--light-text-muted);
}

body.dark-theme .recent-activity-time {
    color: var(--dark-text-muted);
}

/* Charts Container */
.charts-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

.chart-card {
    background: var(--light-card-bg);
    border: 1px solid var(--light-border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-6);
}

body.dark-theme .chart-card {
    background: var(--dark-card-bg);
    border-color: var(--dark-border-color);
}

.chart-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--light-text-color);
    margin-bottom: var(--spacing-5);
}

body.dark-theme .chart-title {
    color: var(--dark-text-color);
}

/* Responsive */
@media (max-width: 1024px) {
    .charts-container {
        grid-template-columns: 1fr;
    }
    
    .dashboard-stats {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .dashboard-welcome {
        padding: var(--spacing-6);
    }
    
    .dashboard-welcome h1 {
        font-size: 1.875rem;
    }
    
    .dashboard-welcome p {
        font-size: 1rem;
    }
    
    .dashboard-welcome-actions {
        flex-direction: column;
    }
    
    .quick-actions {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
    
    .dashboard-stats {
        grid-template-columns: 1fr;
    }
}
