-- إدخال البيانات الافتراضية للهيكل الجديد
-- مالك الشركة: المستخدم 32
-- الشركة: 4
-- المستخدم المضاف: 35

-- =====================================================
-- 1. إدخال الوحدات المتاحة في النظام
-- =====================================================
INSERT INTO `system_modules` (`module_code`, `module_name_ar`, `module_name_en`, `module_description_ar`, `module_description_en`, `base_url`, `icon_name`, `module_type`, `version`, `display_order`) VALUES
('inventory', 'وحدة إدارة المخزون', 'Inventory Management Module', 'إدارة شاملة للمنتجات والمستودعات وحركة المخزون', 'Complete management of products, warehouses and inventory movements', 'inventory', 'fas fa-boxes', 'addon', '1.0.0', 1),
('purchases', 'وحدة إدارة المشتريات', 'Purchases Management Module', 'إدارة طلبات الشراء والموردين وفواتير الشراء', 'Management of purchase orders, suppliers and purchase invoices', 'purchases', 'fas fa-shopping-cart', 'addon', '1.0.0', 2),
('sales', 'وحدة إدارة المبيعات', 'Sales Management Module', 'إدارة العملاء وعروض الأسعار وفواتير المبيعات', 'Management of customers, quotations and sales invoices', 'sales', 'fas fa-chart-line', 'addon', '1.0.0', 3),
('accounting', 'وحدة المحاسبة', 'Accounting Module', 'النظام المحاسبي الكامل مع دليل الحسابات والقيود', 'Complete accounting system with chart of accounts and journal entries', 'accounting', 'fas fa-calculator', 'addon', '1.0.0', 4),
('hr', 'وحدة الموارد البشرية', 'Human Resources Module', 'إدارة الموظفين والرواتب والحضور والإجازات', 'Management of employees, payroll, attendance and leaves', 'hr', 'fas fa-users', 'addon', '1.0.0', 5),
('reports', 'وحدة التقارير', 'Reports Module', 'تقارير شاملة لجميع أقسام النظام', 'Comprehensive reports for all system modules', 'reports', 'fas fa-chart-bar', 'core', '1.0.0', 6);

-- =====================================================
-- 2. تنزيل الوحدات للشركة رقم 4
-- =====================================================
INSERT INTO `company_modules` (`company_id`, `module_id`, `installed_by`, `license_expires_at`) VALUES
(4, 1, 32, '2025-12-31 23:59:59'), -- وحدة المخزون
(4, 2, 32, '2025-12-31 23:59:59'), -- وحدة المشتريات
(4, 3, 32, '2025-12-31 23:59:59'), -- وحدة المبيعات
(4, 4, 32, '2025-12-31 23:59:59'), -- وحدة المحاسبة
(4, 6, 32, '2025-12-31 23:59:59'); -- وحدة التقارير

-- =====================================================
-- 3. إضافة المناصب للشركة 4
-- =====================================================
INSERT INTO `positions` (`company_id`, `position_name_ar`, `position_name_en`, `description`, `created_by`) VALUES
(4, 'مدير عام', 'General Manager', 'صلاحيات كاملة على جميع الوحدات والبرامج', 32),
(4, 'مدير المخزون', 'Inventory Manager', 'صلاحيات كاملة على وحدة المخزون', 32),
(4, 'مدير المبيعات', 'Sales Manager', 'صلاحيات كاملة على وحدة المبيعات', 32),
(4, 'محاسب', 'Accountant', 'صلاحيات على وحدة المحاسبة والتقارير المالية', 32),
(4, 'موظف مخزون', 'Inventory Employee', 'صلاحيات محدودة على وحدة المخزون', 32);

-- =====================================================
-- 4. إضافة المستخدم 35 في الشركة 4
-- =====================================================
INSERT INTO `company_users` (`user_id`, `company_id`, `position_id`, `added_by`, `status`, `user_status`, `notes`) VALUES
(35, 4, 2, 32, 'accepted', 'active', 'مدير المخزون - تم إضافته من قبل مالك الشركة');

-- =====================================================
-- 5. إضافة البرامج الفرعية لوحدة المخزون
-- =====================================================
INSERT INTO `module_programs` (`company_id`, `module_id`, `parent_program_id`, `program_name_ar`, `program_name_en`, `program_code`, `page_url`, `icon_name`, `program_type`, `display_order`) VALUES
-- البرنامج الرئيسي للمخزون
(4, 1, NULL, 'إدارة المخزون', 'Inventory Management', 'inventory_main', 'inventory', 'fas fa-boxes', 'Main', 1),

-- البرامج الفرعية للمخزون
(4, 1, 1, 'المنتجات', 'Products', 'products', 'inventory/products', 'fas fa-box', 'Sub', 11),
(4, 1, 1, 'الفئات', 'Categories', 'categories', 'inventory/categories', 'fas fa-tags', 'Sub', 12),
(4, 1, 1, 'وحدات القياس', 'Units', 'units', 'inventory/units', 'fas fa-ruler', 'Sub', 13),
(4, 1, 1, 'المستودعات', 'Warehouses', 'warehouses', 'inventory/warehouses', 'fas fa-warehouse', 'Sub', 14),
(4, 1, 1, 'حركة المخزون', 'Stock Movements', 'movements', 'inventory/movements', 'fas fa-exchange-alt', 'Sub', 15);

-- =====================================================
-- 6. إضافة البرامج الفرعية لوحدة المشتريات
-- =====================================================
INSERT INTO `module_programs` (`company_id`, `module_id`, `parent_program_id`, `program_name_ar`, `program_name_en`, `program_code`, `page_url`, `icon_name`, `program_type`, `display_order`) VALUES
-- البرنامج الرئيسي للمشتريات
(4, 2, NULL, 'إدارة المشتريات', 'Purchases Management', 'purchases_main', 'purchases', 'fas fa-shopping-cart', 'Main', 2),

-- البرامج الفرعية للمشتريات
(4, 2, 7, 'طلبات الشراء', 'Purchase Orders', 'purchase_orders', 'purchases/orders', 'fas fa-file-alt', 'Sub', 21),
(4, 2, 7, 'الموردين', 'Suppliers', 'suppliers', 'purchases/suppliers', 'fas fa-truck', 'Sub', 22),
(4, 2, 7, 'استلام البضائع', 'Receiving', 'receiving', 'purchases/receiving', 'fas fa-dolly', 'Sub', 23),
(4, 2, 7, 'فواتير الشراء', 'Purchase Invoices', 'purchase_invoices', 'purchases/invoices', 'fas fa-file-invoice', 'Sub', 24);

-- =====================================================
-- 7. إضافة البرامج الفرعية لوحدة المبيعات
-- =====================================================
INSERT INTO `module_programs` (`company_id`, `module_id`, `parent_program_id`, `program_name_ar`, `program_name_en`, `program_code`, `page_url`, `icon_name`, `program_type`, `display_order`) VALUES
-- البرنامج الرئيسي للمبيعات
(4, 3, NULL, 'إدارة المبيعات', 'Sales Management', 'sales_main', 'sales', 'fas fa-chart-line', 'Main', 3),

-- البرامج الفرعية للمبيعات
(4, 3, 12, 'العملاء', 'Customers', 'customers', 'sales/customers', 'fas fa-users', 'Sub', 31),
(4, 3, 12, 'عروض الأسعار', 'Quotations', 'quotations', 'sales/quotations', 'fas fa-file-contract', 'Sub', 32),
(4, 3, 12, 'أوامر البيع', 'Sales Orders', 'sales_orders', 'sales/orders', 'fas fa-clipboard-list', 'Sub', 33),
(4, 3, 12, 'فواتير المبيعات', 'Sales Invoices', 'sales_invoices', 'sales/invoices', 'fas fa-file-invoice-dollar', 'Sub', 34);

-- =====================================================
-- 8. إضافة البرامج الفرعية لوحدة المحاسبة
-- =====================================================
INSERT INTO `module_programs` (`company_id`, `module_id`, `parent_program_id`, `program_name_ar`, `program_name_en`, `program_code`, `page_url`, `icon_name`, `program_type`, `display_order`) VALUES
-- البرنامج الرئيسي للمحاسبة
(4, 4, NULL, 'المحاسبة', 'Accounting', 'accounting_main', 'accounting', 'fas fa-calculator', 'Main', 4),

-- البرامج الفرعية للمحاسبة
(4, 4, 17, 'دليل الحسابات', 'Chart of Accounts', 'chart_accounts', 'accounting/accounts', 'fas fa-list', 'Sub', 41),
(4, 4, 17, 'القيود اليومية', 'Journal Entries', 'journal_entries', 'accounting/entries', 'fas fa-book', 'Sub', 42),
(4, 4, 17, 'المدفوعات', 'Payments', 'payments', 'accounting/payments', 'fas fa-credit-card', 'Sub', 43),
(4, 4, 17, 'المقبوضات', 'Receipts', 'receipts', 'accounting/receipts', 'fas fa-money-bill', 'Sub', 44);

-- =====================================================
-- 9. إضافة البرامج الفرعية لوحدة التقارير
-- =====================================================
INSERT INTO `module_programs` (`company_id`, `module_id`, `parent_program_id`, `program_name_ar`, `program_name_en`, `program_code`, `page_url`, `icon_name`, `program_type`, `display_order`) VALUES
-- البرنامج الرئيسي للتقارير
(4, 6, NULL, 'التقارير', 'Reports', 'reports_main', 'reports', 'fas fa-chart-bar', 'Main', 6),

-- البرامج الفرعية للتقارير
(4, 6, 22, 'تقارير المخزون', 'Inventory Reports', 'inventory_reports', 'reports/inventory', 'fas fa-chart-line', 'Sub', 61),
(4, 6, 22, 'التقارير المالية', 'Financial Reports', 'financial_reports', 'reports/financial', 'fas fa-chart-pie', 'Sub', 62),
(4, 6, 22, 'تقارير المبيعات', 'Sales Reports', 'sales_reports', 'reports/sales', 'fas fa-chart-area', 'Sub', 63),
(4, 6, 22, 'تقارير المشتريات', 'Purchases Reports', 'purchases_reports', 'reports/purchases', 'fas fa-chart-column', 'Sub', 64);

-- =====================================================
-- 10. إضافة الصلاحيات للمناصب
-- =====================================================

-- صلاحيات مدير عام (منصب 1) - صلاحيات كاملة على جميع البرامج
INSERT INTO `permissions` (`company_id`, `position_id`, `program_id`, `can_view`, `can_create`, `can_edit`, `can_delete`, `can_approve`, `created_by`) 
SELECT 4, 1, program_id, 1, 1, 1, 1, 1, 32
FROM `module_programs` 
WHERE company_id = 4;

-- صلاحيات مدير المخزون (منصب 2) - صلاحيات كاملة على وحدة المخزون فقط
INSERT INTO `permissions` (`company_id`, `position_id`, `program_id`, `can_view`, `can_create`, `can_edit`, `can_delete`, `can_approve`, `created_by`) 
SELECT 4, 2, program_id, 1, 1, 1, 1, 0, 32
FROM `module_programs` 
WHERE company_id = 4 AND module_id = 1; -- وحدة المخزون

-- صلاحيات مدير المبيعات (منصب 3) - صلاحيات كاملة على وحدة المبيعات
INSERT INTO `permissions` (`company_id`, `position_id`, `program_id`, `can_view`, `can_create`, `can_edit`, `can_delete`, `can_approve`, `created_by`) 
SELECT 4, 3, program_id, 1, 1, 1, 1, 0, 32
FROM `module_programs` 
WHERE company_id = 4 AND module_id = 3; -- وحدة المبيعات

-- صلاحيات المحاسب (منصب 4) - صلاحيات على المحاسبة والتقارير
INSERT INTO `permissions` (`company_id`, `position_id`, `program_id`, `can_view`, `can_create`, `can_edit`, `can_delete`, `can_approve`, `created_by`) 
SELECT 4, 4, program_id, 1, 1, 1, 0, 0, 32
FROM `module_programs` 
WHERE company_id = 4 AND module_id IN (4, 6); -- المحاسبة والتقارير

-- صلاحيات موظف المخزون (منصب 5) - صلاحيات محدودة على المخزون
INSERT INTO `permissions` (`company_id`, `position_id`, `program_id`, `can_view`, `can_create`, `can_edit`, `can_delete`, `can_approve`, `created_by`) 
SELECT 4, 5, program_id, 1, 1, 0, 0, 0, 32
FROM `module_programs` 
WHERE company_id = 4 AND module_id = 1; -- وحدة المخزون

-- =====================================================
-- 11. تحديث الشركة الحالية للمستخدمين
-- =====================================================
UPDATE `users` SET `current_company_id` = 4 WHERE `UserID` IN (32, 35);

-- =====================================================
-- تم الانتهاء من إدخال البيانات الافتراضية للهيكل الجديد
-- =====================================================

-- للتحقق من البيانات المدخلة:
-- SELECT * FROM system_modules ORDER BY display_order;
-- SELECT * FROM company_modules WHERE company_id = 4;
-- SELECT * FROM positions WHERE company_id = 4;
-- SELECT * FROM company_users WHERE company_id = 4;
-- SELECT * FROM module_programs WHERE company_id = 4 ORDER BY module_id, program_type DESC, display_order;
-- SELECT COUNT(*) as total_permissions FROM permissions WHERE company_id = 4;
