<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title">
                    <i class="fas fa-plus-circle text-primary"></i> <?= $title ?? 'إضافة فئة جديدة' ?>
                </h1>
                <a href="<?= base_url('inventory/categories') ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i> العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (isset($_SESSION['flash_message'])): ?>
        <div class="alert alert-<?= $_SESSION['flash_message']['type'] ?> alert-dismissible fade show" role="alert">
            <?= $_SESSION['flash_message']['message'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['flash_message']); ?>
    <?php endif; ?>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i> معلومات الفئة
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?= base_url('inventory/categories/store') ?>" id="categoryForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="category_code" class="form-label">كود الفئة <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="category_code" name="category_code" 
                                       value="<?= htmlspecialchars($category['category_code'] ?? '') ?>" required>
                                <div class="form-text">كود فريد للفئة</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="parent_category_id" class="form-label">الفئة الرئيسية</label>
                                <select class="form-select" id="parent_category_id" name="parent_category_id">
                                    <option value="">فئة رئيسية</option>
                                    <?php foreach ($parent_categories as $parent): ?>
                                        <option value="<?= $parent['category_id'] ?>" 
                                                <?= ($category['parent_category_id'] ?? '') == $parent['category_id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($parent['category_name_ar']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text">اختر الفئة الرئيسية إذا كانت هذه فئة فرعية</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="category_name_ar" class="form-label">اسم الفئة (عربي) <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="category_name_ar" name="category_name_ar" 
                                       value="<?= htmlspecialchars($category['category_name_ar'] ?? '') ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="category_name_en" class="form-label">اسم الفئة (إنجليزي)</label>
                                <input type="text" class="form-control" id="category_name_en" name="category_name_en" 
                                       value="<?= htmlspecialchars($category['category_name_en'] ?? '') ?>">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="description_ar" class="form-label">الوصف (عربي)</label>
                                <textarea class="form-control" id="description_ar" name="description_ar" rows="3"><?= htmlspecialchars($category['description_ar'] ?? '') ?></textarea>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="description_en" class="form-label">الوصف (إنجليزي)</label>
                                <textarea class="form-control" id="description_en" name="description_en" rows="3"><?= htmlspecialchars($category['description_en'] ?? '') ?></textarea>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="display_order" class="form-label">ترتيب العرض</label>
                                <input type="number" class="form-control" id="display_order" name="display_order" 
                                       value="<?= $category['display_order'] ?? '1' ?>" min="1">
                                <div class="form-text">ترتيب عرض الفئة في القوائم</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="image_url" class="form-label">رابط الصورة</label>
                                <input type="url" class="form-control" id="image_url" name="image_url" 
                                       value="<?= htmlspecialchars($category['image_url'] ?? '') ?>">
                                <div class="form-text">رابط صورة الفئة (اختياري)</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           <?= ($category['is_active'] ?? 1) ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="is_active">
                                        فئة نشطة
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Preview Section -->
                        <div class="row" id="preview_section" style="display: none;">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-eye me-2"></i> معاينة الفئة:</h6>
                                    <div id="category_preview"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-outline-info" onclick="previewCategory()">
                                        <i class="fas fa-eye me-2"></i> معاينة
                                    </button>
                                    <div>
                                        <a href="<?= base_url('inventory/categories') ?>" class="btn btn-outline-secondary me-2">
                                            <i class="fas fa-times me-2"></i> إلغاء
                                        </a>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i> حفظ الفئة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Help Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="m-0">
                        <i class="fas fa-info-circle me-2"></i> نصائح مفيدة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-info">كود الفئة:</h6>
                            <ul class="small">
                                <li>يجب أن يكون فريداً ولا يتكرر</li>
                                <li>يُفضل استخدام أحرف إنجليزية وأرقام فقط</li>
                                <li>مثال: ELECTRONICS, CLOTHING, FOOD</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-info">الفئات الهرمية:</h6>
                            <ul class="small">
                                <li>يمكن إنشاء فئات فرعية تحت الفئات الرئيسية</li>
                                <li>مثال: إلكترونيات > هواتف ذكية</li>
                                <li>يساعد في تنظيم المنتجات بشكل أفضل</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate category code
    document.getElementById('category_name_ar').addEventListener('input', function() {
        const categoryCodeInput = document.getElementById('category_code');
        
        if (!categoryCodeInput.value) {
            const arabicName = this.value.trim();
            if (arabicName) {
                // Convert Arabic to English-like code
                let code = arabicName
                    .replace(/\s+/g, '_')
                    .replace(/[^\w\u0600-\u06FF]/g, '')
                    .toUpperCase();
                
                // If still contains Arabic, use timestamp
                if (/[\u0600-\u06FF]/.test(code)) {
                    code = 'CAT_' + Date.now().toString().slice(-6);
                }
                
                categoryCodeInput.value = code;
            }
        }
    });

    // Form validation
    document.getElementById('categoryForm').addEventListener('submit', function(e) {
        const categoryCode = document.getElementById('category_code').value.trim();
        const categoryNameAr = document.getElementById('category_name_ar').value.trim();

        if (!categoryCode || !categoryNameAr) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
            return false;
        }

        // Check for valid category code format
        if (!/^[A-Z0-9_]+$/.test(categoryCode)) {
            e.preventDefault();
            alert('كود الفئة يجب أن يحتوي على أحرف إنجليزية كبيرة وأرقام وشرطة سفلية فقط');
            return false;
        }
    });
});

function previewCategory() {
    const categoryCode = document.getElementById('category_code').value;
    const categoryNameAr = document.getElementById('category_name_ar').value;
    const categoryNameEn = document.getElementById('category_name_en').value;
    const parentCategorySelect = document.getElementById('parent_category_id');
    const parentCategoryName = parentCategorySelect.options[parentCategorySelect.selectedIndex].text;
    const displayOrder = document.getElementById('display_order').value;
    const isActive = document.getElementById('is_active').checked;

    if (!categoryCode || !categoryNameAr) {
        alert('يرجى ملء الحقول المطلوبة أولاً');
        return;
    }

    const previewHtml = `
        <div class="row">
            <div class="col-md-6">
                <strong>كود الفئة:</strong> ${categoryCode}<br>
                <strong>اسم الفئة (عربي):</strong> ${categoryNameAr}<br>
                ${categoryNameEn ? `<strong>اسم الفئة (إنجليزي):</strong> ${categoryNameEn}<br>` : ''}
                <strong>الفئة الرئيسية:</strong> ${parentCategorySelect.value ? parentCategoryName : 'فئة رئيسية'}<br>
            </div>
            <div class="col-md-6">
                <strong>ترتيب العرض:</strong> ${displayOrder}<br>
                <strong>الحالة:</strong> ${isActive ? '<span class="badge bg-success">نشط</span>' : '<span class="badge bg-secondary">غير نشط</span>'}<br>
            </div>
        </div>
    `;

    document.getElementById('category_preview').innerHTML = previewHtml;
    document.getElementById('preview_section').style.display = 'block';
}
</script>
