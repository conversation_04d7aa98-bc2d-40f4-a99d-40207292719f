<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title">
                    <i class="fas fa-chart-bar text-primary"></i> <?= $title ?? 'أرصدة المخزون' ?>
                </h1>
                <div class="btn-group">
                    <a href="<?= base_url('inventory/stock/out-of-stock') ?>" class="btn btn-outline-danger">
                        <i class="fas fa-exclamation-circle me-2"></i> المنتجات النافدة
                    </a>
                    <a href="<?= base_url('inventory/stock/need-reorder') ?>" class="btn btn-outline-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i> يحتاج إعادة طلب
                    </a>
                    <a href="<?= base_url('inventory/export/stock') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-download me-2"></i> تصدير
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (isset($_SESSION['flash_message'])): ?>
        <div class="alert alert-<?= $_SESSION['flash_message']['type'] ?> alert-dismissible fade show" role="alert">
            <?= $_SESSION['flash_message']['message'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['flash_message']); ?>
    <?php endif; ?>

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي العناصر</h6>
                            <h3><?= number_format($total_count) ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-boxes fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">قيمة المخزون</h6>
                            <h3><?= number_format(array_sum(array_column($stock_items, 'total_value')), 2) ?></h3>
                            <small>ر.س</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">منتجات نافدة</h6>
                            <h3><?= number_format(count(array_filter($stock_items, function($s) { return ($s['quantity_on_hand'] ?? 0) <= 0; }))) ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-times-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">يحتاج إعادة طلب</h6>
                            <h3><?= number_format(count(array_filter($stock_items, function($s) { return ($s['quantity_available'] ?? 0) <= ($s['reorder_point'] ?? 0); }))) ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-filter me-2"></i> فلترة النتائج
                    </h6>
                </div>
                <div class="card-body">
                    <form method="GET" action="<?= base_url('inventory/stock') ?>">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="search" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="<?= htmlspecialchars($filters['search'] ?? '') ?>" 
                                       placeholder="اسم المنتج أو الكود">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="warehouse_id" class="form-label">المخزن</label>
                                <select class="form-select" id="warehouse_id" name="warehouse_id">
                                    <option value="">جميع المخازن</option>
                                    <?php foreach ($warehouses as $warehouse): ?>
                                        <option value="<?= $warehouse['warehouse_id'] ?>" 
                                                <?= ($filters['warehouse_id'] ?? '') == $warehouse['warehouse_id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($warehouse['warehouse_name_ar']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="category_id" class="form-label">الفئة</label>
                                <select class="form-select" id="category_id" name="category_id">
                                    <option value="">جميع الفئات</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?= $category['category_id'] ?>" 
                                                <?= ($filters['category_id'] ?? '') == $category['category_id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($category['category_name_ar']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="stock_status" class="form-label">حالة المخزون</label>
                                <select class="form-select" id="stock_status" name="stock_status">
                                    <option value="">جميع الحالات</option>
                                    <option value="available" <?= ($filters['stock_status'] ?? '') == 'available' ? 'selected' : '' ?>>متوفر</option>
                                    <option value="low" <?= ($filters['stock_status'] ?? '') == 'low' ? 'selected' : '' ?>>منخفض</option>
                                    <option value="out_of_stock" <?= ($filters['stock_status'] ?? '') == 'out_of_stock' ? 'selected' : '' ?>>نافد</option>
                                    <option value="need_reorder" <?= ($filters['stock_status'] ?? '') == 'need_reorder' ? 'selected' : '' ?>>يحتاج إعادة طلب</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search me-1"></i> بحث
                                </button>
                                <a href="<?= base_url('inventory/stock') ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i> إعادة تعيين
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Stock Table -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">أرصدة المخزون</h6>
                </div>
                <div class="card-body">
                    <?php if (empty($stock_items)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-chart-bar fa-3x text-gray-300 mb-3"></i>
                            <h5 class="text-muted">لا توجد أرصدة مخزون</h5>
                            <p class="text-muted">لم يتم العثور على أرصدة تطابق معايير البحث</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="stockTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>المنتج</th>
                                        <th>المخزن</th>
                                        <th>الكمية المتاحة</th>
                                        <th>الكمية المحجوزة</th>
                                        <th>إجمالي الكمية</th>
                                        <th>متوسط التكلفة</th>
                                        <th>القيمة الإجمالية</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($stock_items as $stock): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?= htmlspecialchars($stock['product_name_ar']) ?></strong>
                                                    <br><small class="text-muted"><?= htmlspecialchars($stock['product_code']) ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <?= htmlspecialchars($stock['warehouse_name_ar']) ?>
                                                    <br><small class="text-muted"><?= htmlspecialchars($stock['warehouse_code']) ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary"><?= number_format($stock['quantity_available'], 2) ?></span>
                                                <br><small class="text-muted"><?= htmlspecialchars($stock['unit_symbol_ar'] ?? '') ?></small>
                                            </td>
                                            <td>
                                                <?php if (($stock['quantity_reserved'] ?? 0) > 0): ?>
                                                    <span class="badge bg-warning"><?= number_format($stock['quantity_reserved'], 2) ?></span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?= number_format($stock['quantity_on_hand'], 2) ?></span>
                                            </td>
                                            <td>
                                                <?= number_format($stock['average_cost'], 2) ?> ر.س
                                            </td>
                                            <td>
                                                <strong><?= number_format($stock['total_value'], 2) ?> ر.س</strong>
                                            </td>
                                            <td>
                                                <?php 
                                                $quantity_available = $stock['quantity_available'] ?? 0;
                                                $reorder_point = $stock['reorder_point'] ?? 0;
                                                $min_stock_level = $stock['min_stock_level'] ?? 0;
                                                
                                                if ($quantity_available <= 0) {
                                                    echo '<span class="badge bg-danger">نافد</span>';
                                                } elseif ($quantity_available <= $reorder_point) {
                                                    echo '<span class="badge bg-warning">يحتاج طلب</span>';
                                                } elseif ($quantity_available <= $min_stock_level) {
                                                    echo '<span class="badge bg-info">منخفض</span>';
                                                } else {
                                                    echo '<span class="badge bg-success">متوفر</span>';
                                                }
                                                ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="<?= base_url('inventory/stock/' . $stock['product_id'] . '/' . $stock['warehouse_id']) ?>" 
                                                       class="btn btn-outline-primary" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('inventory/stock/' . $stock['product_id'] . '/' . $stock['warehouse_id'] . '/adjust') ?>" 
                                                       class="btn btn-outline-secondary" title="تعديل الرصيد">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php if ($quantity_available > 0): ?>
                                                    <button type="button" class="btn btn-outline-warning" 
                                                            onclick="reserveStock(<?= $stock['product_id'] ?>, <?= $stock['warehouse_id'] ?>)" title="حجز كمية">
                                                        <i class="fas fa-lock"></i>
                                                    </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reserve Stock Modal -->
<div class="modal fade" id="reserveModal" tabindex="-1" aria-labelledby="reserveModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="reserveModalLabel">
                    <i class="fas fa-lock me-2"></i> حجز كمية من المخزون
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="reserveForm" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="reserve_quantity" class="form-label">الكمية المراد حجزها</label>
                        <input type="number" class="form-control" id="reserve_quantity" name="quantity" 
                               step="0.01" min="0.01" required>
                        <div class="form-text">الكمية المتاحة: <span id="available_quantity"></span></div>
                    </div>
                    <div class="mb-3">
                        <label for="reserve_reason" class="form-label">سبب الحجز</label>
                        <textarea class="form-control" id="reserve_reason" name="reason" rows="3" 
                                  placeholder="اذكر سبب حجز هذه الكمية..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-lock me-1"></i> حجز الكمية
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function reserveStock(productId, warehouseId) {
    const reserveForm = document.getElementById('reserveForm');
    reserveForm.action = `<?= base_url('inventory/stock/') ?>${productId}/${warehouseId}/reserve`;
    
    // Find available quantity from table
    const row = event.target.closest('tr');
    const availableQuantityCell = row.cells[2].querySelector('.badge');
    const availableQuantity = availableQuantityCell.textContent.replace(/[^\d.-]/g, '');
    
    document.getElementById('available_quantity').textContent = availableQuantity;
    document.getElementById('reserve_quantity').max = availableQuantity;
    
    const reserveModal = new bootstrap.Modal(document.getElementById('reserveModal'));
    reserveModal.show();
}

// Initialize DataTable if available
document.addEventListener('DOMContentLoaded', function() {
    if (typeof $.fn.DataTable !== 'undefined') {
        $('#stockTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
            },
            "pageLength": 25,
            "order": [[0, "asc"]],
            "columnDefs": [
                { "orderable": false, "targets": -1 }
            ]
        });
    }
});
</script>
