<?php
/**
 * Inventory Module Router
 * موجه مسارات وحدة المخزون
 */

// Include configuration
require_once __DIR__ . '/config/inventory_config.php';
require_once __DIR__ . '/Modules/Accounting/Inventory/Helpers/UrlHelper.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Get the current request URI
$request_uri = $_SERVER['REQUEST_URI'] ?? '';
$request_method = $_SERVER['REQUEST_METHOD'] ?? 'GET';

// Remove query string
$request_uri = strtok($request_uri, '?');

// Remove leading slash
$request_uri = ltrim($request_uri, '/');

// Parse the route
$route_parts = explode('/', $request_uri);

// Check if this is an inventory route
if ($route_parts[0] !== 'inventory') {
    // Not an inventory route, let the main system handle it
    return false;
}

// Remove 'inventory' from the route parts
array_shift($route_parts);

// Determine controller and action
$controller = 'InventoryController';
$action = 'index';
$params = [];

if (!empty($route_parts)) {
    switch ($route_parts[0]) {
        case '':
        case 'dashboard':
            $controller = 'InventoryController';
            $action = empty($route_parts[0]) ? 'index' : 'dashboard';
            break;
            
        case 'products':
            $controller = 'ProductController';
            $action = 'index';
            
            if (isset($route_parts[1])) {
                if ($route_parts[1] === 'create') {
                    $action = 'create';
                } elseif ($route_parts[1] === 'reorder') {
                    $action = 'reorder';
                } elseif (is_numeric($route_parts[1])) {
                    $params['id'] = $route_parts[1];
                    
                    if (isset($route_parts[2])) {
                        switch ($route_parts[2]) {
                            case 'edit':
                                $action = 'edit';
                                break;
                            case 'update':
                                $action = 'update';
                                break;
                            case 'delete':
                                $action = 'delete';
                                break;
                            default:
                                $action = 'show';
                        }
                    } else {
                        $action = 'show';
                    }
                }
            }
            
            // Handle POST requests
            if ($request_method === 'POST') {
                if (isset($route_parts[1]) && $route_parts[1] === 'store') {
                    $action = 'store';
                } elseif (isset($route_parts[2])) {
                    switch ($route_parts[2]) {
                        case 'update':
                            $action = 'update';
                            break;
                        case 'delete':
                            $action = 'delete';
                            break;
                    }
                }
            }
            break;
            
        case 'categories':
            $controller = 'CategoryController';
            $action = 'index';
            
            if (isset($route_parts[1])) {
                if ($route_parts[1] === 'create') {
                    $action = 'create';
                } elseif ($route_parts[1] === 'hierarchy') {
                    $action = 'hierarchy';
                } elseif (is_numeric($route_parts[1])) {
                    $params['id'] = $route_parts[1];
                    
                    if (isset($route_parts[2])) {
                        switch ($route_parts[2]) {
                            case 'edit':
                                $action = 'edit';
                                break;
                            case 'update':
                                $action = 'update';
                                break;
                            case 'delete':
                                $action = 'delete';
                                break;
                            case 'subcategories':
                                $action = 'getSubCategories';
                                break;
                            default:
                                $action = 'show';
                        }
                    } else {
                        $action = 'show';
                    }
                }
            }
            
            // Handle POST requests
            if ($request_method === 'POST') {
                if (isset($route_parts[1]) && $route_parts[1] === 'store') {
                    $action = 'store';
                } elseif (isset($route_parts[2])) {
                    switch ($route_parts[2]) {
                        case 'update':
                            $action = 'update';
                            break;
                        case 'delete':
                            $action = 'delete';
                            break;
                    }
                }
            }
            break;
            
        case 'units':
            $controller = 'UnitController';
            $action = 'index';
            
            if (isset($route_parts[1])) {
                if ($route_parts[1] === 'create') {
                    $action = 'create';
                } elseif ($route_parts[1] === 'convert') {
                    $action = 'convert';
                } elseif (is_numeric($route_parts[1])) {
                    $params['id'] = $route_parts[1];
                    
                    if (isset($route_parts[2])) {
                        switch ($route_parts[2]) {
                            case 'edit':
                                $action = 'edit';
                                break;
                            case 'update':
                                $action = 'update';
                                break;
                            case 'delete':
                                $action = 'delete';
                                break;
                            default:
                                $action = 'show';
                        }
                    } else {
                        $action = 'show';
                    }
                }
            }
            break;
            
        case 'warehouses':
            $controller = 'WarehouseController';
            $action = 'index';
            
            if (isset($route_parts[1])) {
                if ($route_parts[1] === 'create') {
                    $action = 'create';
                } elseif ($route_parts[1] === 'type' && isset($route_parts[2])) {
                    $action = 'getByType';
                    $params['type'] = $route_parts[2];
                } elseif (is_numeric($route_parts[1])) {
                    $params['id'] = $route_parts[1];
                    
                    if (isset($route_parts[2])) {
                        switch ($route_parts[2]) {
                            case 'edit':
                                $action = 'edit';
                                break;
                            case 'update':
                                $action = 'update';
                                break;
                            case 'delete':
                                $action = 'delete';
                                break;
                            case 'update-usage':
                                $action = 'updateUsage';
                                break;
                            default:
                                $action = 'show';
                        }
                    } else {
                        $action = 'show';
                    }
                }
            }
            break;
            
        case 'stock':
            $controller = 'StockController';
            $action = 'index';
            
            if (isset($route_parts[1])) {
                if ($route_parts[1] === 'out-of-stock') {
                    $action = 'outOfStock';
                } elseif ($route_parts[1] === 'need-reorder') {
                    $action = 'needReorder';
                } elseif ($route_parts[1] === 'product' && isset($route_parts[2])) {
                    $action = 'totalStock';
                    $params['product_id'] = $route_parts[2];
                } elseif (is_numeric($route_parts[1]) && isset($route_parts[2]) && is_numeric($route_parts[2])) {
                    $params['product_id'] = $route_parts[1];
                    $params['warehouse_id'] = $route_parts[2];
                    
                    if (isset($route_parts[3])) {
                        switch ($route_parts[3]) {
                            case 'adjust':
                                $action = 'adjust';
                                break;
                            case 'process-adjustment':
                                $action = 'processAdjustment';
                                break;
                            case 'reserve':
                                $action = 'reserve';
                                break;
                            case 'unreserve':
                                $action = 'unreserve';
                                break;
                            default:
                                $action = 'show';
                        }
                    } else {
                        $action = 'show';
                    }
                }
            }
            break;
            
        case 'reports':
            $controller = 'InventoryController';
            $action = 'reports';
            
            if (isset($route_parts[1])) {
                // Specific report type
                $params['report_type'] = $route_parts[1];
            }
            break;
            
        case 'export':
            $controller = 'InventoryController';
            $action = 'export';
            
            if (isset($route_parts[1])) {
                $params['type'] = $route_parts[1];
            }
            break;
            
        case 'search':
            $controller = 'InventoryController';
            $action = 'search';
            break;
            
        case 'settings':
            $controller = 'InventoryController';
            $action = 'settings';
            break;
            
        default:
            // Unknown route, show 404
            http_response_code(404);
            echo "404 - Page Not Found";
            exit;
    }
}

// Build controller class name
$controller_class = 'Modules\\Accounting\\Inventory\\Controllers\\' . $controller;

// Check if controller file exists
$controller_file = __DIR__ . '/Modules/Accounting/Inventory/Controllers/' . $controller . '.php';

if (!file_exists($controller_file)) {
    http_response_code(404);
    echo "404 - Controller Not Found: " . $controller;
    exit;
}

// Include the controller
require_once $controller_file;

// Check if controller class exists
if (!class_exists($controller_class)) {
    http_response_code(500);
    echo "500 - Controller Class Not Found: " . $controller_class;
    exit;
}

// Create controller instance
try {
    $controller_instance = new $controller_class();
    
    // Check if action method exists
    if (!method_exists($controller_instance, $action)) {
        http_response_code(404);
        echo "404 - Action Not Found: " . $action;
        exit;
    }
    
    // Set parameters in $_GET for backward compatibility
    foreach ($params as $key => $value) {
        $_GET[$key] = $value;
    }
    
    // Call the action
    $controller_instance->$action();
    
} catch (Exception $e) {
    http_response_code(500);
    error_log("Inventory Router Error: " . $e->getMessage());
    echo "500 - Internal Server Error";
    exit;
}
