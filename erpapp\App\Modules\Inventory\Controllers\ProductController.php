<?php
namespace App\Modules\Inventory\Controllers;

use App\Modules\Inventory\Models\Product;
use App\Modules\Inventory\Services\ProductService;

/**
 * Product Controller - متحكم المنتجات
 */
class ProductController 
{
    /**
     * Route parameters
     */
    protected $params = [];

    /**
     * Product model
     */
    protected $productModel;

    /**
     * Product service
     */
    protected $productService;

    /**
     * Constructor
     */
    public function __construct($params = []) 
    {
        $this->params = $params;
        $this->productModel = new Product();
        $this->productService = new ProductService();

        // التحقق من تسجيل الدخول
        if (!is_logged_in()) {
            redirect(base_url('login'));
        }
    }

    /**
     * عرض قائمة المنتجات
     */
    public function index() 
    {
        try {
            $company_id = current_user()['current_company_id'] ?? 1;
            
            // الحصول على المنتجات
            $products = $this->productModel->getByCompany($company_id);
            
            $data = [
                'title' => 'إدارة المنتجات',
                'products' => $products,
                'breadcrumb' => [
                    ['title' => 'المخزون', 'url' => base_url('inventory')],
                    ['title' => 'المنتجات', 'active' => true]
                ]
            ];

            view('Inventory::products/index', $data);

        } catch (Exception $e) {
            flash('product_error', 'حدث خطأ أثناء تحميل المنتجات: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory'));
        }
    }

    /**
     * عرض نموذج إضافة منتج جديد
     */
    public function create() 
    {
        try {
            $company_id = current_user()['current_company_id'] ?? 1;
            
            // الحصول على البيانات المساعدة
            $categories = $this->productService->getCategories($company_id);
            $units = $this->productService->getUnits($company_id);
            
            $data = [
                'title' => 'إضافة منتج جديد',
                'categories' => $categories,
                'units' => $units,
                'breadcrumb' => [
                    ['title' => 'المخزون', 'url' => base_url('inventory')],
                    ['title' => 'المنتجات', 'url' => base_url('inventory/products')],
                    ['title' => 'إضافة منتج', 'active' => true]
                ]
            ];

            view('Inventory::products/create', $data);

        } catch (Exception $e) {
            flash('product_error', 'حدث خطأ أثناء تحميل النموذج: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/products'));
        }
    }

    /**
     * حفظ منتج جديد
     */
    public function store() 
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                redirect(base_url('inventory/products/create'));
            }

            $company_id = current_user()['current_company_id'] ?? 1;
            $user_id = current_user_id();

            // التحقق من البيانات
            $productData = $this->validateProductData($_POST);
            $productData['company_id'] = $company_id;
            $productData['created_by'] = $user_id;

            // إنشاء المنتج
            $product_id = $this->productModel->create($productData);

            if ($product_id) {
                flash('product_success', 'تم إضافة المنتج بنجاح', 'success');
                redirect(base_url('inventory/products'));
            } else {
                flash('product_error', 'حدث خطأ أثناء إضافة المنتج', 'danger');
                redirect(base_url('inventory/products/create'));
            }

        } catch (Exception $e) {
            flash('product_error', 'حدث خطأ: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/products/create'));
        }
    }

    /**
     * عرض تفاصيل منتج
     */
    public function show() 
    {
        try {
            $product_id = $this->params['id'] ?? null;
            $company_id = current_user()['current_company_id'] ?? 1;

            if (!$product_id) {
                flash('product_error', 'المنتج غير موجود', 'danger');
                redirect(base_url('inventory/products'));
            }

            $product = $this->productModel->getById($product_id, $company_id);
            
            if (!$product) {
                flash('product_error', 'المنتج غير موجود', 'danger');
                redirect(base_url('inventory/products'));
            }

            // الحصول على أرصدة المنتج
            $stock = $this->productService->getProductStock($product_id, $company_id);
            
            $data = [
                'title' => $product['product_name_ar'],
                'product' => $product,
                'stock' => $stock,
                'breadcrumb' => [
                    ['title' => 'المخزون', 'url' => base_url('inventory')],
                    ['title' => 'المنتجات', 'url' => base_url('inventory/products')],
                    ['title' => $product['product_name_ar'], 'active' => true]
                ]
            ];

            view('Inventory::products/show', $data);

        } catch (Exception $e) {
            flash('product_error', 'حدث خطأ: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/products'));
        }
    }

    /**
     * عرض نموذج تعديل منتج
     */
    public function edit() 
    {
        try {
            $product_id = $this->params['id'] ?? null;
            $company_id = current_user()['current_company_id'] ?? 1;

            if (!$product_id) {
                flash('product_error', 'المنتج غير موجود', 'danger');
                redirect(base_url('inventory/products'));
            }

            $product = $this->productModel->getById($product_id, $company_id);
            
            if (!$product) {
                flash('product_error', 'المنتج غير موجود', 'danger');
                redirect(base_url('inventory/products'));
            }

            // الحصول على البيانات المساعدة
            $categories = $this->productService->getCategories($company_id);
            $units = $this->productService->getUnits($company_id);
            
            $data = [
                'title' => 'تعديل المنتج - ' . $product['product_name_ar'],
                'product' => $product,
                'categories' => $categories,
                'units' => $units,
                'breadcrumb' => [
                    ['title' => 'المخزون', 'url' => base_url('inventory')],
                    ['title' => 'المنتجات', 'url' => base_url('inventory/products')],
                    ['title' => 'تعديل المنتج', 'active' => true]
                ]
            ];

            view('Inventory::products/edit', $data);

        } catch (Exception $e) {
            flash('product_error', 'حدث خطأ: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/products'));
        }
    }

    /**
     * تحديث منتج
     */
    public function update() 
    {
        try {
            $product_id = $this->params['id'] ?? null;
            $company_id = current_user()['current_company_id'] ?? 1;
            $user_id = current_user_id();

            if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !$product_id) {
                redirect(base_url('inventory/products'));
            }

            // التحقق من وجود المنتج
            $product = $this->productModel->getById($product_id, $company_id);
            if (!$product) {
                flash('product_error', 'المنتج غير موجود', 'danger');
                redirect(base_url('inventory/products'));
            }

            // التحقق من البيانات
            $productData = $this->validateProductData($_POST);
            $productData['updated_by'] = $user_id;

            // تحديث المنتج
            if ($this->productModel->update($product_id, $productData, $company_id)) {
                flash('product_success', 'تم تحديث المنتج بنجاح', 'success');
                redirect(base_url('inventory/products/' . $product_id));
            } else {
                flash('product_error', 'حدث خطأ أثناء تحديث المنتج', 'danger');
                redirect(base_url('inventory/products/' . $product_id . '/edit'));
            }

        } catch (Exception $e) {
            flash('product_error', 'حدث خطأ: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/products'));
        }
    }

    /**
     * حذف منتج
     */
    public function delete() 
    {
        try {
            $product_id = $this->params['id'] ?? null;
            $company_id = current_user()['current_company_id'] ?? 1;

            if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !$product_id) {
                redirect(base_url('inventory/products'));
            }

            // التحقق من وجود المنتج
            $product = $this->productModel->getById($product_id, $company_id);
            if (!$product) {
                flash('product_error', 'المنتج غير موجود', 'danger');
                redirect(base_url('inventory/products'));
            }

            // حذف المنتج
            if ($this->productModel->delete($product_id, $company_id)) {
                flash('product_success', 'تم حذف المنتج بنجاح', 'success');
            } else {
                flash('product_error', 'حدث خطأ أثناء حذف المنتج', 'danger');
            }

            redirect(base_url('inventory/products'));

        } catch (Exception $e) {
            flash('product_error', 'حدث خطأ: ' . $e->getMessage(), 'danger');
            redirect(base_url('inventory/products'));
        }
    }

    /**
     * التحقق من صحة بيانات المنتج
     */
    private function validateProductData($data) 
    {
        $errors = [];

        // التحقق من الحقول المطلوبة
        if (empty($data['product_code'])) {
            $errors[] = 'كود المنتج مطلوب';
        }

        if (empty($data['product_name_ar'])) {
            $errors[] = 'اسم المنتج باللغة العربية مطلوب';
        }

        if (empty($data['category_id'])) {
            $errors[] = 'فئة المنتج مطلوبة';
        }

        if (empty($data['unit_id'])) {
            $errors[] = 'وحدة القياس مطلوبة';
        }

        if (!empty($errors)) {
            throw new Exception(implode(', ', $errors));
        }

        // إعداد البيانات
        return [
            'product_code' => trim($data['product_code']),
            'barcode' => trim($data['barcode'] ?? ''),
            'product_name_ar' => trim($data['product_name_ar']),
            'product_name_en' => trim($data['product_name_en'] ?? ''),
            'description_ar' => trim($data['description_ar'] ?? ''),
            'description_en' => trim($data['description_en'] ?? ''),
            'category_id' => (int)$data['category_id'],
            'unit_id' => (int)$data['unit_id'],
            'product_type' => $data['product_type'] ?? 'product',
            'track_inventory' => isset($data['track_inventory']) ? 1 : 0,
            'cost_price' => (float)($data['cost_price'] ?? 0),
            'selling_price' => (float)($data['selling_price'] ?? 0),
            'min_stock_level' => (float)($data['min_stock_level'] ?? 0),
            'max_stock_level' => !empty($data['max_stock_level']) ? (float)$data['max_stock_level'] : null,
            'reorder_point' => (float)($data['reorder_point'] ?? 0),
            'weight' => !empty($data['weight']) ? (float)$data['weight'] : null,
            'dimensions' => trim($data['dimensions'] ?? ''),
            'tax_rate' => (float)($data['tax_rate'] ?? 0),
            'is_active' => isset($data['is_active']) ? 1 : 0
        ];
    }
}
