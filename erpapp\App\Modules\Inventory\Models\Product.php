<?php
namespace App\Modules\Inventory\Models;

use PDO;
use Exception;

/**
 * Product Model - نموذج المنتجات
 */
class Product
{
    /**
     * Database connection
     */
    protected $db;

    /**
     * Table name
     */
    protected $table = 'inventory_products';

    /**
     * Constructor
     */
    public function __construct()
    {
        global $db;
        $this->db = $db;
    }

    /**
     * الحصول على جميع المنتجات للشركة
     */
    public function getByCompany($company_id, $filters = [])
    {
        $sql = "SELECT p.*,
                       c.category_name_ar,
                       u.unit_name_ar, u.unit_symbol_ar,
                       COALESCE(SUM(s.quantity_on_hand), 0) as total_stock
                FROM {$this->table} p
                LEFT JOIN inventory_categories c ON p.category_id = c.category_id
                LEFT JOIN inventory_units u ON p.unit_id = u.unit_id
                LEFT JOIN inventory_stock s ON p.product_id = s.product_id
                WHERE p.company_id = ?";

        $params = [$company_id];

        // تطبيق الفلاتر
        if (!empty($filters['category_id'])) {
            $sql .= " AND p.category_id = ?";
            $params[] = $filters['category_id'];
        }

        if (!empty($filters['search'])) {
            $sql .= " AND (p.product_name_ar LIKE ? OR p.product_name_en LIKE ? OR p.product_code LIKE ?)";
            $search = '%' . $filters['search'] . '%';
            $params[] = $search;
            $params[] = $search;
            $params[] = $search;
        }

        if (isset($filters['is_active'])) {
            $sql .= " AND p.is_active = ?";
            $params[] = $filters['is_active'];
        }

        $sql .= " GROUP BY p.product_id ORDER BY p.product_name_ar";

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على المنتجات مع تفاصيل كاملة للعرض في الجدول
     */
    public function getProductsWithDetails($company_id)
    {
        $sql = "SELECT p.*,
                       c.category_name_ar,
                       c.category_name_en,
                       u.unit_name_ar,
                       u.unit_name_en,
                       u.unit_symbol_ar,
                       u.unit_symbol_en,
                       COALESCE(SUM(s.quantity_on_hand), 0) as total_stock,
                       COALESCE(SUM(s.quantity_available), 0) as available_stock,
                       COALESCE(SUM(s.quantity_reserved), 0) as reserved_stock
                FROM {$this->table} p
                LEFT JOIN inventory_categories c ON p.category_id = c.category_id
                LEFT JOIN inventory_units u ON p.unit_id = u.unit_id
                LEFT JOIN inventory_stock s ON p.product_id = s.product_id
                WHERE p.company_id = ?
                GROUP BY p.product_id
                ORDER BY p.product_name_ar";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على منتج بالمعرف
     */
    public function getById($product_id, $company_id)
    {
        $sql = "SELECT p.*,
                       c.category_name_ar, c.category_name_en,
                       u.unit_name_ar, u.unit_name_en, u.unit_symbol_ar, u.unit_symbol_en
                FROM {$this->table} p
                LEFT JOIN inventory_categories c ON p.category_id = c.category_id
                LEFT JOIN inventory_units u ON p.unit_id = u.unit_id
                WHERE p.product_id = ? AND p.company_id = ?";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$product_id, $company_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على منتج بالكود
     */
    public function getByCode($product_code, $company_id)
    {
        $sql = "SELECT * FROM {$this->table} WHERE product_code = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$product_code, $company_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * إنشاء منتج جديد
     */
    public function create($data)
    {
        // التحقق من عدم تكرار الكود
        if ($this->getByCode($data['product_code'], $data['company_id'])) {
            throw new Exception('كود المنتج موجود مسبقاً');
        }

        $sql = "INSERT INTO {$this->table} (
                    company_id, module_code, product_code, barcode,
                    product_name_ar, product_name_en, description_ar, description_en,
                    category_id, unit_id, product_type, track_inventory,
                    cost_price, selling_price, min_stock_level, max_stock_level, reorder_point,
                    weight, dimensions, tax_rate, is_active, created_by, created_at
                ) VALUES (
                    ?, 'inventory', ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW()
                )";

        $stmt = $this->db->prepare($sql);
        $result = $stmt->execute([
            $data['company_id'],
            $data['product_code'],
            $data['barcode'] ?: null,
            $data['product_name_ar'],
            $data['product_name_en'] ?: null,
            $data['description_ar'] ?: null,
            $data['description_en'] ?: null,
            $data['category_id'],
            $data['unit_id'],
            $data['product_type'],
            $data['track_inventory'],
            $data['cost_price'],
            $data['selling_price'],
            $data['min_stock_level'],
            $data['max_stock_level'],
            $data['reorder_point'],
            $data['weight'],
            $data['dimensions'] ?: null,
            $data['tax_rate'],
            $data['is_active'],
            $data['created_by']
        ]);

        return $result ? $this->db->lastInsertId() : false;
    }

    /**
     * تحديث منتج
     */
    public function update($product_id, $data, $company_id)
    {
        // التحقق من عدم تكرار الكود (إذا تم تغييره)
        if (isset($data['product_code'])) {
            $existing = $this->getByCode($data['product_code'], $company_id);
            if ($existing && $existing['product_id'] != $product_id) {
                throw new Exception('كود المنتج موجود مسبقاً');
            }
        }

        $sql = "UPDATE {$this->table} SET
                    product_code = ?, barcode = ?,
                    product_name_ar = ?, product_name_en = ?,
                    description_ar = ?, description_en = ?,
                    category_id = ?, unit_id = ?, product_type = ?, track_inventory = ?,
                    cost_price = ?, selling_price = ?,
                    min_stock_level = ?, max_stock_level = ?, reorder_point = ?,
                    weight = ?, dimensions = ?, tax_rate = ?, is_active = ?,
                    updated_by = ?, updated_at = NOW()
                WHERE product_id = ? AND company_id = ?";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            $data['product_code'],
            $data['barcode'] ?: null,
            $data['product_name_ar'],
            $data['product_name_en'] ?: null,
            $data['description_ar'] ?: null,
            $data['description_en'] ?: null,
            $data['category_id'],
            $data['unit_id'],
            $data['product_type'],
            $data['track_inventory'],
            $data['cost_price'],
            $data['selling_price'],
            $data['min_stock_level'],
            $data['max_stock_level'],
            $data['reorder_point'],
            $data['weight'],
            $data['dimensions'] ?: null,
            $data['tax_rate'],
            $data['is_active'],
            $data['updated_by'],
            $product_id,
            $company_id
        ]);
    }

    /**
     * حذف منتج
     */
    public function delete($product_id, $company_id)
    {
        // التحقق من عدم وجود حركات للمنتج
        $sql = "SELECT COUNT(*) FROM inventory_movements WHERE product_id = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$product_id, $company_id]);

        if ($stmt->fetchColumn() > 0) {
            throw new Exception('لا يمكن حذف المنتج لوجود حركات مخزون مرتبطة به');
        }

        // حذف أرصدة المخزون أولاً
        $sql = "DELETE FROM inventory_stock WHERE product_id = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$product_id, $company_id]);

        // حذف المنتج
        $sql = "DELETE FROM {$this->table} WHERE product_id = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$product_id, $company_id]);
    }

    /**
     * الحصول على المنتجات منخفضة المخزون
     */
    public function getLowStockProducts($company_id)
    {
        $sql = "SELECT p.*,
                       c.category_name_ar,
                       u.unit_name_ar, u.unit_symbol_ar,
                       COALESCE(SUM(s.quantity_on_hand), 0) as total_stock
                FROM {$this->table} p
                LEFT JOIN inventory_categories c ON p.category_id = c.category_id
                LEFT JOIN inventory_units u ON p.unit_id = u.unit_id
                LEFT JOIN inventory_stock s ON p.product_id = s.product_id
                WHERE p.company_id = ? AND p.is_active = 1 AND p.track_inventory = 1
                GROUP BY p.product_id
                HAVING total_stock <= p.min_stock_level
                ORDER BY total_stock ASC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على المنتجات النافدة
     */
    public function getOutOfStockProducts($company_id)
    {
        $sql = "SELECT p.*,
                       c.category_name_ar,
                       u.unit_name_ar, u.unit_symbol_ar,
                       COALESCE(SUM(s.quantity_on_hand), 0) as total_stock
                FROM {$this->table} p
                LEFT JOIN inventory_categories c ON p.category_id = c.category_id
                LEFT JOIN inventory_units u ON p.unit_id = u.unit_id
                LEFT JOIN inventory_stock s ON p.product_id = s.product_id
                WHERE p.company_id = ? AND p.is_active = 1 AND p.track_inventory = 1
                GROUP BY p.product_id
                HAVING total_stock = 0
                ORDER BY p.product_name_ar";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على إحصائيات المنتجات
     */
    public function getStats($company_id)
    {
        $stats = [];

        // إجمالي المنتجات
        $sql = "SELECT COUNT(*) FROM {$this->table} WHERE company_id = ? AND is_active = 1";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        $stats['total_products'] = $stmt->fetchColumn();

        // المنتجات النشطة
        $sql = "SELECT COUNT(*) FROM {$this->table} WHERE company_id = ? AND is_active = 1";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        $stats['active_products'] = $stmt->fetchColumn();

        // المنتجات منخفضة المخزون
        $stats['low_stock_count'] = count($this->getLowStockProducts($company_id));

        // المنتجات النافدة
        $stats['out_of_stock_count'] = count($this->getOutOfStockProducts($company_id));

        return $stats;
    }
}
