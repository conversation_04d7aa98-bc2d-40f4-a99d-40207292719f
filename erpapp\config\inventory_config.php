<?php
/**
 * Inventory Module Configuration
 * إعدادات وحدة المخزون
 */

// Base URL Configuration
define('INVENTORY_BASE_URL', 'https://qqo.shq.mybluehost.me/');
define('INVENTORY_MODULE_PATH', 'inventory/');

// Full URLs for Inventory Module
define('INVENTORY_URL', INVENTORY_BASE_URL . INVENTORY_MODULE_PATH);

/**
 * URL Helper Functions
 */
function inventory_url($path = '') {
    return INVENTORY_URL . ltrim($path, '/');
}

function base_url($path = '') {
    return INVENTORY_BASE_URL . ltrim($path, '/');
}

/**
 * Inventory Module URLs
 */
$inventory_urls = [
    // Main URLs
    'dashboard' => inventory_url(''),
    'dashboard_full' => inventory_url('dashboard'),
    
    // Products URLs
    'products' => inventory_url('products'),
    'products_create' => inventory_url('products/create'),
    'products_show' => inventory_url('products/{id}'),
    'products_edit' => inventory_url('products/{id}/edit'),
    'products_update' => inventory_url('products/{id}/update'),
    'products_delete' => inventory_url('products/{id}/delete'),
    'products_reorder' => inventory_url('products/reorder'),
    
    // Categories URLs
    'categories' => inventory_url('categories'),
    'categories_create' => inventory_url('categories/create'),
    'categories_show' => inventory_url('categories/{id}'),
    'categories_edit' => inventory_url('categories/{id}/edit'),
    'categories_update' => inventory_url('categories/{id}/update'),
    'categories_delete' => inventory_url('categories/{id}/delete'),
    'categories_hierarchy' => inventory_url('categories/hierarchy'),
    'categories_subcategories' => inventory_url('categories/{id}/subcategories'),
    
    // Units URLs
    'units' => inventory_url('units'),
    'units_create' => inventory_url('units/create'),
    'units_show' => inventory_url('units/{id}'),
    'units_edit' => inventory_url('units/{id}/edit'),
    'units_update' => inventory_url('units/{id}/update'),
    'units_delete' => inventory_url('units/{id}/delete'),
    'units_convert' => inventory_url('units/convert'),
    
    // Warehouses URLs
    'warehouses' => inventory_url('warehouses'),
    'warehouses_create' => inventory_url('warehouses/create'),
    'warehouses_show' => inventory_url('warehouses/{id}'),
    'warehouses_edit' => inventory_url('warehouses/{id}/edit'),
    'warehouses_update' => inventory_url('warehouses/{id}/update'),
    'warehouses_delete' => inventory_url('warehouses/{id}/delete'),
    'warehouses_update_usage' => inventory_url('warehouses/{id}/update-usage'),
    'warehouses_by_type' => inventory_url('warehouses/type/{type}'),
    
    // Stock URLs
    'stock' => inventory_url('stock'),
    'stock_show' => inventory_url('stock/{product_id}/{warehouse_id}'),
    'stock_adjust' => inventory_url('stock/{product_id}/{warehouse_id}/adjust'),
    'stock_process_adjustment' => inventory_url('stock/{product_id}/{warehouse_id}/process-adjustment'),
    'stock_reserve' => inventory_url('stock/{product_id}/{warehouse_id}/reserve'),
    'stock_unreserve' => inventory_url('stock/{product_id}/{warehouse_id}/unreserve'),
    'stock_total' => inventory_url('stock/product/{product_id}/total'),
    'stock_out_of_stock' => inventory_url('stock/out-of-stock'),
    'stock_need_reorder' => inventory_url('stock/need-reorder'),
    
    // Reports URLs
    'reports' => inventory_url('reports'),
    'reports_stock_summary' => inventory_url('reports/stock_summary'),
    'reports_stock_valuation' => inventory_url('reports/stock_valuation'),
    'reports_movement' => inventory_url('reports/movement_report'),
    'reports_reorder' => inventory_url('reports/reorder_report'),
    'reports_out_of_stock' => inventory_url('reports/out_of_stock_report'),
    'reports_warehouse' => inventory_url('reports/warehouse_report'),
    'reports_category' => inventory_url('reports/category_report'),
    
    // Export URLs
    'export_products' => inventory_url('export/products'),
    'export_stock' => inventory_url('export/stock'),
    'export_categories' => inventory_url('export/categories'),
    'export_warehouses' => inventory_url('export/warehouses'),
    
    // Search URLs
    'search' => inventory_url('search'),
    'settings' => inventory_url('settings'),
];

/**
 * Navigation Menu Configuration
 */
$inventory_navigation = [
    'main' => [
        'title' => 'إدارة المخزون',
        'url' => inventory_url(''),
        'icon' => 'fas fa-boxes',
        'active_patterns' => ['/inventory'],
    ],
    'products' => [
        'title' => 'المنتجات',
        'url' => inventory_url('products'),
        'icon' => 'fas fa-cube',
        'active_patterns' => ['/inventory/products'],
        'submenu' => [
            [
                'title' => 'جميع المنتجات',
                'url' => inventory_url('products'),
                'icon' => 'fas fa-list'
            ],
            [
                'title' => 'إضافة منتج',
                'url' => inventory_url('products/create'),
                'icon' => 'fas fa-plus'
            ],
            [
                'title' => 'المنتجات النافدة',
                'url' => inventory_url('stock/out-of-stock'),
                'icon' => 'fas fa-exclamation-circle'
            ],
            [
                'title' => 'يحتاج إعادة طلب',
                'url' => inventory_url('products/reorder'),
                'icon' => 'fas fa-exclamation-triangle'
            ]
        ]
    ],
    'categories' => [
        'title' => 'الفئات',
        'url' => inventory_url('categories'),
        'icon' => 'fas fa-tags',
        'active_patterns' => ['/inventory/categories'],
        'submenu' => [
            [
                'title' => 'جميع الفئات',
                'url' => inventory_url('categories'),
                'icon' => 'fas fa-list'
            ],
            [
                'title' => 'إضافة فئة',
                'url' => inventory_url('categories/create'),
                'icon' => 'fas fa-plus'
            ],
            [
                'title' => 'الهيكل الهرمي',
                'url' => inventory_url('categories/hierarchy'),
                'icon' => 'fas fa-sitemap'
            ]
        ]
    ],
    'warehouses' => [
        'title' => 'المخازن',
        'url' => inventory_url('warehouses'),
        'icon' => 'fas fa-warehouse',
        'active_patterns' => ['/inventory/warehouses'],
        'submenu' => [
            [
                'title' => 'جميع المخازن',
                'url' => inventory_url('warehouses'),
                'icon' => 'fas fa-list'
            ],
            [
                'title' => 'إضافة مخزن',
                'url' => inventory_url('warehouses/create'),
                'icon' => 'fas fa-plus'
            ]
        ]
    ],
    'stock' => [
        'title' => 'أرصدة المخزون',
        'url' => inventory_url('stock'),
        'icon' => 'fas fa-chart-bar',
        'active_patterns' => ['/inventory/stock'],
        'submenu' => [
            [
                'title' => 'جميع الأرصدة',
                'url' => inventory_url('stock'),
                'icon' => 'fas fa-list'
            ],
            [
                'title' => 'المنتجات النافدة',
                'url' => inventory_url('stock/out-of-stock'),
                'icon' => 'fas fa-exclamation-circle'
            ],
            [
                'title' => 'يحتاج إعادة طلب',
                'url' => inventory_url('stock/need-reorder'),
                'icon' => 'fas fa-exclamation-triangle'
            ]
        ]
    ],
    'reports' => [
        'title' => 'التقارير',
        'url' => inventory_url('reports'),
        'icon' => 'fas fa-chart-line',
        'active_patterns' => ['/inventory/reports'],
        'submenu' => [
            [
                'title' => 'جميع التقارير',
                'url' => inventory_url('reports'),
                'icon' => 'fas fa-list'
            ],
            [
                'title' => 'تقرير المخزون',
                'url' => inventory_url('reports/stock_summary'),
                'icon' => 'fas fa-chart-bar'
            ],
            [
                'title' => 'تقييم المخزون',
                'url' => inventory_url('reports/stock_valuation'),
                'icon' => 'fas fa-dollar-sign'
            ]
        ]
    ]
];

/**
 * Breadcrumb Helper Function
 */
function get_inventory_breadcrumb($current_page, $params = []) {
    $breadcrumbs = [
        [
            'title' => 'الرئيسية',
            'url' => base_url('dashboard'),
            'icon' => 'fas fa-home'
        ],
        [
            'title' => 'المخزون',
            'url' => inventory_url(''),
            'icon' => 'fas fa-boxes'
        ]
    ];
    
    switch ($current_page) {
        case 'products':
            $breadcrumbs[] = ['title' => 'المنتجات', 'icon' => 'fas fa-cube'];
            break;
        case 'products_create':
            $breadcrumbs[] = ['title' => 'المنتجات', 'url' => inventory_url('products'), 'icon' => 'fas fa-cube'];
            $breadcrumbs[] = ['title' => 'إضافة منتج جديد', 'icon' => 'fas fa-plus'];
            break;
        case 'products_show':
            $breadcrumbs[] = ['title' => 'المنتجات', 'url' => inventory_url('products'), 'icon' => 'fas fa-cube'];
            $breadcrumbs[] = ['title' => 'تفاصيل المنتج', 'icon' => 'fas fa-eye'];
            break;
        case 'products_edit':
            $breadcrumbs[] = ['title' => 'المنتجات', 'url' => inventory_url('products'), 'icon' => 'fas fa-cube'];
            $breadcrumbs[] = ['title' => 'تعديل المنتج', 'icon' => 'fas fa-edit'];
            break;
        case 'categories':
            $breadcrumbs[] = ['title' => 'الفئات', 'icon' => 'fas fa-tags'];
            break;
        case 'categories_create':
            $breadcrumbs[] = ['title' => 'الفئات', 'url' => inventory_url('categories'), 'icon' => 'fas fa-tags'];
            $breadcrumbs[] = ['title' => 'إضافة فئة جديدة', 'icon' => 'fas fa-plus'];
            break;
        case 'warehouses':
            $breadcrumbs[] = ['title' => 'المخازن', 'icon' => 'fas fa-warehouse'];
            break;
        case 'stock':
            $breadcrumbs[] = ['title' => 'أرصدة المخزون', 'icon' => 'fas fa-chart-bar'];
            break;
        case 'reports':
            $breadcrumbs[] = ['title' => 'التقارير', 'icon' => 'fas fa-chart-line'];
            break;
    }
    
    return $breadcrumbs;
}

/**
 * Asset URLs
 */
$inventory_assets = [
    'css' => [
        'bootstrap' => 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css',
        'fontawesome' => 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
        'datatables' => 'https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css',
        'toastr' => 'https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css',
    ],
    'js' => [
        'jquery' => 'https://code.jquery.com/jquery-3.7.1.min.js',
        'bootstrap' => 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js',
        'datatables' => 'https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js',
        'datatables_bootstrap' => 'https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js',
        'toastr' => 'https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js',
        'chartjs' => 'https://cdn.jsdelivr.net/npm/chart.js',
    ]
];

/**
 * API Endpoints
 */
$inventory_api = [
    'search' => inventory_url('api/search'),
    'products' => inventory_url('api/products'),
    'categories' => inventory_url('api/categories'),
    'warehouses' => inventory_url('api/warehouses'),
    'stock' => inventory_url('api/stock'),
    'reports' => inventory_url('api/reports'),
];

/**
 * File Upload Configuration
 */
$inventory_uploads = [
    'base_path' => INVENTORY_BASE_URL . 'uploads/inventory/',
    'products_images' => INVENTORY_BASE_URL . 'uploads/inventory/products/',
    'categories_images' => INVENTORY_BASE_URL . 'uploads/inventory/categories/',
    'reports' => INVENTORY_BASE_URL . 'uploads/inventory/reports/',
    'exports' => INVENTORY_BASE_URL . 'uploads/inventory/exports/',
];

// Make variables globally available
$GLOBALS['inventory_urls'] = $inventory_urls;
$GLOBALS['inventory_navigation'] = $inventory_navigation;
$GLOBALS['inventory_assets'] = $inventory_assets;
$GLOBALS['inventory_api'] = $inventory_api;
$GLOBALS['inventory_uploads'] = $inventory_uploads;
