<?php
namespace Modules\Accounting;

use App\Core\Module as BaseModule;

/**
 * وحدة المحاسبة الرئيسية
 */
class Module extends BaseModule
{
    /**
     * اسم الوحدة
     */
    protected $name = 'Accounting';

    /**
     * وصف الوحدة
     */
    protected $description = 'نظام المحاسبة المتكامل';

    /**
     * إصدار الوحدة
     */
    protected $version = '1.0.0';

    /**
     * الوحدات الفرعية
     */
    protected $subModules = [
        'Sales' => 'Modules\Accounting\Sales\Module',
        'Purchases' => 'Modules\Accounting\Purchases\Module',
        'Inventory' => 'Modules\Accounting\Inventory\Module'
    ];

    /**
     * الوحدات المطلوبة
     */
    protected $dependencies = ['System.Companies', 'System.Users'];

    /**
     * تسجيل المسارات الخاصة بالوحدة
     */
    public function registerRoutes()
    {
        // مسارات المحاسبة الرئيسية
        add_route('GET', '/accounting', 'Modules\Accounting\Controllers\AccountingController@index');
        add_route('GET', '/accounting/dashboard', 'Modules\Accounting\Controllers\AccountingController@dashboard');
        add_route('GET', '/accounting/reports', 'Modules\Accounting\Controllers\ReportController@index');
        add_route('GET', '/accounting/settings', 'Modules\Accounting\Controllers\SettingsController@index');

        // مسار اختبار للمخزون (مؤقت)
        add_route('GET', '/inventory', 'Modules\Accounting\Inventory\Controllers\InventoryController@index');
    }

    /**
     * تهيئة الوحدة
     */
    public function boot()
    {
        // تسجيل المسارات
        $this->registerRoutes();

        // تحميل الوحدات الفرعية
        $this->loadSubModules();

        // تسجيل الخدمات
        $this->registerServices();
    }

    /**
     * تحميل الوحدات الفرعية
     */
    protected function loadSubModules()
    {
        foreach ($this->subModules as $name => $class) {
            try {
                // محاولة تحميل الفئة يدوياً إذا لم تكن موجودة
                if (!class_exists($class)) {
                    $file_path = str_replace('\\', '/', $class) . '.php';
                    $full_path = BASE_PATH . '/' . $file_path;

                    if (file_exists($full_path)) {
                        require_once $full_path;
                    }
                }

                if (class_exists($class)) {
                    $subModule = new $class();
                    $subModule->boot();
                } else {
                    error_log("Sub-module class not found: $class");
                }
            } catch (Exception $e) {
                error_log("Error loading sub-module $name: " . $e->getMessage());
            }
        }
    }

    /**
     * تسجيل الخدمات
     */
    protected function registerServices()
    {
        // يمكن تسجيل خدمات مشتركة للمحاسبة هنا
    }

    /**
     * التحقق من صلاحيات الوحدة
     */
    public function hasPermission($user_id, $company_id)
    {
        // التحقق من أن الشركة لديها اشتراك يسمح بوحدة المحاسبة
        return true;
    }

    /**
     * الحصول على معلومات الوحدة
     */
    public function getInfo()
    {
        return [
            'name' => $this->name,
            'description' => $this->description,
            'version' => $this->version,
            'dependencies' => $this->dependencies,
            'category' => 'Business',
            'sub_modules' => array_keys($this->subModules)
        ];
    }

    /**
     * الحصول على الوحدات الفرعية المتاحة
     */
    public function getAvailableSubModules($company_id)
    {
        // يمكن تطوير هذا لاحقاً للتحقق من الاشتراك
        // وإرجاع الوحدات الفرعية المتاحة للشركة
        return $this->subModules;
    }

    /**
     * تفعيل وحدة فرعية
     */
    public function enableSubModule($subModuleName, $company_id)
    {
        // يمكن تطوير هذا لاحقاً لتفعيل وحدات فرعية محددة
        // حسب اشتراك الشركة
        return true;
    }

    /**
     * إلغاء تفعيل وحدة فرعية
     */
    public function disableSubModule($subModuleName, $company_id)
    {
        // يمكن تطوير هذا لاحقاً لإلغاء تفعيل وحدات فرعية
        return true;
    }
}
