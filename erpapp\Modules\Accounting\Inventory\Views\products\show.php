<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title">
                    <i class="fas fa-cube text-primary"></i> <?= $title ?? 'تفاصيل المنتج' ?>
                </h1>
                <div class="btn-group">
                    <a href="<?= base_url('inventory/products/' . $product['product_id'] . '/edit') ?>" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i> تعديل
                    </a>
                    <a href="<?= base_url('inventory/products') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i> العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (isset($_SESSION['flash_message'])): ?>
        <div class="alert alert-<?= $_SESSION['flash_message']['type'] ?> alert-dismissible fade show" role="alert">
            <?= $_SESSION['flash_message']['message'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['flash_message']); ?>
    <?php endif; ?>

    <div class="row">
        <!-- Product Information -->
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i> المعلومات الأساسية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">كود المنتج:</td>
                                    <td><?= htmlspecialchars($product['product_code']) ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">الباركود:</td>
                                    <td><?= htmlspecialchars($product['barcode'] ?? 'غير محدد') ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">اسم المنتج (عربي):</td>
                                    <td><?= htmlspecialchars($product['product_name_ar']) ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">اسم المنتج (إنجليزي):</td>
                                    <td><?= htmlspecialchars($product['product_name_en'] ?? 'غير محدد') ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">الفئة:</td>
                                    <td><?= htmlspecialchars($product['category_name_ar'] ?? 'غير محدد') ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">وحدة القياس:</td>
                                    <td><?= htmlspecialchars($product['unit_name_ar'] ?? 'غير محدد') ?> (<?= htmlspecialchars($product['unit_symbol_ar'] ?? '') ?>)</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">نوع المنتج:</td>
                                    <td>
                                        <?php
                                        $type_labels = [
                                            'product' => 'منتج',
                                            'service' => 'خدمة',
                                            'digital' => 'رقمي'
                                        ];
                                        echo $type_labels[$product['product_type']] ?? $product['product_type'];
                                        ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">تتبع المخزون:</td>
                                    <td>
                                        <?php if ($product['track_inventory']): ?>
                                            <span class="badge bg-success">نعم</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">لا</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">الحالة:</td>
                                    <td>
                                        <?php if ($product['is_active']): ?>
                                            <span class="badge bg-success">نشط</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">غير نشط</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">تاريخ الإنشاء:</td>
                                    <td><?= date('Y-m-d H:i', strtotime($product['created_at'])) ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <?php if (!empty($product['description_ar']) || !empty($product['description_en'])): ?>
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6 class="fw-bold">الوصف:</h6>
                            <?php if (!empty($product['description_ar'])): ?>
                                <p><strong>عربي:</strong> <?= nl2br(htmlspecialchars($product['description_ar'])) ?></p>
                            <?php endif; ?>
                            <?php if (!empty($product['description_en'])): ?>
                                <p><strong>إنجليزي:</strong> <?= nl2br(htmlspecialchars($product['description_en'])) ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Pricing Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-dollar-sign me-2"></i> معلومات التسعير
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center">
                                <h5 class="text-info">سعر التكلفة</h5>
                                <h3 class="text-primary"><?= number_format($product['cost_price'], 2) ?> ر.س</h3>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h5 class="text-success">سعر البيع</h5>
                                <h3 class="text-success"><?= number_format($product['selling_price'], 2) ?> ر.س</h3>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h5 class="text-warning">هامش الربح</h5>
                                <?php 
                                $profit_margin = $product['selling_price'] - $product['cost_price'];
                                $profit_percentage = $product['cost_price'] > 0 ? ($profit_margin / $product['cost_price']) * 100 : 0;
                                ?>
                                <h3 class="text-warning"><?= number_format($profit_margin, 2) ?> ر.س</h3>
                                <small class="text-muted">(<?= number_format($profit_percentage, 1) ?>%)</small>
                            </div>
                        </div>
                    </div>
                    
                    <?php if ($product['tax_rate'] > 0): ?>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                معدل الضريبة: <strong><?= number_format($product['tax_rate'], 2) ?>%</strong>
                                - السعر شامل الضريبة: <strong><?= number_format($product['selling_price'] * (1 + $product['tax_rate'] / 100), 2) ?> ر.س</strong>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Inventory Information -->
            <?php if ($product['track_inventory']): ?>
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-boxes me-2"></i> معلومات المخزون
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6 class="text-muted">الحد الأدنى</h6>
                                <h4 class="text-warning"><?= number_format($product['min_stock_level'], 2) ?></h4>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6 class="text-muted">نقطة إعادة الطلب</h6>
                                <h4 class="text-info"><?= number_format($product['reorder_point'], 2) ?></h4>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6 class="text-muted">الحد الأقصى</h6>
                                <h4 class="text-success"><?= $product['max_stock_level'] ? number_format($product['max_stock_level'], 2) : 'غير محدد' ?></h4>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6 class="text-muted">المخزون الحالي</h6>
                                <h4 class="text-primary">
                                    <a href="<?= base_url('inventory/stock/product/' . $product['product_id'] . '/total') ?>" class="text-decoration-none">
                                        <?= number_format($product['total_stock'] ?? 0, 2) ?>
                                    </a>
                                </h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Product Image -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-image me-2"></i> صورة المنتج
                    </h6>
                </div>
                <div class="card-body text-center">
                    <?php if (!empty($product['image_url'])): ?>
                        <img src="<?= htmlspecialchars($product['image_url']) ?>" 
                             alt="<?= htmlspecialchars($product['product_name_ar']) ?>" 
                             class="img-fluid rounded" style="max-height: 200px;">
                    <?php else: ?>
                        <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 200px;">
                            <i class="fas fa-image fa-3x text-muted"></i>
                        </div>
                        <p class="text-muted mt-2">لا توجد صورة</p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-cog me-2"></i> معلومات إضافية
                    </h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless table-sm">
                        <?php if (!empty($product['weight'])): ?>
                        <tr>
                            <td class="fw-bold">الوزن:</td>
                            <td><?= number_format($product['weight'], 3) ?> كجم</td>
                        </tr>
                        <?php endif; ?>
                        
                        <?php if (!empty($product['dimensions'])): ?>
                        <tr>
                            <td class="fw-bold">الأبعاد:</td>
                            <td><?= htmlspecialchars($product['dimensions']) ?></td>
                        </tr>
                        <?php endif; ?>
                        
                        <tr>
                            <td class="fw-bold">أنشأ بواسطة:</td>
                            <td><?= htmlspecialchars($product['created_by_name'] ?? 'غير محدد') ?></td>
                        </tr>
                        
                        <?php if (!empty($product['updated_by_name'])): ?>
                        <tr>
                            <td class="fw-bold">آخر تحديث:</td>
                            <td><?= htmlspecialchars($product['updated_by_name']) ?></td>
                        </tr>
                        <tr>
                            <td class="fw-bold">تاريخ التحديث:</td>
                            <td><?= date('Y-m-d H:i', strtotime($product['updated_at'])) ?></td>
                        </tr>
                        <?php endif; ?>
                    </table>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt me-2"></i> إجراءات سريعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?= base_url('inventory/products/' . $product['product_id'] . '/edit') ?>" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i> تعديل المنتج
                        </a>
                        
                        <?php if ($product['track_inventory']): ?>
                        <a href="<?= base_url('inventory/stock/product/' . $product['product_id'] . '/total') ?>" class="btn btn-outline-info">
                            <i class="fas fa-chart-bar me-2"></i> عرض المخزون
                        </a>
                        <?php endif; ?>
                        
                        <button type="button" class="btn btn-outline-danger" onclick="deleteProduct(<?= $product['product_id'] ?>)">
                            <i class="fas fa-trash me-2"></i> حذف المنتج
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i> تأكيد الحذف
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من رغبتك في حذف هذا المنتج؟</p>
                <p class="text-danger"><strong>تحذير:</strong> لا يمكن التراجع عن هذا الإجراء.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i> حذف
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function deleteProduct(productId) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `<?= base_url('inventory/products/') ?>${productId}/delete`;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
