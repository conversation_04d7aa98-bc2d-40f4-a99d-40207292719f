<?php
namespace App\Controllers;

/**
 * Redirect Controller
 * للتعامل مع إعادة التوجيه من المسارات القديمة إلى الجديدة
 */
class RedirectController
{
    /**
     * إعادة توجيه إلى مسارات erpapp
     */
    public function redirectToErpApp()
    {
        // الحصول على المسار الحالي
        $currentPath = $_SERVER['REQUEST_URI'] ?? '';
        
        // إزالة المعاملات من المسار
        $path = parse_url($currentPath, PHP_URL_PATH);
        
        // إنشاء المسار الجديد
        $newPath = '/erpapp' . $path;
        
        // إعادة التوجيه
        header("Location: $newPath", true, 301);
        exit;
    }
}
