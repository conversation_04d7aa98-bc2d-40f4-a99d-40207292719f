<?php
/**
 * إدراج بيانات تجريبية للشركة رقم 1
 */

require_once 'loader.php';

echo "<h1>إدراج بيانات تجريبية للشركة رقم 1</h1>";

try {
    global $db;

    // الحصول على الشركة الحالية للمستخدم المسجل
    if (!is_logged_in()) {
        echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
        echo "<h3>❌ يجب تسجيل الدخول أولاً</h3>";
        echo "<p><a href='/login'>تسجيل الدخول</a></p>";
        echo "</div>";
        exit;
    }

    $user = current_user();
    $company_id = $user['current_company_id'] ?? null;
    $user_id = $user['UserID'];

    if (!$company_id) {
        echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;'>";
        echo "<h3>⚠️ لا توجد شركة حالية محددة</h3>";
        echo "<p>يجب تحديد شركة حالية أولاً. <a href='/companies'>إدارة الشركات</a></p>";
        echo "</div>";
        exit;
    }

    // جلب معلومات الشركة
    $stmt = $db->prepare("SELECT CompanyName FROM companies WHERE CompanyID = ?");
    $stmt->execute([$company_id]);
    $company = $stmt->fetch(PDO::FETCH_ASSOC);

    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin-bottom: 20px;'>";
    echo "<h3>📊 إدراج بيانات تجريبية</h3>";
    echo "<p><strong>الشركة:</strong> " . ($company['CompanyName'] ?? 'غير محدد') . " (رقم: $company_id)</p>";
    echo "<p><strong>المستخدم:</strong> " . $user['FirstName'] . " " . $user['LastName'] . " (رقم: $user_id)</p>";
    echo "</div>";

    echo "<h2>1. إدراج وحدات القياس:</h2>";

    $units = [
        ['PIECE', 'قطعة', 'Piece', 'قطعة', 'pcs', 1],
        ['KG', 'كيلوجرام', 'Kilogram', 'كجم', 'kg', 1],
        ['LITER', 'لتر', 'Liter', 'لتر', 'L', 1],
        ['METER', 'متر', 'Meter', 'م', 'm', 1],
        ['BOX', 'صندوق', 'Box', 'صندوق', 'box', 0],
        ['CARTON', 'كرتون', 'Carton', 'كرتون', 'carton', 0]
    ];

    $sql = "INSERT IGNORE INTO inventory_units (company_id, unit_code, unit_name_ar, unit_name_en, unit_symbol_ar, unit_symbol_en, is_base_unit, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = $db->prepare($sql);

    foreach ($units as $unit) {
        $stmt->execute([$company_id, $unit[0], $unit[1], $unit[2], $unit[3], $unit[4], $unit[5], $user_id]);
        echo "<p>✅ تم إدراج وحدة القياس: {$unit[1]}</p>";
    }

    echo "<h2>2. إدراج الفئات:</h2>";

    $categories = [
        ['ELECTRONICS', 'إلكترونيات', 'Electronics', 1],
        ['FURNITURE', 'أثاث', 'Furniture', 2],
        ['OFFICE_SUPPLIES', 'مستلزمات مكتبية', 'Office Supplies', 3],
        ['FOOD_BEVERAGE', 'أطعمة ومشروبات', 'Food & Beverage', 4],
        ['CLOTHING', 'ملابس', 'Clothing', 5],
        ['BOOKS', 'كتب', 'Books', 6]
    ];

    $sql = "INSERT IGNORE INTO inventory_categories (company_id, category_code, category_name_ar, category_name_en, display_order, created_by)
            VALUES (?, ?, ?, ?, ?, ?)";
    $stmt = $db->prepare($sql);

    foreach ($categories as $category) {
        $stmt->execute([$company_id, $category[0], $category[1], $category[2], $category[3], $user_id]);
        echo "<p>✅ تم إدراج الفئة: {$category[1]}</p>";
    }

    echo "<h2>3. إدراج المخازن:</h2>";

    $warehouses = [
        ['MAIN_WH', 'المخزن الرئيسي', 'Main Warehouse', 'الرياض، المملكة العربية السعودية', 'main'],
        ['BRANCH_WH', 'مخزن الفرع', 'Branch Warehouse', 'جدة، المملكة العربية السعودية', 'branch'],
        ['ONLINE_WH', 'مخزن التجارة الإلكترونية', 'Online Warehouse', 'الدمام، المملكة العربية السعودية', 'main']
    ];

    $sql = "INSERT IGNORE INTO inventory_warehouses (company_id, warehouse_code, warehouse_name_ar, warehouse_name_en, address, warehouse_type, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?)";
    $stmt = $db->prepare($sql);

    foreach ($warehouses as $warehouse) {
        $stmt->execute([$company_id, $warehouse[0], $warehouse[1], $warehouse[2], $warehouse[3], $warehouse[4], $user_id]);
        echo "<p>✅ تم إدراج المخزن: {$warehouse[1]}</p>";
    }

    echo "<h2>4. إدراج المنتجات:</h2>";

    // الحصول على معرفات الفئات ووحدات القياس
    $sql = "SELECT category_id FROM inventory_categories WHERE company_id = ? AND category_code = ?";
    $stmt = $db->prepare($sql);

    $stmt->execute([$company_id, 'ELECTRONICS']);
    $electronics_cat = $stmt->fetchColumn();

    $stmt->execute([$company_id, 'FURNITURE']);
    $furniture_cat = $stmt->fetchColumn();

    $stmt->execute([$company_id, 'OFFICE_SUPPLIES']);
    $office_cat = $stmt->fetchColumn();

    $sql = "SELECT unit_id FROM inventory_units WHERE company_id = ? AND unit_code = ?";
    $stmt = $db->prepare($sql);

    $stmt->execute([$company_id, 'PIECE']);
    $piece_unit = $stmt->fetchColumn();

    $stmt->execute([$company_id, 'BOX']);
    $box_unit = $stmt->fetchColumn();

    $products = [
        ['LAPTOP001', 'لابتوب ديل XPS 13', 'Dell XPS 13 Laptop', $electronics_cat, $piece_unit, 3500.00, 4200.00, 5.00, 10.00],
        ['PHONE001', 'هاتف آيفون 14', 'iPhone 14', $electronics_cat, $piece_unit, 2800.00, 3500.00, 3.00, 8.00],
        ['DESK001', 'مكتب خشبي', 'Wooden Desk', $furniture_cat, $piece_unit, 800.00, 1200.00, 2.00, 5.00],
        ['CHAIR001', 'كرسي مكتبي', 'Office Chair', $furniture_cat, $piece_unit, 400.00, 600.00, 5.00, 15.00],
        ['PEN001', 'أقلام حبر جاف', 'Ballpoint Pens', $office_cat, $box_unit, 25.00, 40.00, 10.00, 50.00],
        ['PAPER001', 'ورق طباعة A4', 'A4 Printing Paper', $office_cat, $box_unit, 35.00, 50.00, 20.00, 100.00]
    ];

    $sql = "INSERT IGNORE INTO inventory_products (company_id, product_code, product_name_ar, product_name_en, category_id, unit_id, cost_price, selling_price, min_stock_level, reorder_point, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = $db->prepare($sql);

    foreach ($products as $product) {
        $stmt->execute([$company_id, $product[0], $product[1], $product[2], $product[3], $product[4], $product[5], $product[6], $product[7], $product[8], $user_id]);
        echo "<p>✅ تم إدراج المنتج: {$product[1]}</p>";
    }

    echo "<h2>5. إدراج أرصدة المخزون:</h2>";

    // الحصول على معرف المخزن الرئيسي
    $sql = "SELECT warehouse_id FROM inventory_warehouses WHERE company_id = ? AND warehouse_code = 'MAIN_WH'";
    $stmt = $db->prepare($sql);
    $stmt->execute([$company_id]);
    $main_warehouse = $stmt->fetchColumn();

    // الحصول على معرفات المنتجات
    $sql = "SELECT product_id, product_code, cost_price FROM inventory_products WHERE company_id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$company_id]);
    $products_list = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $stock_quantities = [
        'LAPTOP001' => 15,
        'PHONE001' => 25,
        'DESK001' => 8,
        'CHAIR001' => 20,
        'PEN001' => 150,
        'PAPER001' => 200
    ];

    $sql = "INSERT IGNORE INTO inventory_stock (company_id, product_id, warehouse_id, quantity_on_hand, quantity_available, average_cost, last_cost, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = $db->prepare($sql);

    foreach ($products_list as $product) {
        $quantity = $stock_quantities[$product['product_code']] ?? 10;
        $stmt->execute([$company_id, $product['product_id'], $main_warehouse, $quantity, $quantity, $product['cost_price'], $product['cost_price'], $user_id]);
        echo "<p>✅ تم إدراج رصيد المنتج: {$product['product_code']} - الكمية: $quantity</p>";
    }

    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin-top: 20px;'>";
    echo "<h3>🎉 تم إدراج البيانات التجريبية بنجاح!</h3>";
    echo "<p>يمكنك الآن العودة إلى <a href='/inventory'>وحدة المخزون</a> لرؤية البيانات الحقيقية.</p>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h3>❌ خطأ في إدراج البيانات</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='/check_inventory_data.php'>← فحص البيانات مرة أخرى</a></p>";
echo "<p><a href='/inventory'>← العودة إلى وحدة المخزون</a></p>";
?>
