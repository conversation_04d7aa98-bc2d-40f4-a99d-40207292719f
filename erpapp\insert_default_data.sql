-- إدخال البيانات الافتراضية للنظام
-- مالك الشركة: المستخدم 32
-- الشركة: 4
-- المستخدم المضاف: 35

-- =====================================================
-- 1. إدخال الوحدات المتاحة في النظام
-- =====================================================
INSERT INTO `system_modules` (`module_id`, `program_code`, `module_type`, `name_ar`, `name_en`, `description_ar`, `description_en`, `icon_name`, `base_url`, `is_active`, `version`, `display_order`) VALUES
(1, 'inventory_module', 'addon', 'وحدة إدارة المخزون', 'Inventory Management Module', 'إدارة شاملة للمنتجات والمستودعات وحركة المخزون', 'Complete management of products, warehouses and inventory movements', 'fas fa-boxes', 'inventory', 1, '1.0.0', 1),
(2, 'purchases_module', 'addon', 'وحدة إدارة المشتريات', 'Purchases Management Module', 'إدارة طلبات الشراء والموردين وفواتير الشراء', 'Management of purchase orders, suppliers and purchase invoices', 'fas fa-shopping-cart', 'purchases', 1, '1.0.0', 2),
(3, 'sales_module', 'addon', 'وحدة إدارة المبيعات', 'Sales Management Module', 'إدارة العملاء وعروض الأسعار وفواتير المبيعات', 'Management of customers, quotations and sales invoices', 'fas fa-chart-line', 'sales', 1, '1.0.0', 3),
(4, 'accounting_module', 'addon', 'وحدة المحاسبة', 'Accounting Module', 'النظام المحاسبي الكامل مع دليل الحسابات والقيود', 'Complete accounting system with chart of accounts and journal entries', 'fas fa-calculator', 'accounting', 1, '1.0.0', 4),
(5, 'hr_module', 'addon', 'وحدة الموارد البشرية', 'Human Resources Module', 'إدارة الموظفين والرواتب والحضور والإجازات', 'Management of employees, payroll, attendance and leaves', 'fas fa-users', 'hr', 1, '1.0.0', 5),
(6, 'reports_module', 'core', 'وحدة التقارير', 'Reports Module', 'تقارير شاملة لجميع أقسام النظام', 'Comprehensive reports for all system modules', 'fas fa-chart-bar', 'reports', 1, '1.0.0', 6);

-- =====================================================
-- 2. تنزيل الوحدات للشركة رقم 4
-- =====================================================
INSERT INTO `company_modules` (`company_id`, `module_id`, `module_version`, `installation_date`, `installed_by`, `is_active`, `license_expires_at`) VALUES
(4, 1, '1.0.0', NOW(), 32, 1, '2025-12-31 23:59:59'), -- وحدة المخزون
(4, 2, '1.0.0', NOW(), 32, 1, '2025-12-31 23:59:59'), -- وحدة المشتريات  
(4, 3, '1.0.0', NOW(), 32, 1, '2025-12-31 23:59:59'), -- وحدة المبيعات
(4, 4, '1.0.0', NOW(), 32, 1, '2025-12-31 23:59:59'), -- وحدة المحاسبة
(4, 6, '1.0.0', NOW(), 32, 1, '2025-12-31 23:59:59'); -- وحدة التقارير

-- =====================================================
-- 3. إضافة البرامج الفرعية لوحدة المخزون
-- =====================================================
INSERT INTO `company_all_programs` (`company_id`, `module_id`, `parent_program_id`, `name_ar`, `name_en`, `program_code`, `status_en`, `status_ar`, `display_order`, `program_type`, `page_url`, `icon_name`, `category`) VALUES
-- البرنامج الرئيسي للمخزون
(4, 1, NULL, 'إدارة المخزون', 'inventory_management', 'inventory_main', 'Active', 'نشط', 1, 'Main', 'inventory', 'fas fa-boxes', 'Programs'),

-- البرامج الفرعية للمخزون
(4, 1, 1, 'المنتجات', 'inventory_products', 'products', 'Active', 'نشط', 11, 'Sub', 'inventory/products', 'fas fa-box', 'Programs'),
(4, 1, 1, 'الفئات', 'inventory_categories', 'categories', 'Active', 'نشط', 12, 'Sub', 'inventory/categories', 'fas fa-tags', 'Programs'),
(4, 1, 1, 'وحدات القياس', 'inventory_units', 'units', 'Active', 'نشط', 13, 'Sub', 'inventory/units', 'fas fa-ruler', 'Programs'),
(4, 1, 1, 'المستودعات', 'inventory_warehouses', 'warehouses', 'Active', 'نشط', 14, 'Sub', 'inventory/warehouses', 'fas fa-warehouse', 'Programs'),
(4, 1, 1, 'حركة المخزون', 'inventory_movements', 'movements', 'Active', 'نشط', 15, 'Sub', 'inventory/movements', 'fas fa-exchange-alt', 'Programs');

-- =====================================================
-- 4. إضافة البرامج الفرعية لوحدة المشتريات
-- =====================================================
INSERT INTO `company_all_programs` (`company_id`, `module_id`, `parent_program_id`, `name_ar`, `name_en`, `program_code`, `status_en`, `status_ar`, `display_order`, `program_type`, `page_url`, `icon_name`, `category`) VALUES
-- البرنامج الرئيسي للمشتريات
(4, 2, NULL, 'إدارة المشتريات', 'purchases_management', 'purchases_main', 'Active', 'نشط', 2, 'Main', 'purchases', 'fas fa-shopping-cart', 'Programs'),

-- البرامج الفرعية للمشتريات
(4, 2, 7, 'طلبات الشراء', 'purchase_orders', 'purchase_orders', 'Active', 'نشط', 21, 'Sub', 'purchases/orders', 'fas fa-file-alt', 'Programs'),
(4, 2, 7, 'الموردين', 'suppliers', 'suppliers', 'Active', 'نشط', 22, 'Sub', 'purchases/suppliers', 'fas fa-truck', 'Programs'),
(4, 2, 7, 'استلام البضائع', 'receiving', 'receiving', 'Active', 'نشط', 23, 'Sub', 'purchases/receiving', 'fas fa-dolly', 'Programs'),
(4, 2, 7, 'فواتير الشراء', 'purchase_invoices', 'purchase_invoices', 'Active', 'نشط', 24, 'Sub', 'purchases/invoices', 'fas fa-file-invoice', 'Programs');

-- =====================================================
-- 5. إضافة البرامج الفرعية لوحدة المبيعات
-- =====================================================
INSERT INTO `company_all_programs` (`company_id`, `module_id`, `parent_program_id`, `name_ar`, `name_en`, `program_code`, `status_en`, `status_ar`, `display_order`, `program_type`, `page_url`, `icon_name`, `category`) VALUES
-- البرنامج الرئيسي للمبيعات
(4, 3, NULL, 'إدارة المبيعات', 'sales_management', 'sales_main', 'Active', 'نشط', 3, 'Main', 'sales', 'fas fa-chart-line', 'Programs'),

-- البرامج الفرعية للمبيعات
(4, 3, 12, 'العملاء', 'customers', 'customers', 'Active', 'نشط', 31, 'Sub', 'sales/customers', 'fas fa-users', 'Programs'),
(4, 3, 12, 'عروض الأسعار', 'quotations', 'quotations', 'Active', 'نشط', 32, 'Sub', 'sales/quotations', 'fas fa-file-contract', 'Programs'),
(4, 3, 12, 'أوامر البيع', 'sales_orders', 'sales_orders', 'Active', 'نشط', 33, 'Sub', 'sales/orders', 'fas fa-clipboard-list', 'Programs'),
(4, 3, 12, 'فواتير المبيعات', 'sales_invoices', 'sales_invoices', 'Active', 'نشط', 34, 'Sub', 'sales/invoices', 'fas fa-file-invoice-dollar', 'Programs');

-- =====================================================
-- 6. إضافة البرامج الفرعية لوحدة المحاسبة
-- =====================================================
INSERT INTO `company_all_programs` (`company_id`, `module_id`, `parent_program_id`, `name_ar`, `name_en`, `program_code`, `status_en`, `status_ar`, `display_order`, `program_type`, `page_url`, `icon_name`, `category`) VALUES
-- البرنامج الرئيسي للمحاسبة
(4, 4, NULL, 'المحاسبة', 'accounting_management', 'accounting_main', 'Active', 'نشط', 4, 'Main', 'accounting', 'fas fa-calculator', 'Programs'),

-- البرامج الفرعية للمحاسبة
(4, 4, 17, 'دليل الحسابات', 'chart_of_accounts', 'chart_accounts', 'Active', 'نشط', 41, 'Sub', 'accounting/accounts', 'fas fa-list', 'Programs'),
(4, 4, 17, 'القيود اليومية', 'journal_entries', 'journal_entries', 'Active', 'نشط', 42, 'Sub', 'accounting/entries', 'fas fa-book', 'Programs'),
(4, 4, 17, 'المدفوعات', 'payments', 'payments', 'Active', 'نشط', 43, 'Sub', 'accounting/payments', 'fas fa-credit-card', 'Programs'),
(4, 4, 17, 'المقبوضات', 'receipts', 'receipts', 'Active', 'نشط', 44, 'Sub', 'accounting/receipts', 'fas fa-money-bill', 'Programs');

-- =====================================================
-- 7. إضافة البرامج الفرعية لوحدة التقارير
-- =====================================================
INSERT INTO `company_all_programs` (`company_id`, `module_id`, `parent_program_id`, `name_ar`, `name_en`, `program_code`, `status_en`, `status_ar`, `display_order`, `program_type`, `page_url`, `icon_name`, `category`) VALUES
-- البرنامج الرئيسي للتقارير
(4, 6, NULL, 'التقارير', 'reports_management', 'reports_main', 'Active', 'نشط', 6, 'Main', 'reports', 'fas fa-chart-bar', 'Programs'),

-- البرامج الفرعية للتقارير
(4, 6, 22, 'تقارير المخزون', 'inventory_reports', 'inventory_reports', 'Active', 'نشط', 61, 'Sub', 'reports/inventory', 'fas fa-chart-line', 'Programs'),
(4, 6, 22, 'التقارير المالية', 'financial_reports', 'financial_reports', 'Active', 'نشط', 62, 'Sub', 'reports/financial', 'fas fa-chart-pie', 'Programs'),
(4, 6, 22, 'تقارير المبيعات', 'sales_reports', 'sales_reports', 'Active', 'نشط', 63, 'Sub', 'reports/sales', 'fas fa-chart-area', 'Programs'),
(4, 6, 22, 'تقارير المشتريات', 'purchases_reports', 'purchases_reports', 'Active', 'نشط', 64, 'Sub', 'reports/purchases', 'fas fa-chart-column', 'Programs');

-- =====================================================
-- 8. إضافة المناصب للشركة
-- =====================================================
INSERT INTO `positions` (`CompanyID`, `CreatedBy`, `PositionNameAR`, `PositionNameEN`, `Description`) VALUES
(4, 32, 'مدير عام', 'General Manager', 'صلاحيات كاملة على جميع الوحدات والبرامج'),
(4, 32, 'مدير المخزون', 'Inventory Manager', 'صلاحيات كاملة على وحدة المخزون'),
(4, 32, 'مدير المبيعات', 'Sales Manager', 'صلاحيات كاملة على وحدة المبيعات'),
(4, 32, 'محاسب', 'Accountant', 'صلاحيات على وحدة المحاسبة والتقارير المالية'),
(4, 32, 'موظف مخزون', 'Inventory Employee', 'صلاحيات محدودة على وحدة المخزون');

-- =====================================================
-- 9. إضافة المستخدم 35 في الشركة 4
-- =====================================================
INSERT INTO `company_users` (`user_id`, `company_id`, `added_by_user_id`, `status`, `status_ar`, `user_status`, `position_id`, `notes`) VALUES
(35, 4, 32, 'accepted', 'مقبول', 'active', 2, 'مدير المخزون - تم إضافته من قبل مالك الشركة');

-- =====================================================
-- 10. إضافة الصلاحيات للمناصب
-- =====================================================

-- صلاحيات مدير عام (منصب 1) - صلاحيات كاملة على جميع البرامج
INSERT INTO `permissions` (`PositionID`, `CompanyID`, `CreatedBy`, `ProgramID`, `CanView`, `CanCreate`, `CanEdit`, `CanDelete`, `CanApprove`) 
SELECT 1, 4, 32, program_id, 1, 1, 1, 1, 1 
FROM `company_all_programs` 
WHERE company_id = 4;

-- صلاحيات مدير المخزون (منصب 2) - صلاحيات كاملة على وحدة المخزون فقط
INSERT INTO `permissions` (`PositionID`, `CompanyID`, `CreatedBy`, `ProgramID`, `CanView`, `CanCreate`, `CanEdit`, `CanDelete`, `CanApprove`) 
SELECT 2, 4, 32, program_id, 1, 1, 1, 1, 0 
FROM `company_all_programs` 
WHERE company_id = 4 AND module_id = 1; -- وحدة المخزون

-- صلاحيات مدير المبيعات (منصب 3) - صلاحيات كاملة على وحدة المبيعات
INSERT INTO `permissions` (`PositionID`, `CompanyID`, `CreatedBy`, `ProgramID`, `CanView`, `CanCreate`, `CanEdit`, `CanDelete`, `CanApprove`) 
SELECT 3, 4, 32, program_id, 1, 1, 1, 1, 0 
FROM `company_all_programs` 
WHERE company_id = 4 AND module_id = 3; -- وحدة المبيعات

-- صلاحيات المحاسب (منصب 4) - صلاحيات على المحاسبة والتقارير
INSERT INTO `permissions` (`PositionID`, `CompanyID`, `CreatedBy`, `ProgramID`, `CanView`, `CanCreate`, `CanEdit`, `CanDelete`, `CanApprove`) 
SELECT 4, 4, 32, program_id, 1, 1, 1, 0, 0 
FROM `company_all_programs` 
WHERE company_id = 4 AND module_id IN (4, 6); -- المحاسبة والتقارير

-- صلاحيات موظف المخزون (منصب 5) - صلاحيات محدودة على المخزون
INSERT INTO `permissions` (`PositionID`, `CompanyID`, `CreatedBy`, `ProgramID`, `CanView`, `CanCreate`, `CanEdit`, `CanDelete`, `CanApprove`) 
SELECT 5, 4, 32, program_id, 1, 1, 0, 0, 0 
FROM `company_all_programs` 
WHERE company_id = 4 AND module_id = 1; -- وحدة المخزون

-- =====================================================
-- 11. تحديث الشركة الحالية للمستخدمين
-- =====================================================
UPDATE `users` SET `current_company_id` = 4 WHERE `UserID` IN (32, 35);

-- =====================================================
-- تم الانتهاء من إدخال البيانات الافتراضية
-- =====================================================
