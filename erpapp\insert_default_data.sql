-- إدخال البيانات الافتراضية للنظام
-- مالك الشركة: المستخدم 32
-- الشركة: 4
-- المستخدم المضاف: 35

-- =====================================================
-- 1. إدخال الوحدات المتاحة في النظام (استخدام الجدول الموجود)
-- =====================================================
INSERT INTO `system_programs` (`program_code`, `name_ar`, `name_en`, `description_ar`, `description_en`, `icon_name`, `is_active`, `version`, `display_order`) VALUES
('inventory_module', 'وحدة إدارة المخزون', 'Inventory Management Module', 'إدارة شاملة للمنتجات والمستودعات وحركة المخزون', 'Complete management of products, warehouses and inventory movements', 'fas fa-boxes', 1, '1.0.0', 1),
('purchases_module', 'وحدة إدارة المشتريات', 'Purchases Management Module', 'إدارة طلبات الشراء والموردين وفواتير الشراء', 'Management of purchase orders, suppliers and purchase invoices', 'fas fa-shopping-cart', 1, '1.0.0', 2),
('sales_module', 'وحدة إدارة المبيعات', 'Sales Management Module', 'إدارة العملاء وعروض الأسعار وفواتير المبيعات', 'Management of customers, quotations and sales invoices', 'fas fa-chart-line', 1, '1.0.0', 3),
('accounting_module', 'وحدة المحاسبة', 'Accounting Module', 'النظام المحاسبي الكامل مع دليل الحسابات والقيود', 'Complete accounting system with chart of accounts and journal entries', 'fas fa-calculator', 1, '1.0.0', 4),
('hr_module', 'وحدة الموارد البشرية', 'Human Resources Module', 'إدارة الموظفين والرواتب والحضور والإجازات', 'Management of employees, payroll, attendance and leaves', 'fas fa-users', 1, '1.0.0', 5),
('reports_module', 'وحدة التقارير', 'Reports Module', 'تقارير شاملة لجميع أقسام النظام', 'Comprehensive reports for all system modules', 'fas fa-chart-bar', 1, '1.0.0', 6);

-- =====================================================
-- 2. تنزيل الوحدات للشركة رقم 4 (استخدام الجدول الموجود)
-- =====================================================
INSERT INTO `company_installed_programs` (`company_id`, `program_id`, `installation_date`, `installed_by`, `is_active`) VALUES
(4, 1, NOW(), 32, 1), -- وحدة المخزون
(4, 2, NOW(), 32, 1), -- وحدة المشتريات
(4, 3, NOW(), 32, 1), -- وحدة المبيعات
(4, 4, NOW(), 32, 1), -- وحدة المحاسبة
(4, 6, NOW(), 32, 1); -- وحدة التقارير

-- =====================================================
-- 3. إضافة البرامج الفرعية لوحدة المخزون
-- =====================================================
INSERT INTO `company_all_programs` (`company_id`, `parent_program_id`, `name_ar`, `name_en`, `status_en`, `status_ar`, `display_order`, `program_type`, `page_url`, `icon_name`, `category`) VALUES
-- البرنامج الرئيسي للمخزون
(4, NULL, 'إدارة المخزون', 'inventory_management', 'Active', 'نشط', 1, 'Main', 'inventory', 'fas fa-boxes', 'Programs'),

-- البرامج الفرعية للمخزون
(4, 1, 'المنتجات', 'inventory_products', 'Active', 'نشط', 11, 'Sub', 'inventory/products', 'fas fa-box', 'Programs'),
(4, 1, 'الفئات', 'inventory_categories', 'Active', 'نشط', 12, 'Sub', 'inventory/categories', 'fas fa-tags', 'Programs'),
(4, 1, 'وحدات القياس', 'inventory_units', 'Active', 'نشط', 13, 'Sub', 'inventory/units', 'fas fa-ruler', 'Programs'),
(4, 1, 'المستودعات', 'inventory_warehouses', 'Active', 'نشط', 14, 'Sub', 'inventory/warehouses', 'fas fa-warehouse', 'Programs'),
(4, 1, 'حركة المخزون', 'inventory_movements', 'Active', 'نشط', 15, 'Sub', 'inventory/movements', 'fas fa-exchange-alt', 'Programs');

-- =====================================================
-- 4. إضافة البرامج الفرعية لوحدة المشتريات
-- =====================================================
INSERT INTO `company_all_programs` (`company_id`, `module_id`, `parent_program_id`, `name_ar`, `name_en`, `program_code`, `status_en`, `status_ar`, `display_order`, `program_type`, `page_url`, `icon_name`, `category`) VALUES
-- البرنامج الرئيسي للمشتريات
(4, 2, NULL, 'إدارة المشتريات', 'purchases_management', 'purchases_main', 'Active', 'نشط', 2, 'Main', 'purchases', 'fas fa-shopping-cart', 'Programs'),

-- البرامج الفرعية للمشتريات
(4, 2, 7, 'طلبات الشراء', 'purchase_orders', 'purchase_orders', 'Active', 'نشط', 21, 'Sub', 'purchases/orders', 'fas fa-file-alt', 'Programs'),
(4, 2, 7, 'الموردين', 'suppliers', 'suppliers', 'Active', 'نشط', 22, 'Sub', 'purchases/suppliers', 'fas fa-truck', 'Programs'),
(4, 2, 7, 'استلام البضائع', 'receiving', 'receiving', 'Active', 'نشط', 23, 'Sub', 'purchases/receiving', 'fas fa-dolly', 'Programs'),
(4, 2, 7, 'فواتير الشراء', 'purchase_invoices', 'purchase_invoices', 'Active', 'نشط', 24, 'Sub', 'purchases/invoices', 'fas fa-file-invoice', 'Programs');

-- =====================================================
-- 5. إضافة البرامج الفرعية لوحدة المبيعات
-- =====================================================
INSERT INTO `company_all_programs` (`company_id`, `module_id`, `parent_program_id`, `name_ar`, `name_en`, `program_code`, `status_en`, `status_ar`, `display_order`, `program_type`, `page_url`, `icon_name`, `category`) VALUES
-- البرنامج الرئيسي للمبيعات
(4, 3, NULL, 'إدارة المبيعات', 'sales_management', 'sales_main', 'Active', 'نشط', 3, 'Main', 'sales', 'fas fa-chart-line', 'Programs'),

-- البرامج الفرعية للمبيعات
(4, 3, 12, 'العملاء', 'customers', 'customers', 'Active', 'نشط', 31, 'Sub', 'sales/customers', 'fas fa-users', 'Programs'),
(4, 3, 12, 'عروض الأسعار', 'quotations', 'quotations', 'Active', 'نشط', 32, 'Sub', 'sales/quotations', 'fas fa-file-contract', 'Programs'),
(4, 3, 12, 'أوامر البيع', 'sales_orders', 'sales_orders', 'Active', 'نشط', 33, 'Sub', 'sales/orders', 'fas fa-clipboard-list', 'Programs'),
(4, 3, 12, 'فواتير المبيعات', 'sales_invoices', 'sales_invoices', 'Active', 'نشط', 34, 'Sub', 'sales/invoices', 'fas fa-file-invoice-dollar', 'Programs');

-- =====================================================
-- 6. إضافة البرامج الفرعية لوحدة المحاسبة
-- =====================================================
INSERT INTO `company_all_programs` (`company_id`, `module_id`, `parent_program_id`, `name_ar`, `name_en`, `program_code`, `status_en`, `status_ar`, `display_order`, `program_type`, `page_url`, `icon_name`, `category`) VALUES
-- البرنامج الرئيسي للمحاسبة
(4, 4, NULL, 'المحاسبة', 'accounting_management', 'accounting_main', 'Active', 'نشط', 4, 'Main', 'accounting', 'fas fa-calculator', 'Programs'),

-- البرامج الفرعية للمحاسبة
(4, 4, 17, 'دليل الحسابات', 'chart_of_accounts', 'chart_accounts', 'Active', 'نشط', 41, 'Sub', 'accounting/accounts', 'fas fa-list', 'Programs'),
(4, 4, 17, 'القيود اليومية', 'journal_entries', 'journal_entries', 'Active', 'نشط', 42, 'Sub', 'accounting/entries', 'fas fa-book', 'Programs'),
(4, 4, 17, 'المدفوعات', 'payments', 'payments', 'Active', 'نشط', 43, 'Sub', 'accounting/payments', 'fas fa-credit-card', 'Programs'),
(4, 4, 17, 'المقبوضات', 'receipts', 'receipts', 'Active', 'نشط', 44, 'Sub', 'accounting/receipts', 'fas fa-money-bill', 'Programs');

-- =====================================================
-- 7. إضافة البرامج الفرعية لوحدة التقارير
-- =====================================================
INSERT INTO `company_all_programs` (`company_id`, `module_id`, `parent_program_id`, `name_ar`, `name_en`, `program_code`, `status_en`, `status_ar`, `display_order`, `program_type`, `page_url`, `icon_name`, `category`) VALUES
-- البرنامج الرئيسي للتقارير
(4, 6, NULL, 'التقارير', 'reports_management', 'reports_main', 'Active', 'نشط', 6, 'Main', 'reports', 'fas fa-chart-bar', 'Programs'),

-- البرامج الفرعية للتقارير
(4, 6, 22, 'تقارير المخزون', 'inventory_reports', 'inventory_reports', 'Active', 'نشط', 61, 'Sub', 'reports/inventory', 'fas fa-chart-line', 'Programs'),
(4, 6, 22, 'التقارير المالية', 'financial_reports', 'financial_reports', 'Active', 'نشط', 62, 'Sub', 'reports/financial', 'fas fa-chart-pie', 'Programs'),
(4, 6, 22, 'تقارير المبيعات', 'sales_reports', 'sales_reports', 'Active', 'نشط', 63, 'Sub', 'reports/sales', 'fas fa-chart-area', 'Programs'),
(4, 6, 22, 'تقارير المشتريات', 'purchases_reports', 'purchases_reports', 'Active', 'نشط', 64, 'Sub', 'reports/purchases', 'fas fa-chart-column', 'Programs');

-- =====================================================
-- 8. إضافة المناصب للشركة
-- =====================================================
INSERT INTO `positions` (`CompanyID`, `CreatedBy`, `PositionNameAR`, `PositionNameEN`, `Description`) VALUES
(4, 32, 'مدير عام', 'General Manager', 'صلاحيات كاملة على جميع الوحدات والبرامج'),
(4, 32, 'مدير المخزون', 'Inventory Manager', 'صلاحيات كاملة على وحدة المخزون'),
(4, 32, 'مدير المبيعات', 'Sales Manager', 'صلاحيات كاملة على وحدة المبيعات'),
(4, 32, 'محاسب', 'Accountant', 'صلاحيات على وحدة المحاسبة والتقارير المالية'),
(4, 32, 'موظف مخزون', 'Inventory Employee', 'صلاحيات محدودة على وحدة المخزون');

-- =====================================================
-- 9. إضافة المستخدم 35 في الشركة 4
-- =====================================================
INSERT INTO `company_users` (`user_id`, `company_id`, `added_by_user_id`, `status`, `status_ar`, `user_status`, `position_id`, `notes`) VALUES
(35, 4, 32, 'accepted', 'مقبول', 'active', 2, 'مدير المخزون - تم إضافته من قبل مالك الشركة');

-- =====================================================
-- 10. إضافة الصلاحيات للمناصب
-- =====================================================

-- صلاحيات مدير عام (منصب 1) - صلاحيات كاملة على جميع البرامج
INSERT INTO `permissions` (`PositionID`, `CompanyID`, `CreatedBy`, `ProgramID`, `CanView`, `CanCreate`, `CanEdit`, `CanDelete`, `CanApprove`)
SELECT 1, 4, 32, program_id, 1, 1, 1, 1, 1
FROM `company_all_programs`
WHERE company_id = 4;

-- صلاحيات مدير المخزون (منصب 2) - صلاحيات كاملة على وحدة المخزون فقط
INSERT INTO `permissions` (`PositionID`, `CompanyID`, `CreatedBy`, `ProgramID`, `CanView`, `CanCreate`, `CanEdit`, `CanDelete`, `CanApprove`)
SELECT 2, 4, 32, program_id, 1, 1, 1, 1, 0
FROM `company_all_programs`
WHERE company_id = 4 AND module_id = 1; -- وحدة المخزون

-- صلاحيات مدير المبيعات (منصب 3) - صلاحيات كاملة على وحدة المبيعات
INSERT INTO `permissions` (`PositionID`, `CompanyID`, `CreatedBy`, `ProgramID`, `CanView`, `CanCreate`, `CanEdit`, `CanDelete`, `CanApprove`)
SELECT 3, 4, 32, program_id, 1, 1, 1, 1, 0
FROM `company_all_programs`
WHERE company_id = 4 AND module_id = 3; -- وحدة المبيعات

-- صلاحيات المحاسب (منصب 4) - صلاحيات على المحاسبة والتقارير
INSERT INTO `permissions` (`PositionID`, `CompanyID`, `CreatedBy`, `ProgramID`, `CanView`, `CanCreate`, `CanEdit`, `CanDelete`, `CanApprove`)
SELECT 4, 4, 32, program_id, 1, 1, 1, 0, 0
FROM `company_all_programs`
WHERE company_id = 4 AND module_id IN (4, 6); -- المحاسبة والتقارير

-- صلاحيات موظف المخزون (منصب 5) - صلاحيات محدودة على المخزون
INSERT INTO `permissions` (`PositionID`, `CompanyID`, `CreatedBy`, `ProgramID`, `CanView`, `CanCreate`, `CanEdit`, `CanDelete`, `CanApprove`)
SELECT 5, 4, 32, program_id, 1, 1, 0, 0, 0
FROM `company_all_programs`
WHERE company_id = 4 AND module_id = 1; -- وحدة المخزون

-- =====================================================
-- 11. تحديث الشركة الحالية للمستخدمين
-- =====================================================
UPDATE `users` SET `current_company_id` = 4 WHERE `UserID` IN (32, 35);

-- =====================================================
-- تم الانتهاء من إدخال البيانات الافتراضية
-- =====================================================
