<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title">
                    <i class="fas fa-building"></i> <?= e($company['CompanyName']) ?>
                </h1>
                <div>
                    <?php if ($is_owner): ?>
                        <a href="<?= base_url('companies/' . $company['CompanyID'] . '/edit') ?>" class="btn btn-outline-primary me-2">
                            <i class="fas fa-edit me-1"></i> <?= __('تعديل') ?>
                        </a>
                    <?php endif; ?>
                    <a href="<?= base_url('companies') ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> <?= __('العودة') ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php display_flash('company_success'); ?>
    <?php display_flash('company_error'); ?>

    <?php
    // تحديد حالة الاشتراك في بداية الملف
    $subscription_status = $company['subscription_status'] ?? 'Trial';
    ?>

    <div class="row">
        <!-- معلومات الشركة -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i> <?= __('معلومات الشركة') ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center mb-4">
                            <?php if (!empty($company['CompanyLogo'])): ?>
                                <img src="<?= base_url($company['CompanyLogo']) ?>" alt="<?= e($company['CompanyName']) ?>" class="img-fluid rounded" style="max-height: 150px;">
                            <?php else: ?>
                                <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 150px; width: 150px; margin: 0 auto;">
                                    <i class="fas fa-building fa-4x text-muted"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-9">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <h6 class="text-muted mb-1"><?= __('اسم الشركة') ?></h6>
                                    <p class="mb-0"><?= e($company['CompanyName']) ?></p>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-muted mb-1"><?= __('اسم الشركة (بالإنجليزية)') ?></h6>
                                    <p class="mb-0"><?= !empty($company['CompanyNameEN']) ? e($company['CompanyNameEN']) : '<span class="text-muted">-</span>' ?></p>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <h6 class="text-muted mb-1"><?= __('البريد الإلكتروني') ?></h6>
                                    <p class="mb-0"><?= e($company['CompanyEmail']) ?></p>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-muted mb-1"><?= __('رقم الهاتف') ?></h6>
                                    <p class="mb-0"><?= e($company['CompanyPhone']) ?></p>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <h6 class="text-muted mb-1"><?= __('الموقع الإلكتروني') ?></h6>
                                    <p class="mb-0">
                                        <?php if (!empty($company['CompanyWebsite'])): ?>
                                            <a href="<?= e($company['CompanyWebsite']) ?>" target="_blank">
                                                <?= e($company['CompanyWebsite']) ?>
                                                <i class="fas fa-external-link-alt ms-1 small"></i>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-muted mb-1"><?= __('الرقم الضريبي') ?></h6>
                                    <p class="mb-0"><?= !empty($company['TaxID']) ? e($company['TaxID']) : '<span class="text-muted">-</span>' ?></p>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="text-muted mb-1"><?= __('العنوان') ?></h6>
                                    <p class="mb-0"><?= !empty($company['CompanyAddress']) ? e($company['CompanyAddress']) : '<span class="text-muted">-</span>' ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الاشتراك الحالي أو الفترة التجريبية -->
            <?php if (isset($current_subscription) && $current_subscription): ?>
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-crown me-2"></i> <?= __('الاشتراك الحالي') ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h5 class="text-primary"><?= $current_subscription['plan_name_ar'] ?></h5>
                                <p class="text-muted mb-2"><?= $current_subscription['billing_cycle'] === 'monthly' ? __('اشتراك شهري') : __('اشتراك سنوي') ?></p>

                                <div class="row mb-3">
                                    <div class="col-sm-6">
                                        <strong><?= __('الحالة') ?>:</strong>
                                        <?php if ($current_subscription['status'] === 'active'): ?>
                                            <span class="badge bg-success"><?= __('نشط') ?></span>
                                        <?php elseif ($current_subscription['status'] === 'canceled'): ?>
                                            <span class="badge bg-danger"><?= __('ملغي') ?></span>
                                        <?php elseif ($current_subscription['status'] === 'expired'): ?>
                                            <span class="badge bg-warning"><?= __('منتهي') ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary"><?= __('معلق') ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="col-sm-6">
                                        <strong><?= __('التجديد التلقائي') ?>:</strong>
                                        <div class="d-flex align-items-center mt-1">
                                            <?php if ($current_subscription['is_auto_renew']): ?>
                                                <span class="badge bg-success me-2"><?= __('مفعل') ?></span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary me-2"><?= __('معطل') ?></span>
                                            <?php endif; ?>

                                            <?php if ($is_owner): ?>
                                                <form method="POST" action="<?= base_url('subscriptions/toggle-auto-renew/' . $current_subscription['subscription_id']) ?>" class="d-inline">
                                                    <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
                                                    <button type="submit"
                                                            class="btn btn-sm <?= $current_subscription['is_auto_renew'] ? 'btn-outline-danger' : 'btn-outline-success' ?>"
                                                            <?= $current_subscription['status'] !== 'active' ? 'disabled' : '' ?>
                                                            title="<?= $current_subscription['status'] !== 'active' ? __('متاح فقط للاشتراكات النشطة') : '' ?>">
                                                        <i class="fas fa-<?= $current_subscription['is_auto_renew'] ? 'times' : 'check' ?> me-1"></i>
                                                        <?= $current_subscription['is_auto_renew'] ? __('إيقاف') : __('تفعيل') ?>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-sm-6">
                                        <strong><?= __('تاريخ البدء') ?>:</strong>
                                        <br><?= date('Y-m-d', strtotime($current_subscription['start_date'])) ?>
                                    </div>
                                    <div class="col-sm-6">
                                        <strong><?= __('تاريخ الانتهاء') ?>:</strong>
                                        <br><?= date('Y-m-d', strtotime($current_subscription['end_date'])) ?>
                                    </div>
                                </div>

                                <!-- شريط تقدم الاشتراك -->
                                <?php if ($current_subscription['status'] === 'active'): ?>
                                    <?php
                                    $start_date = new DateTime($current_subscription['start_date']);
                                    $end_date = new DateTime($current_subscription['end_date']);
                                    $now = new DateTime();

                                    $total_days = $start_date->diff($end_date)->days;
                                    $days_remaining = $now <= $end_date ? $end_date->diff($now)->days : 0;
                                    $days_used = $total_days - $days_remaining;
                                    $subscription_percentage = min(100, max(0, ($days_used / $total_days) * 100));

                                    // تحديد لون الشريط حسب الوقت المتبقي
                                    $sub_progress_class = 'bg-success';
                                    $sub_status_class = 'success';

                                    if ($days_remaining <= 7 && $days_remaining > 3) {
                                        $sub_progress_class = 'bg-warning';
                                        $sub_status_class = 'warning';
                                    } elseif ($days_remaining <= 3) {
                                        $sub_progress_class = 'bg-danger';
                                        $sub_status_class = 'danger';
                                    }
                                    ?>
                                    <div class="mt-4">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="small text-muted"><?= __('تقدم فترة الاشتراك') ?></span>
                                            <span class="small text-<?= $sub_status_class ?> fw-bold">
                                                <?= $days_remaining ?> <?= __('يوم متبقي') ?>
                                            </span>
                                        </div>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar <?= $sub_progress_class ?>" role="progressbar"
                                                 style="width: <?= $subscription_percentage ?>%"
                                                 aria-valuenow="<?= $subscription_percentage ?>"
                                                 aria-valuemin="0"
                                                 aria-valuemax="100">
                                            </div>
                                        </div>
                                        <div class="d-flex justify-content-between small text-muted mt-1">
                                            <span><?= $start_date->format('M d') ?></span>
                                            <span><?= $end_date->format('M d') ?></span>
                                        </div>

                                        <?php if ($days_remaining <= 7): ?>
                                            <div class="alert alert-<?= $sub_status_class ?> p-2 mt-2">
                                                <small>
                                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                                    <?php if ($days_remaining > 0): ?>
                                                        <?= __('ينتهي الاشتراك خلال') ?> <?= $days_remaining ?> <?= __('أيام') ?>
                                                    <?php else: ?>
                                                        <?= __('انتهى الاشتراك') ?>
                                                    <?php endif; ?>
                                                </small>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="mb-3">
                                    <h4 class="text-primary mb-0">
                                        <?php if ($current_subscription['billing_cycle'] === 'monthly'): ?>
                                            <?= number_format($current_subscription['price_monthly'], 0) ?> <?= __('ريال/شهر') ?>
                                        <?php else: ?>
                                            <?= number_format($current_subscription['price_yearly'], 0) ?> <?= __('ريال/سنة') ?>
                                        <?php endif; ?>
                                    </h4>
                                </div>

                                <?php if ($is_owner): ?>
                                    <?php if ($current_subscription['status'] === 'active'): ?>
                                        <button type="button" class="btn btn-danger btn-sm mb-2" data-bs-toggle="modal" data-bs-target="#cancelSubscriptionModal">
                                            <i class="fas fa-times-circle me-1"></i> <?= __('إلغاء الاشتراك') ?>
                                        </button>
                                        <br>
                                    <?php elseif ($current_subscription['status'] === 'expired' || $current_subscription['status'] === 'canceled'): ?>
                                        <a href="<?= base_url('subscriptions/plans?company_id=' . $company['CompanyID']) ?>" class="btn btn-primary btn-sm mb-2">
                                            <i class="fas fa-sync me-1"></i> <?= __('تجديد الاشتراك') ?>
                                        </a>
                                        <br>
                                    <?php endif; ?>
                                    <a href="<?= base_url('subscriptions/plans?company_id=' . $company['CompanyID']) ?>" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-crown me-1"></i> <?= __('ترقية الباقة') ?>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modal إلغاء الاشتراك -->
                <?php if ($is_owner && $current_subscription['status'] === 'active'): ?>
                    <div class="modal fade" id="cancelSubscriptionModal" tabindex="-1" aria-labelledby="cancelSubscriptionModalLabel" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header bg-danger text-white">
                                    <h5 class="modal-title" id="cancelSubscriptionModalLabel">
                                        <i class="fas fa-exclamation-triangle me-2"></i> <?= __('إلغاء الاشتراك') ?>
                                    </h5>
                                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-circle me-2"></i>
                                        <strong><?= __('تحذير') ?>:</strong> <?= __('إلغاء الاشتراك سيؤدي إلى فقدان الوصول إلى الميزات المتقدمة بعد انتهاء فترة الاشتراك الحالية.') ?>
                                    </div>
                                    <p><?= __('هل أنت متأكد من رغبتك في إلغاء اشتراكك في خطة') ?> <strong>"<?= $current_subscription['plan_name_ar'] ?>"</strong>؟</p>
                                    <form method="POST" action="<?= base_url('subscriptions/' . $current_subscription['subscription_id'] . '/cancel') ?>" id="cancelSubscriptionForm">
                                        <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
                                    </form>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                        <i class="fas fa-times me-1"></i> <?= __('إلغاء') ?>
                                    </button>
                                    <button type="submit" form="cancelSubscriptionForm" class="btn btn-danger">
                                        <i class="fas fa-check me-1"></i> <?= __('تأكيد الإلغاء') ?>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            <?php elseif ($company['subscription_status'] === 'Trial'): ?>
                <!-- الفترة التجريبية -->
                <?php
                // حساب بيانات الفترة التجريبية
                $trial_start = new DateTime($company['trial_start_date']);
                $trial_end = new DateTime($company['trial_end_date']);
                $now = new DateTime();

                $total_days = $trial_start->diff($trial_end)->days;
                $days_remaining = $now <= $trial_end ? $trial_end->diff($now)->days : 0;
                $days_used = $total_days - $days_remaining;
                $percentage = min(100, max(0, ($days_used / $total_days) * 100));

                // تحديد لون الشريط والحالة
                $status_class = 'info';
                $progress_class = 'bg-info';
                $header_class = 'bg-info';

                if ($days_remaining <= 3 && $days_remaining > 0) {
                    $status_class = 'warning';
                    $progress_class = 'bg-warning';
                    $header_class = 'bg-warning';
                } elseif ($days_remaining <= 0) {
                    $status_class = 'danger';
                    $progress_class = 'bg-danger';
                    $header_class = 'bg-danger';
                }
                ?>
                <div class="card mb-4">
                    <div class="card-header <?= $header_class ?> text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-hourglass-half me-2"></i> <?= __('الفترة التجريبية') ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h5 class="text-<?= $status_class ?>"><?= __('فترة تجريبية مجانية') ?></h5>
                                <p class="text-muted mb-3"><?= __('استمتع بجميع الميزات مجاناً خلال الفترة التجريبية') ?></p>

                                <div class="row mb-3">
                                    <div class="col-sm-6">
                                        <strong><?= __('الحالة') ?>:</strong>
                                        <?php if ($days_remaining > 3): ?>
                                            <span class="badge bg-info"><?= __('نشطة') ?></span>
                                        <?php elseif ($days_remaining > 0): ?>
                                            <span class="badge bg-warning"><?= __('تنتهي قريباً') ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-danger"><?= __('منتهية') ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="col-sm-6">
                                        <strong><?= __('الأيام المتبقية') ?>:</strong>
                                        <span class="text-<?= $status_class ?> fw-bold">
                                            <?= max(0, $days_remaining) ?> <?= __('يوم') ?>
                                        </span>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-sm-6">
                                        <strong><?= __('تاريخ البدء') ?>:</strong>
                                        <br><?= $trial_start->format('Y-m-d') ?>
                                    </div>
                                    <div class="col-sm-6">
                                        <strong><?= __('تاريخ الانتهاء') ?>:</strong>
                                        <br><?= $trial_end->format('Y-m-d') ?>
                                    </div>
                                </div>

                                <!-- شريط التقدم -->
                                <div class="mt-4">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span class="small text-muted"><?= __('تقدم الفترة التجريبية') ?></span>
                                        <span class="small text-<?= $status_class ?> fw-bold"><?= round($percentage, 1) ?>%</span>
                                    </div>
                                    <div class="progress" style="height: 10px;">
                                        <div class="progress-bar <?= $progress_class ?>" role="progressbar"
                                             style="width: <?= $percentage ?>%"
                                             aria-valuenow="<?= $percentage ?>"
                                             aria-valuemin="0"
                                             aria-valuemax="100">
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-between small text-muted mt-1">
                                        <span><?= $trial_start->format('M d') ?></span>
                                        <span><?= $trial_end->format('M d') ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="mb-3">
                                    <h4 class="text-success mb-0">
                                        <i class="fas fa-gift me-1"></i> <?= __('مجاني') ?>
                                    </h4>
                                    <small class="text-muted"><?= __('خلال الفترة التجريبية') ?></small>
                                </div>

                                <?php if ($is_owner): ?>
                                    <?php if ($days_remaining <= 7): ?>
                                        <div class="alert alert-<?= $status_class ?> p-2 mb-3">
                                            <small>
                                                <i class="fas fa-clock me-1"></i>
                                                <?php if ($days_remaining > 0): ?>
                                                    <?= __('تنتهي خلال') ?> <?= $days_remaining ?> <?= __('أيام') ?>
                                                <?php else: ?>
                                                    <?= __('انتهت الفترة التجريبية') ?>
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                    <?php endif; ?>

                                    <a href="<?= base_url('subscriptions/plans?company_id=' . $company['CompanyID']) ?>" class="btn btn-primary btn-sm mb-2">
                                        <i class="fas fa-crown me-1"></i> <?= __('اختر خطة الاشتراك') ?>
                                    </a>
                                    <br>
                                    <small class="text-muted">
                                        <?= __('احصل على ميزات إضافية') ?>
                                    </small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <!-- لا يوجد اشتراك حالي -->
                <div class="card mb-4">
                    <div class="card-header bg-warning text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i> <?= __('لا يوجد اشتراك نشط') ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info mb-0">
                            <div class="d-flex">
                                <div class="me-3">
                                    <i class="fas fa-info-circle fa-2x"></i>
                                </div>
                                <div>
                                    <h5><?= __('لا يوجد اشتراك نشط') ?></h5>
                                    <p class="mb-3"><?= __('هذه الشركة لا تملك اشتراك نشط حالياً. للاستفادة من جميع الميزات، يرجى اختيار خطة اشتراك مناسبة.') ?></p>
                                    <?php if ($is_owner): ?>
                                        <a href="<?= base_url('subscriptions/plans?company_id=' . $company['CompanyID']) ?>" class="btn btn-primary">
                                            <i class="fas fa-crown me-1"></i> <?= __('عرض خطط الاشتراك') ?>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- الإحصائيات والإجراءات -->
        <div class="col-lg-4">
            <!-- الإحصائيات -->
            <div class="card mb-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i> <?= __('إحصائيات الشركة') ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 mb-3">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <i class="fas fa-users fa-2x text-primary mb-2"></i>
                                    <h3 class="mb-0"><?= $user_count ?></h3>
                                    <p class="text-muted mb-0"><?= __('المستخدمين') ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <i class="fas fa-puzzle-piece fa-2x text-success mb-2"></i>
                                    <h3 class="mb-0"><?= $program_count ?></h3>
                                    <p class="text-muted mb-0"><?= __('البرامج') ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 mt-3">
                        <a href="<?= base_url('dashboard?company=' . $company['CompanyID']) ?>" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-1"></i> <?= __('الدخول إلى لوحة التحكم') ?>
                        </a>
                    </div>
                </div>
            </div>

            <!-- الإجراءات -->
            <?php if ($is_owner): ?>
                <div class="card">
                    <div class="card-header bg-dark text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-cogs me-2"></i> <?= __('إجراءات') ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group">
                            <a href="<?= base_url('companies/' . $company['CompanyID'] . '/users') ?>" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-user-friends me-2 text-primary"></i>
                                    <?= __('إدارة المستخدمين') ?>
                                </div>
                                <i class="fas fa-chevron-right text-muted"></i>
                            </a>
                            <a href="<?= base_url('companies/' . $company['CompanyID'] . '/programs') ?>" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-puzzle-piece me-2 text-success"></i>
                                    <?= __('إدارة البرامج') ?>
                                </div>
                                <i class="fas fa-chevron-right text-muted"></i>
                            </a>
                            <a href="<?= base_url('companies/' . $company['CompanyID'] . '/positions') ?>" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-id-badge me-2 text-info"></i>
                                    <?= __('إدارة المناصب والصلاحيات') ?>
                                </div>
                                <i class="fas fa-chevron-right text-muted"></i>
                            </a>
                            <a href="<?= base_url('companies/' . $company['CompanyID'] . '/settings') ?>" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-cog me-2 text-secondary"></i>
                                    <?= __('إعدادات الشركة') ?>
                                </div>
                                <i class="fas fa-chevron-right text-muted"></i>
                            </a>
                            <a href="<?= base_url('subscriptions/plans?company_id=' . $company['CompanyID']) ?>" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-crown me-2 text-warning"></i>
                                    <?= __('ترقية الباقة') ?>
                                </div>
                                <i class="fas fa-chevron-right text-muted"></i>
                            </a>

                            <?php if ($company['CompanyStatus'] === 'Inactive'): ?>
                            <button type="button" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center text-success" data-bs-toggle="modal" data-bs-target="#activateCompanyModal">
                                <div>
                                    <i class="fas fa-check-circle me-2"></i>
                                    <?= __('تفعيل الشركة') ?>
                                </div>
                                <i class="fas fa-chevron-right text-muted"></i>
                            </button>
                            <?php else: ?>
                            <button type="button" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center text-danger" data-bs-toggle="modal" data-bs-target="#deactivateCompanyModal">
                                <div>
                                    <i class="fas fa-ban me-2"></i>
                                    <?= __('تعطيل الشركة') ?>
                                </div>
                                <i class="fas fa-chevron-right text-muted"></i>
                            </button>
                            <?php endif; ?>

                            <button type="button" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center text-danger" data-bs-toggle="modal" data-bs-target="#deleteCompanyModal">
                                <div>
                                    <i class="fas fa-trash-alt me-2"></i>
                                    <?= __('حذف الشركة') ?>
                                </div>
                                <i class="fas fa-chevron-right text-muted"></i>
                            </button>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Modal حذف الشركة -->
            <?php if ($is_owner): ?>
                <div class="modal fade" id="deleteCompanyModal" tabindex="-1" aria-labelledby="deleteCompanyModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header bg-danger text-white">
                                <h5 class="modal-title" id="deleteCompanyModalLabel">
                                    <i class="fas fa-exclamation-triangle me-2"></i> <?= __('حذف الشركة') ?>
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="text-center mb-4">
                                    <div class="avatar-icon avatar-icon-lg bg-danger-soft text-danger mb-3">
                                        <i class="fas fa-trash-alt fa-2x"></i>
                                    </div>
                                    <h4><?= __('هل أنت متأكد من رغبتك في حذف الشركة؟') ?></h4>
                                    <p class="text-muted"><?= e($company['CompanyName']) ?></p>
                                </div>

                                <div class="alert alert-warning">
                                    <div class="d-flex">
                                        <div class="me-3">
                                            <i class="fas fa-exclamation-circle fa-2x"></i>
                                        </div>
                                        <div>
                                            <h5 class="alert-heading"><?= __('تحذير') ?></h5>
                                            <p class="mb-0"><?= __('هذا الإجراء لا يمكن التراجع عنه. سيتم حذف جميع البيانات المرتبطة بالشركة بشكل نهائي.') ?></p>
                                        </div>
                                    </div>
                                </div>

                                <form method="POST" action="<?= base_url('companies/' . $company['CompanyID'] . '/delete') ?>" id="deleteCompanyForm">
                                    <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" name="confirm_delete" id="confirm_delete" required>
                                        <label class="form-check-label" for="confirm_delete">
                                            <?= __('نعم، أنا متأكد من رغبتي في حذف هذه الشركة وجميع بياناتها') ?>
                                        </label>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                    <i class="fas fa-times me-1"></i> <?= __('إلغاء') ?>
                                </button>
                                <button type="submit" form="deleteCompanyForm" class="btn btn-danger">
                                    <i class="fas fa-trash-alt me-1"></i> <?= __('حذف الشركة') ?>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modal تعطيل الشركة -->
                <?php if ($company['CompanyStatus'] !== 'Inactive'): ?>
                <div class="modal fade" id="deactivateCompanyModal" tabindex="-1" aria-labelledby="deactivateCompanyModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header bg-danger text-white">
                                <h5 class="modal-title" id="deactivateCompanyModalLabel">
                                    <i class="fas fa-exclamation-triangle me-2"></i> <?= __('تأكيد تعطيل الشركة') ?>
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="text-center mb-4">
                                    <div class="avatar-icon avatar-icon-lg bg-danger-soft text-danger mb-3">
                                        <i class="fas fa-ban fa-2x"></i>
                                    </div>
                                    <h4><?= __('هل أنت متأكد من رغبتك في تعطيل الشركة؟') ?></h4>
                                    <p class="text-muted"><?= e($company['CompanyName']) ?></p>
                                </div>

                                <div class="alert alert-warning">
                                    <div class="d-flex">
                                        <div class="me-3">
                                            <i class="fas fa-exclamation-circle fa-2x"></i>
                                        </div>
                                        <div>
                                            <h5 class="alert-heading"><?= __('تنبيه هام') ?></h5>
                                            <p class="mb-0"><?= __('سيتم منع جميع المستخدمين من الوصول إلى الشركة حتى يتم تفعيلها مرة أخرى.') ?></p>
                                        </div>
                                    </div>
                                </div>

                                <form action="<?= base_url('companies/' . $company['CompanyID'] . '/deactivate') ?>" method="post" id="deactivateCompanyForm">
                                    <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">

                                    <div class="mb-3">
                                        <label for="deactivation_reason" class="form-label"><?= __('سبب التعطيل (اختياري)') ?></label>
                                        <textarea class="form-control" id="deactivation_reason" name="deactivation_reason" rows="3" placeholder="<?= __('يرجى ذكر سبب تعطيل الشركة...') ?>"></textarea>
                                    </div>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="confirm_deactivation" name="confirm_deactivation" required>
                                        <label class="form-check-label" for="confirm_deactivation">
                                            <?= __('أؤكد رغبتي في تعطيل الشركة') ?>
                                        </label>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                    <i class="fas fa-times me-1"></i> <?= __('إلغاء') ?>
                                </button>
                                <button type="button" class="btn btn-danger" id="confirmDeactivateBtn">
                                    <i class="fas fa-ban me-1"></i> <?= __('تعطيل الشركة') ?>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Modal تفعيل الشركة -->
                <?php if ($company['CompanyStatus'] === 'Inactive'): ?>
                <div class="modal fade" id="activateCompanyModal" tabindex="-1" aria-labelledby="activateCompanyModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header bg-success text-white">
                                <h5 class="modal-title" id="activateCompanyModalLabel">
                                    <i class="fas fa-check-circle me-2"></i> <?= __('تأكيد تفعيل الشركة') ?>
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="text-center mb-4">
                                    <div class="avatar-icon avatar-icon-lg bg-success-soft text-success mb-3">
                                        <i class="fas fa-check-circle fa-2x"></i>
                                    </div>
                                    <h4><?= __('هل ترغب في تفعيل الشركة؟') ?></h4>
                                    <p class="text-muted"><?= e($company['CompanyName']) ?></p>
                                </div>

                                <div class="alert alert-info">
                                    <div class="d-flex">
                                        <div class="me-3">
                                            <i class="fas fa-info-circle fa-2x"></i>
                                        </div>
                                        <div>
                                            <h5 class="alert-heading"><?= __('معلومات') ?></h5>
                                            <p class="mb-0"><?= __('سيتمكن جميع المستخدمين من الوصول إلى الشركة مرة أخرى بعد التفعيل.') ?></p>
                                        </div>
                                    </div>
                                </div>

                                <form action="<?= base_url('companies/' . $company['CompanyID'] . '/activate') ?>" method="post" id="activateCompanyForm">
                                    <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                    <i class="fas fa-times me-1"></i> <?= __('إلغاء') ?>
                                </button>
                                <button type="submit" form="activateCompanyForm" class="btn btn-success">
                                    <i class="fas fa-check-circle me-1"></i> <?= __('تفعيل الشركة') ?>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
/* تنسيق أيقونات النوافذ المنبثقة */
.avatar-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    border-radius: 50%;
}

.avatar-icon-lg {
    width: 100px;
    height: 100px;
}

.bg-danger-soft {
    background-color: rgba(220, 53, 69, 0.1);
}

.bg-success-soft {
    background-color: rgba(40, 167, 69, 0.1);
}

/* تحسين مظهر بطاقة الاشتراك */
.card-header.bg-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

.card-header.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
}

/* تحسين مظهر الشارات */
.badge {
    font-size: 0.75em;
    padding: 0.375rem 0.75rem;
}

/* تحسين مظهر الأزرار */
.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
}

/* تحسين مظهر النصوص */
.text-primary {
    color: #007bff !important;
}

/* تحسين مظهر التنبيهات */
.alert {
    border: none;
    border-radius: 0.5rem;
}

.alert-info {
    background-color: #e7f3ff;
    color: #0c5460;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

/* تحسين مظهر شريط التقدم */
.progress {
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.1);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    border-radius: 10px;
    transition: width 0.6s ease;
    position: relative;
    overflow: hidden;
}

.progress-bar.bg-info {
    background: linear-gradient(45deg, #17a2b8, #20c997);
}

.progress-bar.bg-warning {
    background: linear-gradient(45deg, #ffc107, #fd7e14);
}

.progress-bar.bg-danger {
    background: linear-gradient(45deg, #dc3545, #e74c3c);
}

.progress-bar.bg-success {
    background: linear-gradient(45deg, #28a745, #20c997);
}

/* تحسين مظهر بطاقة الفترة التجريبية */
.card-header.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%) !important;
}

/* تحسين مظهر الشارات */
.badge.bg-info {
    background: linear-gradient(45deg, #17a2b8, #20c997) !important;
}

.badge.bg-warning {
    background: linear-gradient(45deg, #ffc107, #fd7e14) !important;
}

.badge.bg-danger {
    background: linear-gradient(45deg, #dc3545, #e74c3c) !important;
}

/* تحسين مظهر التنبيهات الصغيرة */
.alert.p-2 {
    padding: 0.5rem !important;
    border-radius: 0.375rem;
    border: none;
}

/* تحسين مظهر النصوص */
.fw-bold {
    font-weight: 600 !important;
}

/* تحسين مظهر الأيقونات */
.fas.fa-gift {
    color: #28a745;
}

.fas.fa-hourglass-half {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* تحسين مظهر التواريخ */
.small.text-muted {
    font-size: 0.8rem;
    opacity: 0.8;
}

/* تحسين مظهر أزرار التجديد التلقائي */
.btn-outline-success:disabled,
.btn-outline-danger:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    border-color: #dee2e6;
    color: #6c757d;
}

.btn-outline-success:disabled:hover,
.btn-outline-danger:disabled:hover {
    background-color: transparent;
    border-color: #dee2e6;
    color: #6c757d;
}

/* تحسين مظهر النماذج المضمنة */
.d-inline form {
    display: inline-block;
}

/* تحسين مظهر الأزرار الصغيرة */
.btn-sm {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

/* تحسين مظهر الشارات مع الأزرار */
.badge.me-2 {
    margin-right: 0.5rem !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تأكيد تعطيل الشركة
    const confirmDeactivateBtn = document.getElementById('confirmDeactivateBtn');
    if (confirmDeactivateBtn) {
        confirmDeactivateBtn.addEventListener('click', function() {
            const confirmCheckbox = document.getElementById('confirm_deactivation');
            if (confirmCheckbox.checked) {
                document.getElementById('deactivateCompanyForm').submit();
            } else {
                alert('يرجى تأكيد رغبتك في تعطيل الشركة');
            }
        });
    }
});
</script>