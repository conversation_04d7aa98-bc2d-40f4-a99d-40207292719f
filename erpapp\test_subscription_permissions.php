<?php
/**
 * اختبار نظام الاشتراكات والصلاحيات المتكامل
 * يتحقق من: حالة الشركة، الاشتراك، الوحدات المسموحة، الصلاحيات
 */

// تحميل النظام
require_once 'loader.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الاشتراكات والصلاحيات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-4">
    <h1 class="text-center mb-4">
        <i class="fas fa-shield-alt text-success"></i> اختبار نظام الاشتراكات والصلاحيات المتكامل
    </h1>

    <?php 
    $user = current_user();
    if (!$user): 
    ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle"></i> يجب تسجيل الدخول أولاً
        <br><a href="login.php" class="btn btn-primary mt-2">تسجيل الدخول</a>
    </div>
    <?php 
    else:
        $companyId = $user['current_company_id'];
        if (!$companyId):
    ?>
    <div class="alert alert-warning">
        <i class="fas fa-building"></i> يجب اختيار شركة أولاً
    </div>
    <?php 
        else:
    ?>

    <!-- معلومات الاشتراك -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5><i class="fas fa-crown"></i> معلومات الاشتراك</h5>
        </div>
        <div class="card-body">
            <?php
            $subscriptionInfo = getCompanySubscriptionInfo($companyId);
            $isSubscriptionValid = isCompanySubscriptionValid($companyId);
            ?>
            <div class="row">
                <div class="col-md-6">
                    <h6>حالة الشركة:</h6>
                    <span class="badge bg-<?= 
                        $subscriptionInfo['CompanyStatus'] === 'Active' ? 'success' : 
                        ($subscriptionInfo['CompanyStatus'] === 'Trial' ? 'warning' : 'danger') 
                    ?> fs-6">
                        <?= htmlspecialchars($subscriptionInfo['CompanyStatus'] ?? 'غير محدد') ?>
                    </span>
                    
                    <h6 class="mt-3">صحة الاشتراك:</h6>
                    <span class="badge bg-<?= $isSubscriptionValid ? 'success' : 'danger' ?> fs-6">
                        <?= $isSubscriptionValid ? 'صالح' : 'غير صالح' ?>
                    </span>
                </div>
                <div class="col-md-6">
                    <?php if ($subscriptionInfo): ?>
                    <h6>تفاصيل الاشتراك:</h6>
                    <ul class="list-group list-group-flush">
                        <?php if ($subscriptionInfo['CompanyStatus'] === 'Trial'): ?>
                        <li class="list-group-item">
                            <strong>نهاية الفترة التجريبية:</strong> 
                            <?= $subscriptionInfo['trial_end_date'] ? date('Y-m-d H:i', strtotime($subscriptionInfo['trial_end_date'])) : 'غير محدد' ?>
                        </li>
                        <?php else: ?>
                        <li class="list-group-item">
                            <strong>خطة الاشتراك:</strong> 
                            <?= htmlspecialchars($subscriptionInfo['plan_name_ar'] ?? 'غير محدد') ?>
                        </li>
                        <li class="list-group-item">
                            <strong>حالة الاشتراك:</strong> 
                            <?= htmlspecialchars($subscriptionInfo['subscription_status'] ?? 'غير محدد') ?>
                        </li>
                        <li class="list-group-item">
                            <strong>بداية الاشتراك:</strong> 
                            <?= $subscriptionInfo['subscription_start'] ? date('Y-m-d', strtotime($subscriptionInfo['subscription_start'])) : 'غير محدد' ?>
                        </li>
                        <li class="list-group-item">
                            <strong>نهاية الاشتراك:</strong> 
                            <?= $subscriptionInfo['subscription_end'] ? date('Y-m-d', strtotime($subscriptionInfo['subscription_end'])) : 'غير محدد' ?>
                        </li>
                        <?php endif; ?>
                    </ul>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- اختبار الوحدات -->
    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5><i class="fas fa-cubes"></i> اختبار الوحدات والاشتراك</h5>
        </div>
        <div class="card-body">
            <?php
            $testModules = [
                'inventory' => 'المخزون',
                'purchases' => 'المشتريات', 
                'sales' => 'المبيعات',
                'accounting' => 'المحاسبة',
                'hr' => 'الموارد البشرية',
                'reports' => 'التقارير'
            ];
            ?>
            <div class="row">
                <?php foreach ($testModules as $moduleCode => $moduleName): ?>
                <div class="col-md-4 mb-3">
                    <div class="card h-100">
                        <div class="card-header">
                            <h6 class="mb-0"><?= $moduleName ?> (<?= $moduleCode ?>)</h6>
                        </div>
                        <div class="card-body">
                            <?php
                            $isAllowedInSubscription = isModuleAllowedInSubscription($moduleCode, $companyId);
                            $isInstalled = false;
                            
                            // التحقق من التنزيل
                            global $db;
                            $stmt = $db->prepare("
                                SELECT COUNT(*) as count
                                FROM company_modules cm
                                JOIN system_modules sm ON sm.module_id = cm.module_id
                                WHERE cm.company_id = ? AND sm.module_code = ?
                            ");
                            $stmt->execute([$companyId, $moduleCode]);
                            $result = $stmt->fetch();
                            $isInstalled = $result['count'] > 0;
                            ?>
                            
                            <div class="mb-2">
                                <strong>مسموح في الاشتراك:</strong>
                                <span class="badge bg-<?= $isAllowedInSubscription ? 'success' : 'danger' ?>">
                                    <?= $isAllowedInSubscription ? 'نعم' : 'لا' ?>
                                </span>
                            </div>
                            
                            <div class="mb-2">
                                <strong>منزل للشركة:</strong>
                                <span class="badge bg-<?= $isInstalled ? 'success' : 'secondary' ?>">
                                    <?= $isInstalled ? 'نعم' : 'لا' ?>
                                </span>
                            </div>
                            
                            <div class="mb-2">
                                <strong>متاح للاستخدام:</strong>
                                <?php $canAccess = canView($moduleCode . '_management'); ?>
                                <span class="badge bg-<?= $canAccess ? 'success' : 'danger' ?>">
                                    <?= $canAccess ? 'نعم' : 'لا' ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- اختبار صلاحيات المنتجات -->
    <div class="card mb-4">
        <div class="card-header bg-warning text-dark">
            <h5><i class="fas fa-box"></i> اختبار صلاحيات المنتجات</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>الفحوصات المتدرجة:</h6>
                    <ol class="list-group list-group-numbered">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            صحة الاشتراك
                            <span class="badge bg-<?= $isSubscriptionValid ? 'success' : 'danger' ?>">
                                <?= $isSubscriptionValid ? 'نجح' : 'فشل' ?>
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            وحدة المخزون مسموحة
                            <span class="badge bg-<?= isModuleAllowedInSubscription('inventory', $companyId) ? 'success' : 'danger' ?>">
                                <?= isModuleAllowedInSubscription('inventory', $companyId) ? 'نجح' : 'فشل' ?>
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            وحدة المخزون منزلة
                            <?php
                            $stmt = $db->prepare("
                                SELECT COUNT(*) as count
                                FROM company_modules cm
                                JOIN system_modules sm ON sm.module_id = cm.module_id
                                WHERE cm.company_id = ? AND sm.module_code = 'inventory'
                            ");
                            $stmt->execute([$companyId]);
                            $inventoryInstalled = $stmt->fetch()['count'] > 0;
                            ?>
                            <span class="badge bg-<?= $inventoryInstalled ? 'success' : 'danger' ?>">
                                <?= $inventoryInstalled ? 'نجح' : 'فشل' ?>
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            صلاحية عرض المنتجات
                            <span class="badge bg-<?= canView('products') ? 'success' : 'danger' ?>">
                                <?= canView('products') ? 'نجح' : 'فشل' ?>
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            صلاحية إنشاء المنتجات
                            <span class="badge bg-<?= canCreate('products') ? 'success' : 'danger' ?>">
                                <?= canCreate('products') ? 'نجح' : 'فشل' ?>
                            </span>
                        </li>
                    </ol>
                </div>
                <div class="col-md-6">
                    <h6>النتيجة النهائية:</h6>
                    <div class="p-3 border rounded">
                        <?php if (canView('products')): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i>
                            <strong>يمكن الوصول لصفحة المنتجات</strong>
                        </div>
                        
                        <!-- أزرار الاختبار -->
                        <div class="d-grid gap-2">
                            <a href="inventory/products" class="btn btn-primary" target="_blank">
                                <i class="fas fa-box"></i> فتح صفحة المنتجات
                            </a>
                            
                            <?php if (canCreate('products')): ?>
                            <a href="inventory/products/create" class="btn btn-success" target="_blank">
                                <i class="fas fa-plus"></i> فتح صفحة إنشاء منتج
                            </a>
                            <?php else: ?>
                            <button class="btn btn-secondary" disabled>
                                <i class="fas fa-plus"></i> إنشاء منتج (غير مسموح)
                            </button>
                            <?php endif; ?>
                        </div>
                        
                        <?php else: ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-times-circle"></i>
                            <strong>لا يمكن الوصول لصفحة المنتجات</strong>
                            <br><small>السبب: <?= 
                                !$isSubscriptionValid ? 'اشتراك غير صالح' : 
                                (!isModuleAllowedInSubscription('inventory', $companyId) ? 'وحدة غير مسموحة' : 
                                (!$inventoryInstalled ? 'وحدة غير منزلة' : 'لا توجد صلاحيات'))
                            ?></small>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات تقنية -->
    <div class="card mb-4">
        <div class="card-header bg-dark text-white">
            <h5><i class="fas fa-code"></i> معلومات تقنية</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>الدوال المتاحة:</h6>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <code>isCompanySubscriptionValid()</code>
                            <span class="badge bg-<?= function_exists('isCompanySubscriptionValid') ? 'success' : 'danger' ?>">
                                <?= function_exists('isCompanySubscriptionValid') ? 'متاح' : 'غير متاح' ?>
                            </span>
                        </li>
                        <li class="list-group-item">
                            <code>isModuleAllowedInSubscription()</code>
                            <span class="badge bg-<?= function_exists('isModuleAllowedInSubscription') ? 'success' : 'danger' ?>">
                                <?= function_exists('isModuleAllowedInSubscription') ? 'متاح' : 'غير متاح' ?>
                            </span>
                        </li>
                        <li class="list-group-item">
                            <code>getCompanySubscriptionInfo()</code>
                            <span class="badge bg-<?= function_exists('getCompanySubscriptionInfo') ? 'success' : 'danger' ?>">
                                <?= function_exists('getCompanySubscriptionInfo') ? 'متاح' : 'غير متاح' ?>
                            </span>
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>معلومات المستخدم:</h6>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <strong>معرف المستخدم:</strong> <?= $user['UserID'] ?>
                        </li>
                        <li class="list-group-item">
                            <strong>معرف الشركة:</strong> <?= $companyId ?>
                        </li>
                        <li class="list-group-item">
                            <strong>مالك الشركة:</strong> 
                            <span class="badge bg-<?= isCompanyOwner() ? 'success' : 'secondary' ?>">
                                <?= isCompanyOwner() ? 'نعم' : 'لا' ?>
                            </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <?php endif; ?>
    <?php endif; ?>
</div>

</body>
</html>
