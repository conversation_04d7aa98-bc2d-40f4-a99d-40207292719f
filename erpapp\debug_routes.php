<?php
/**
 * Debug Routes - للتحقق من المسارات المسجلة
 */

// تحميل النظام
require_once 'erpapp/loader.php';

echo "<h1>Debug Routes</h1>";

// الحصول على ModuleRouter
$moduleRouter = \App\Core\ModuleRouter::getInstance();
$routes = $moduleRouter->getRoutes();

echo "<h2>Registered Routes (" . count($routes) . " routes):</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Methods</th><th>Route</th><th>Controller</th><th>Action</th></tr>";

foreach ($routes as $route) {
    $methods = implode(', ', $route['methods']);
    $routePattern = $route['route'];
    $controller = $route['handler']['controller'] ?? 'N/A';
    $action = $route['handler']['action'] ?? 'N/A';
    
    echo "<tr>";
    echo "<td>$methods</td>";
    echo "<td>$routePattern</td>";
    echo "<td>$controller</td>";
    echo "<td>$action</td>";
    echo "</tr>";
}

echo "</table>";

// فحص مسار محدد
echo "<h2>Test Inventory Route:</h2>";
$testUrl = '/inventory';
$testMethod = 'GET';

if ($moduleRouter->match($testUrl, $testMethod)) {
    echo "<p style='color: green;'>✅ Route '/inventory' matches!</p>";
    $params = $moduleRouter->getParams();
    echo "<pre>" . print_r($params, true) . "</pre>";
} else {
    echo "<p style='color: red;'>❌ Route '/inventory' does not match!</p>";
}

// فحص الوحدات المحملة
echo "<h2>Loaded Classes:</h2>";
$classes = get_declared_classes();
$moduleClasses = array_filter($classes, function($class) {
    return strpos($class, 'Modules\\') === 0;
});

echo "<ul>";
foreach ($moduleClasses as $class) {
    echo "<li>$class</li>";
}
echo "</ul>";

// فحص ملفات الوحدات
echo "<h2>Module Files Check:</h2>";
$inventoryModulePath = BASE_PATH . '/Modules/Accounting/Inventory/Module.php';
echo "<p>Inventory Module File: " . ($inventoryModulePath) . "</p>";
echo "<p>File exists: " . (file_exists($inventoryModulePath) ? 'Yes' : 'No') . "</p>";

if (file_exists($inventoryModulePath)) {
    echo "<p>File size: " . filesize($inventoryModulePath) . " bytes</p>";
}

// فحص autoloader
echo "<h2>Autoloader Test:</h2>";
$testClass = 'Modules\\Accounting\\Inventory\\Module';
echo "<p>Testing class: $testClass</p>";
echo "<p>Class exists: " . (class_exists($testClass) ? 'Yes' : 'No') . "</p>";

if (class_exists($testClass)) {
    $reflection = new ReflectionClass($testClass);
    echo "<p>File: " . $reflection->getFileName() . "</p>";
}
?>
