-- ===================================
-- بيانات أساسية لوحدة المخزون (طريقة يدوية)
-- ===================================

-- تعليمات الاستخدام:
-- 1. استبدل [COMPANY_ID] بمعرف الشركة الصحيح
-- 2. استبدل [USER_ID] بمعرف المستخدم الصحيح
-- 3. قم بتشغيل هذا الملف بعد إنشاء جداول المخزون

-- للحصول على الشركات والمستخدمين المتاحين:
-- SELECT CompanyID, CompanyName FROM companies WHERE CompanyStatus = 'Active';
-- SELECT UserID, UserName FROM users WHERE AccountStatus = 'Active';

-- ===================================
-- إدراج وحدات القياس الأساسية
-- ===================================

INSERT INTO `inventory_units` (`company_id`, `unit_code`, `unit_name_ar`, `unit_name_en`, `unit_symbol_ar`, `unit_symbol_en`, `is_base_unit`, `created_by`) VALUES
([COMPANY_ID], 'PIECE', 'قطعة', 'Piece', 'قطعة', 'pcs', 1, [USER_ID]),
([COMPANY_ID], 'KG', 'كيلوجرام', 'Kilogram', 'كجم', 'kg', 1, [USER_ID]),
([COMPANY_ID], 'GRAM', 'جرام', 'Gram', 'جم', 'g', 0, [USER_ID]),
([COMPANY_ID], 'LITER', 'لتر', 'Liter', 'لتر', 'L', 1, [USER_ID]),
([COMPANY_ID], 'ML', 'مليلتر', 'Milliliter', 'مل', 'ml', 0, [USER_ID]),
([COMPANY_ID], 'METER', 'متر', 'Meter', 'م', 'm', 1, [USER_ID]),
([COMPANY_ID], 'CM', 'سنتيمتر', 'Centimeter', 'سم', 'cm', 0, [USER_ID]),
([COMPANY_ID], 'BOX', 'صندوق', 'Box', 'صندوق', 'box', 0, [USER_ID]),
([COMPANY_ID], 'CARTON', 'كرتون', 'Carton', 'كرتون', 'carton', 0, [USER_ID]),
([COMPANY_ID], 'PACK', 'عبوة', 'Pack', 'عبوة', 'pack', 0, [USER_ID]),
([COMPANY_ID], 'DOZEN', 'دزينة', 'Dozen', 'دزينة', 'dz', 0, [USER_ID]),
([COMPANY_ID], 'SET', 'طقم', 'Set', 'طقم', 'set', 0, [USER_ID]);

-- ===================================
-- إدراج فئات المنتجات الأساسية
-- ===================================

INSERT INTO `inventory_categories` (`company_id`, `category_code`, `category_name_ar`, `category_name_en`, `display_order`, `created_by`) VALUES
([COMPANY_ID], 'ELECTRONICS', 'إلكترونيات', 'Electronics', 1, [USER_ID]),
([COMPANY_ID], 'COMPUTERS', 'حاسوب وملحقاته', 'Computers & Accessories', 2, [USER_ID]),
([COMPANY_ID], 'MOBILE', 'هواتف ذكية', 'Mobile Phones', 3, [USER_ID]),
([COMPANY_ID], 'FURNITURE', 'أثاث', 'Furniture', 4, [USER_ID]),
([COMPANY_ID], 'OFFICE_FURNITURE', 'أثاث مكتبي', 'Office Furniture', 5, [USER_ID]),
([COMPANY_ID], 'OFFICE_SUPPLIES', 'مستلزمات مكتبية', 'Office Supplies', 6, [USER_ID]),
([COMPANY_ID], 'STATIONERY', 'قرطاسية', 'Stationery', 7, [USER_ID]),
([COMPANY_ID], 'BOOKS', 'كتب', 'Books', 8, [USER_ID]),
([COMPANY_ID], 'FOOD_BEVERAGE', 'أطعمة ومشروبات', 'Food & Beverage', 9, [USER_ID]),
([COMPANY_ID], 'CLEANING', 'مواد تنظيف', 'Cleaning Supplies', 10, [USER_ID]),
([COMPANY_ID], 'TOOLS', 'أدوات', 'Tools', 11, [USER_ID]),
([COMPANY_ID], 'AUTOMOTIVE', 'قطع غيار سيارات', 'Automotive Parts', 12, [USER_ID]),
([COMPANY_ID], 'CLOTHING', 'ملابس', 'Clothing', 13, [USER_ID]),
([COMPANY_ID], 'MEDICAL', 'مستلزمات طبية', 'Medical Supplies', 14, [USER_ID]),
([COMPANY_ID], 'SPORTS', 'مستلزمات رياضية', 'Sports Equipment', 15, [USER_ID]);

-- ===================================
-- إدراج المخازن الأساسية
-- ===================================

INSERT INTO `inventory_warehouses` (`company_id`, `warehouse_code`, `warehouse_name_ar`, `warehouse_name_en`, `address`, `warehouse_type`, `capacity`, `created_by`) VALUES
([COMPANY_ID], 'MAIN_WH', 'المخزن الرئيسي', 'Main Warehouse', 'الرياض، المملكة العربية السعودية', 'main', 10000.00, [USER_ID]),
([COMPANY_ID], 'BRANCH_RUH', 'مخزن فرع الرياض', 'Riyadh Branch Warehouse', 'الرياض، حي العليا', 'branch', 5000.00, [USER_ID]),
([COMPANY_ID], 'BRANCH_JED', 'مخزن فرع جدة', 'Jeddah Branch Warehouse', 'جدة، حي الروضة', 'branch', 3000.00, [USER_ID]),
([COMPANY_ID], 'BRANCH_DAM', 'مخزن فرع الدمام', 'Dammam Branch Warehouse', 'الدمام، حي الفيصلية', 'branch', 2000.00, [USER_ID]),
([COMPANY_ID], 'VIRTUAL_WH', 'المخزن الافتراضي', 'Virtual Warehouse', 'للمنتجات الرقمية والخدمات', 'virtual', NULL, [USER_ID]),
([COMPANY_ID], 'EXTERNAL_WH', 'المخزن الخارجي', 'External Warehouse', 'مخزن خارجي للتخزين المؤقت', 'external', 1000.00, [USER_ID]);

-- ===================================
-- إدراج منتجات تجريبية
-- ===================================

-- منتجات إلكترونية
INSERT INTO `inventory_products` (`company_id`, `product_code`, `barcode`, `product_name_ar`, `product_name_en`, `category_id`, `unit_id`, `cost_price`, `selling_price`, `min_stock_level`, `reorder_point`, `created_by`) VALUES
([COMPANY_ID], 'LAPTOP001', '1234567890123', 'لابتوب ديل انسبايرون', 'Dell Inspiron Laptop', (SELECT category_id FROM inventory_categories WHERE category_code = 'COMPUTERS' AND company_id = [COMPANY_ID] LIMIT 1), (SELECT unit_id FROM inventory_units WHERE unit_code = 'PIECE' AND company_id = [COMPANY_ID] LIMIT 1), 2500.00, 3200.00, 5, 10, [USER_ID]),
([COMPANY_ID], 'MOUSE001', '1234567890124', 'فأرة لاسلكية', 'Wireless Mouse', (SELECT category_id FROM inventory_categories WHERE category_code = 'COMPUTERS' AND company_id = [COMPANY_ID] LIMIT 1), (SELECT unit_id FROM inventory_units WHERE unit_code = 'PIECE' AND company_id = [COMPANY_ID] LIMIT 1), 25.00, 45.00, 20, 30, [USER_ID]),
([COMPANY_ID], 'KEYBOARD001', '1234567890125', 'لوحة مفاتيح عربية', 'Arabic Keyboard', (SELECT category_id FROM inventory_categories WHERE category_code = 'COMPUTERS' AND company_id = [COMPANY_ID] LIMIT 1), (SELECT unit_id FROM inventory_units WHERE unit_code = 'PIECE' AND company_id = [COMPANY_ID] LIMIT 1), 35.00, 60.00, 15, 25, [USER_ID]);

-- أثاث مكتبي
INSERT INTO `inventory_products` (`company_id`, `product_code`, `barcode`, `product_name_ar`, `product_name_en`, `category_id`, `unit_id`, `cost_price`, `selling_price`, `min_stock_level`, `reorder_point`, `created_by`) VALUES
([COMPANY_ID], 'CHAIR001', '1234567890126', 'كرسي مكتب جلد', 'Leather Office Chair', (SELECT category_id FROM inventory_categories WHERE category_code = 'OFFICE_FURNITURE' AND company_id = [COMPANY_ID] LIMIT 1), (SELECT unit_id FROM inventory_units WHERE unit_code = 'PIECE' AND company_id = [COMPANY_ID] LIMIT 1), 180.00, 280.00, 8, 15, [USER_ID]),
([COMPANY_ID], 'DESK001', '1234567890127', 'مكتب خشبي', 'Wooden Desk', (SELECT category_id FROM inventory_categories WHERE category_code = 'OFFICE_FURNITURE' AND company_id = [COMPANY_ID] LIMIT 1), (SELECT unit_id FROM inventory_units WHERE unit_code = 'PIECE' AND company_id = [COMPANY_ID] LIMIT 1), 350.00, 550.00, 5, 10, [USER_ID]);

-- قرطاسية
INSERT INTO `inventory_products` (`company_id`, `product_code`, `barcode`, `product_name_ar`, `product_name_en`, `category_id`, `unit_id`, `cost_price`, `selling_price`, `min_stock_level`, `reorder_point`, `created_by`) VALUES
([COMPANY_ID], 'PEN001', '1234567890128', 'قلم حبر أزرق', 'Blue Ink Pen', (SELECT category_id FROM inventory_categories WHERE category_code = 'STATIONERY' AND company_id = [COMPANY_ID] LIMIT 1), (SELECT unit_id FROM inventory_units WHERE unit_code = 'PIECE' AND company_id = [COMPANY_ID] LIMIT 1), 1.50, 3.00, 100, 200, [USER_ID]),
([COMPANY_ID], 'PAPER001', '1234567890129', 'ورق A4', 'A4 Paper', (SELECT category_id FROM inventory_categories WHERE category_code = 'STATIONERY' AND company_id = [COMPANY_ID] LIMIT 1), (SELECT unit_id FROM inventory_units WHERE unit_code = 'PACK' AND company_id = [COMPANY_ID] LIMIT 1), 15.00, 25.00, 50, 100, [USER_ID]);

-- ===================================
-- ملاحظة: لإدراج أرصدة أولية، استخدم الاستعلامات التالية بعد استبدال القيم:
-- ===================================

/*
INSERT INTO `inventory_stock` (`company_id`, `product_id`, `warehouse_id`, `quantity_on_hand`, `quantity_available`, `average_cost`, `created_by`) VALUES
([COMPANY_ID], (SELECT product_id FROM inventory_products WHERE product_code = 'LAPTOP001' AND company_id = [COMPANY_ID]), (SELECT warehouse_id FROM inventory_warehouses WHERE warehouse_code = 'MAIN_WH' AND company_id = [COMPANY_ID]), 15.000, 15.000, 2500.00, [USER_ID]),
([COMPANY_ID], (SELECT product_id FROM inventory_products WHERE product_code = 'CHAIR001' AND company_id = [COMPANY_ID]), (SELECT warehouse_id FROM inventory_warehouses WHERE warehouse_code = 'MAIN_WH' AND company_id = [COMPANY_ID]), 30.000, 30.000, 180.00, [USER_ID]),
([COMPANY_ID], (SELECT product_id FROM inventory_products WHERE product_code = 'PEN001' AND company_id = [COMPANY_ID]), (SELECT warehouse_id FROM inventory_warehouses WHERE warehouse_code = 'MAIN_WH' AND company_id = [COMPANY_ID]), 1000.000, 1000.000, 1.50, [USER_ID]);
*/
