-- إدخال البيانات الافتراضية المبسطة
-- مالك الشركة: المستخدم 32
-- الشركة: 4
-- المستخدم المضاف: 35

-- =====================================================
-- 1. إدخال الوحدات المتاحة في النظام
-- =====================================================
INSERT INTO `system_modules` (`program_code`, `module_type`, `name_ar`, `name_en`, `description_ar`, `description_en`, `base_url`, `icon_name`, `is_active`, `version`, `display_order`) VALUES
('inventory_module', 'addon', 'وحدة إدارة المخزون', 'Inventory Management Module', 'إدارة شاملة للمنتجات والمستودعات وحركة المخزون', 'Complete management of products, warehouses and inventory movements', 'inventory', 'fas fa-boxes', 1, '1.0.0', 1),
('purchases_module', 'addon', 'وحدة إدارة المشتريات', 'Purchases Management Module', 'إدارة طلبات الشراء والموردين وفواتير الشراء', 'Management of purchase orders, suppliers and purchase invoices', 'purchases', 'fas fa-shopping-cart', 1, '1.0.0', 2),
('sales_module', 'addon', 'وحدة إدارة المبيعات', 'Sales Management Module', 'إدارة العملاء وعروض الأسعار وفواتير المبيعات', 'Management of customers, quotations and sales invoices', 'sales', 'fas fa-chart-line', 1, '1.0.0', 3),
('accounting_module', 'addon', 'وحدة المحاسبة', 'Accounting Module', 'النظام المحاسبي الكامل مع دليل الحسابات والقيود', 'Complete accounting system with chart of accounts and journal entries', 'accounting', 'fas fa-calculator', 1, '1.0.0', 4),
('reports_module', 'core', 'وحدة التقارير', 'Reports Module', 'تقارير شاملة لجميع أقسام النظام', 'Comprehensive reports for all system modules', 'reports', 'fas fa-chart-bar', 1, '1.0.0', 5);

-- =====================================================
-- 2. تنزيل الوحدات للشركة رقم 4
-- =====================================================
INSERT INTO `company_installed_programs` (`company_id`, `program_id`, `installation_date`, `installed_by`, `is_active`) VALUES
(4, 1, NOW(), 32, 1), -- وحدة المخزون
(4, 2, NOW(), 32, 1), -- وحدة المشتريات  
(4, 3, NOW(), 32, 1), -- وحدة المبيعات
(4, 4, NOW(), 32, 1), -- وحدة المحاسبة
(4, 5, NOW(), 32, 1); -- وحدة التقارير

-- =====================================================
-- 3. إضافة البرامج الفرعية للشركة 4
-- =====================================================

-- البرامج الرئيسية والفرعية لوحدة المخزون
INSERT INTO `company_all_programs` (`company_id`, `parent_program_id`, `name_ar`, `name_en`, `status_en`, `status_ar`, `display_order`, `program_type`, `page_url`, `icon_name`, `category`) VALUES
-- البرنامج الرئيسي للمخزون
(4, NULL, 'إدارة المخزون', 'inventory_management', 'Active', 'نشط', 1, 'Main', 'inventory', 'fas fa-boxes', 'Programs'),
-- البرامج الفرعية للمخزون
(4, 1, 'المنتجات', 'inventory_products', 'Active', 'نشط', 11, 'Sub', 'inventory/products', 'fas fa-box', 'Programs'),
(4, 1, 'الفئات', 'inventory_categories', 'Active', 'نشط', 12, 'Sub', 'inventory/categories', 'fas fa-tags', 'Programs'),
(4, 1, 'وحدات القياس', 'inventory_units', 'Active', 'نشط', 13, 'Sub', 'inventory/units', 'fas fa-ruler', 'Programs'),
(4, 1, 'المستودعات', 'inventory_warehouses', 'Active', 'نشط', 14, 'Sub', 'inventory/warehouses', 'fas fa-warehouse', 'Programs'),

-- البرامج الرئيسية والفرعية لوحدة المشتريات
(4, NULL, 'إدارة المشتريات', 'purchases_management', 'Active', 'نشط', 2, 'Main', 'purchases', 'fas fa-shopping-cart', 'Programs'),
(4, 6, 'طلبات الشراء', 'purchase_orders', 'Active', 'نشط', 21, 'Sub', 'purchases/orders', 'fas fa-file-alt', 'Programs'),
(4, 6, 'الموردين', 'suppliers', 'Active', 'نشط', 22, 'Sub', 'purchases/suppliers', 'fas fa-truck', 'Programs'),
(4, 6, 'فواتير الشراء', 'purchase_invoices', 'Active', 'نشط', 23, 'Sub', 'purchases/invoices', 'fas fa-file-invoice', 'Programs'),

-- البرامج الرئيسية والفرعية لوحدة المبيعات
(4, NULL, 'إدارة المبيعات', 'sales_management', 'Active', 'نشط', 3, 'Main', 'sales', 'fas fa-chart-line', 'Programs'),
(4, 10, 'العملاء', 'customers', 'Active', 'نشط', 31, 'Sub', 'sales/customers', 'fas fa-users', 'Programs'),
(4, 10, 'عروض الأسعار', 'quotations', 'Active', 'نشط', 32, 'Sub', 'sales/quotations', 'fas fa-file-contract', 'Programs'),
(4, 10, 'فواتير المبيعات', 'sales_invoices', 'Active', 'نشط', 33, 'Sub', 'sales/invoices', 'fas fa-file-invoice-dollar', 'Programs'),

-- البرامج الرئيسية والفرعية لوحدة المحاسبة
(4, NULL, 'المحاسبة', 'accounting_management', 'Active', 'نشط', 4, 'Main', 'accounting', 'fas fa-calculator', 'Programs'),
(4, 14, 'دليل الحسابات', 'chart_of_accounts', 'Active', 'نشط', 41, 'Sub', 'accounting/accounts', 'fas fa-list', 'Programs'),
(4, 14, 'القيود اليومية', 'journal_entries', 'Active', 'نشط', 42, 'Sub', 'accounting/entries', 'fas fa-book', 'Programs'),
(4, 14, 'المدفوعات', 'payments', 'Active', 'نشط', 43, 'Sub', 'accounting/payments', 'fas fa-credit-card', 'Programs'),

-- البرامج الرئيسية والفرعية لوحدة التقارير
(4, NULL, 'التقارير', 'reports_management', 'Active', 'نشط', 5, 'Main', 'reports', 'fas fa-chart-bar', 'Programs'),
(4, 18, 'تقارير المخزون', 'inventory_reports', 'Active', 'نشط', 51, 'Sub', 'reports/inventory', 'fas fa-chart-line', 'Programs'),
(4, 18, 'التقارير المالية', 'financial_reports', 'Active', 'نشط', 52, 'Sub', 'reports/financial', 'fas fa-chart-pie', 'Programs'),
(4, 18, 'تقارير المبيعات', 'sales_reports', 'Active', 'نشط', 53, 'Sub', 'reports/sales', 'fas fa-chart-area', 'Programs');

-- =====================================================
-- 4. إضافة المناصب للشركة
-- =====================================================
INSERT INTO `positions` (`CompanyID`, `CreatedBy`, `PositionNameAR`, `PositionNameEN`, `Description`) VALUES
(4, 32, 'مدير عام', 'General Manager', 'صلاحيات كاملة على جميع الوحدات والبرامج'),
(4, 32, 'مدير المخزون', 'Inventory Manager', 'صلاحيات كاملة على وحدة المخزون'),
(4, 32, 'مدير المبيعات', 'Sales Manager', 'صلاحيات كاملة على وحدة المبيعات'),
(4, 32, 'محاسب', 'Accountant', 'صلاحيات على وحدة المحاسبة والتقارير المالية'),
(4, 32, 'موظف مخزون', 'Inventory Employee', 'صلاحيات محدودة على وحدة المخزون');

-- =====================================================
-- 5. إضافة المستخدم 35 في الشركة 4
-- =====================================================
INSERT INTO `company_users` (`user_id`, `company_id`, `added_by_user_id`, `status`, `status_ar`, `user_status`, `position_id`, `notes`) VALUES
(35, 4, 32, 'accepted', 'مقبول', 'active', 2, 'مدير المخزون - تم إضافته من قبل مالك الشركة');

-- =====================================================
-- 6. إضافة الصلاحيات للمناصب
-- =====================================================

-- صلاحيات مدير عام (منصب 1) - صلاحيات كاملة على جميع البرامج
INSERT INTO `permissions` (`PositionID`, `CompanyID`, `CreatedBy`, `ProgramID`, `CanView`, `CanCreate`, `CanEdit`, `CanDelete`, `CanApprove`) 
SELECT 1, 4, 32, program_id, 1, 1, 1, 1, 1 
FROM `company_all_programs` 
WHERE company_id = 4;

-- صلاحيات مدير المخزون (منصب 2) - صلاحيات كاملة على وحدة المخزون فقط
INSERT INTO `permissions` (`PositionID`, `CompanyID`, `CreatedBy`, `ProgramID`, `CanView`, `CanCreate`, `CanEdit`, `CanDelete`, `CanApprove`) 
SELECT 2, 4, 32, program_id, 1, 1, 1, 1, 0 
FROM `company_all_programs` 
WHERE company_id = 4 AND (name_en LIKE 'inventory%' OR parent_program_id = 1);

-- صلاحيات مدير المبيعات (منصب 3) - صلاحيات كاملة على وحدة المبيعات
INSERT INTO `permissions` (`PositionID`, `CompanyID`, `CreatedBy`, `ProgramID`, `CanView`, `CanCreate`, `CanEdit`, `CanDelete`, `CanApprove`) 
SELECT 3, 4, 32, program_id, 1, 1, 1, 1, 0 
FROM `company_all_programs` 
WHERE company_id = 4 AND (name_en LIKE 'sales%' OR parent_program_id = 10);

-- صلاحيات المحاسب (منصب 4) - صلاحيات على المحاسبة والتقارير
INSERT INTO `permissions` (`PositionID`, `CompanyID`, `CreatedBy`, `ProgramID`, `CanView`, `CanCreate`, `CanEdit`, `CanDelete`, `CanApprove`) 
SELECT 4, 4, 32, program_id, 1, 1, 1, 0, 0 
FROM `company_all_programs` 
WHERE company_id = 4 AND (name_en LIKE 'accounting%' OR name_en LIKE '%reports%' OR parent_program_id IN (14, 18));

-- صلاحيات موظف المخزون (منصب 5) - صلاحيات محدودة على المخزون
INSERT INTO `permissions` (`PositionID`, `CompanyID`, `CreatedBy`, `ProgramID`, `CanView`, `CanCreate`, `CanEdit`, `CanDelete`, `CanApprove`) 
SELECT 5, 4, 32, program_id, 1, 1, 0, 0, 0 
FROM `company_all_programs` 
WHERE company_id = 4 AND (name_en LIKE 'inventory%' OR parent_program_id = 1);

-- =====================================================
-- 7. تحديث الشركة الحالية للمستخدمين
-- =====================================================
UPDATE `users` SET `current_company_id` = 4 WHERE `UserID` IN (32, 35);

-- =====================================================
-- تم الانتهاء من إدخال البيانات الافتراضية المبسطة
-- =====================================================

-- للتحقق من البيانات المدخلة:
-- SELECT * FROM system_modules;
-- SELECT * FROM company_installed_programs WHERE company_id = 4;
-- SELECT * FROM company_all_programs WHERE company_id = 4 ORDER BY display_order;
-- SELECT * FROM positions WHERE CompanyID = 4;
-- SELECT * FROM company_users WHERE company_id = 4;
-- SELECT COUNT(*) as total_permissions FROM permissions WHERE CompanyID = 4;
