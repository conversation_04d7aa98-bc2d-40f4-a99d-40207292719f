<?php
/**
 * Inventory Dashboard - لوحة تحكم المخزون
 */

// تحديد البيانات الافتراضية إذا لم تكن موجودة
$stats = $stats ?? [
    'total_products' => 0,
    'active_products' => 0,
    'low_stock_count' => 0,
    'out_of_stock_count' => 0,
    'reorder_count' => 0,
    'total_categories' => 0,
    'total_warehouses' => 0,
    'total_inventory_value' => 0,
    'total_cost_value' => 0,
    'today_movements' => 0,
    'today_in_movements' => 0,
    'today_out_movements' => 0
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'وحدة إدارة المخزون' ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .stats-card {
            transition: transform 0.2s;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .page-title {
            font-size: 2.5rem;
            font-weight: 300;
            margin: 0;
        }
        .breadcrumb {
            background: transparent;
            padding: 0;
        }
        .breadcrumb-item a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
        }
        .breadcrumb-item.active {
            color: white;
        }
        .alert-dashboard {
            border-radius: 10px;
            border: none;
        }
    </style>
</head>
<body>

    <!-- Page Header -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="page-title">
                        <i class="fas fa-boxes text-white-50 me-3"></i>
                        <?= $title ?? 'وحدة إدارة المخزون' ?>
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="<?= base_url('dashboard') ?>">
                                    <i class="fas fa-home me-1"></i> الرئيسية
                                </a>
                            </li>
                            <li class="breadcrumb-item active">المخزون</li>
                        </ol>
                    </nav>
                </div>
                <div class="col-md-6 text-end">
                    <div class="btn-group">
                        <a href="<?= base_url('inventory/products/create') ?>" class="btn btn-light">
                            <i class="fas fa-plus me-2"></i> منتج جديد
                        </a>
                        <a href="<?= base_url('inventory/movements/create') ?>" class="btn btn-outline-light">
                            <i class="fas fa-exchange-alt me-2"></i> حركة مخزون
                        </a>
                        <a href="<?= base_url('inventory/reports') ?>" class="btn btn-outline-light">
                            <i class="fas fa-chart-line me-2"></i> التقارير
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        
        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stats-card border-start border-primary border-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    إجمالي المنتجات
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?= number_format($stats['total_products']) ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-cube fa-2x text-primary"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stats-card border-start border-success border-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    قيمة المخزون
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?= number_format($stats['total_inventory_value'], 2) ?> ر.س
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-dollar-sign fa-2x text-success"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stats-card border-start border-warning border-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    منخفض المخزون
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?= number_format($stats['low_stock_count']) ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stats-card border-start border-danger border-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                    نافد من المخزون
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?= number_format($stats['out_of_stock_count']) ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-exclamation-circle fa-2x text-danger"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإجراءات السريعة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-tachometer-alt me-2"></i> الإجراءات السريعة
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="<?= base_url('inventory/products') ?>" class="btn btn-outline-primary btn-block h-100 d-flex flex-column justify-content-center">
                                    <i class="fas fa-cube fa-2x mb-2"></i>
                                    <span>إدارة المنتجات</span>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="<?= base_url('inventory/stock') ?>" class="btn btn-outline-success btn-block h-100 d-flex flex-column justify-content-center">
                                    <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                    <span>أرصدة المخزون</span>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="<?= base_url('inventory/categories') ?>" class="btn btn-outline-info btn-block h-100 d-flex flex-column justify-content-center">
                                    <i class="fas fa-tags fa-2x mb-2"></i>
                                    <span>إدارة الفئات</span>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="<?= base_url('inventory/warehouses') ?>" class="btn btn-outline-warning btn-block h-100 d-flex flex-column justify-content-center">
                                    <i class="fas fa-warehouse fa-2x mb-2"></i>
                                    <span>إدارة المخازن</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تنبيهات المخزون -->
        <div class="row mb-4">
            <?php if ($stats['out_of_stock_count'] > 0): ?>
            <div class="col-md-6 mb-3">
                <div class="card border-start border-danger border-4">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-danger">
                            <i class="fas fa-exclamation-circle me-2"></i> منتجات نافدة
                        </h6>
                        <a href="<?= base_url('inventory/stock/out-of-stock') ?>" class="btn btn-sm btn-outline-danger">
                            عرض الكل
                        </a>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-danger">
                            <strong><?= $stats['out_of_stock_count'] ?></strong> منتج نافد من المخزون
                        </div>
                        <a href="<?= base_url('inventory/stock/out-of-stock') ?>" class="btn btn-danger btn-sm">
                            <i class="fas fa-eye me-1"></i> عرض المنتجات النافدة
                        </a>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <?php if ($stats['reorder_count'] > 0): ?>
            <div class="col-md-6 mb-3">
                <div class="card border-start border-warning border-4">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i> يحتاج إعادة طلب
                        </h6>
                        <a href="<?= base_url('inventory/stock/need-reorder') ?>" class="btn btn-sm btn-outline-warning">
                            عرض الكل
                        </a>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <strong><?= $stats['reorder_count'] ?></strong> منتج يحتاج إعادة طلب
                        </div>
                        <a href="<?= base_url('inventory/stock/need-reorder') ?>" class="btn btn-warning btn-sm">
                            <i class="fas fa-eye me-1"></i> عرض المنتجات
                        </a>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- إحصائيات إضافية -->
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-chart-pie me-2"></i> الفئات
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center py-4">
                            <i class="fas fa-chart-pie fa-3x text-gray-300 mb-3"></i>
                            <p class="text-muted">إجمالي الفئات: <?= $stats['total_categories'] ?? 0 ?></p>
                            <a href="<?= base_url('inventory/categories') ?>" class="btn btn-primary btn-sm">
                                <i class="fas fa-eye me-1"></i> عرض الفئات
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-success">
                            <i class="fas fa-warehouse me-2"></i> المخازن
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center py-4">
                            <i class="fas fa-warehouse fa-3x text-gray-300 mb-3"></i>
                            <p class="text-muted">إجمالي المخازن: <?= $stats['total_warehouses'] ?? 0 ?></p>
                            <a href="<?= base_url('inventory/warehouses') ?>" class="btn btn-success btn-sm">
                                <i class="fas fa-eye me-1"></i> عرض المخازن
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-info">
                            <i class="fas fa-exchange-alt me-2"></i> حركات اليوم
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center py-2">
                            <div class="row">
                                <div class="col-4">
                                    <div class="text-success">
                                        <i class="fas fa-arrow-up"></i>
                                        <div><?= $stats['today_in_movements'] ?></div>
                                        <small>إدخال</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="text-danger">
                                        <i class="fas fa-arrow-down"></i>
                                        <div><?= $stats['today_out_movements'] ?></div>
                                        <small>إخراج</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="text-primary">
                                        <i class="fas fa-list"></i>
                                        <div><?= $stats['today_movements'] ?></div>
                                        <small>الكل</small>
                                    </div>
                                </div>
                            </div>
                            <a href="<?= base_url('inventory/movements') ?>" class="btn btn-info btn-sm mt-2">
                                <i class="fas fa-eye me-1"></i> عرض الحركات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
</body>
</html>
