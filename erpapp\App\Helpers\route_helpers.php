<?php
/**
 * دوال مساعدة للمسارات
 */

/**
 * إضافة مسار
 *
 * @param string $methods الطرق المسموح بها (GET, POST, etc.)
 * @param string $route المسار
 * @param string|array $handler المتحكم والإجراء
 * @return void
 */
function add_route($methods, $route, $handler)
{
    $router = \App\Core\ModuleRouter::getInstance();
    $router->add($methods, $route, $handler);
}

/**
 * إضافة مسار GET
 *
 * @param string $route المسار
 * @param string|array $handler المتحكم والإجراء
 * @return void
 */
function get_route($route, $handler)
{
    $router = \App\Core\ModuleRouter::getInstance();
    $router->get($route, $handler);
}

/**
 * إضافة مسار POST
 *
 * @param string $route المسار
 * @param string|array $handler المتحكم والإجراء
 * @return void
 */
function post_route($route, $handler)
{
    $router = \App\Core\ModuleRouter::getInstance();
    $router->post($route, $handler);
}

/**
 * إضافة مسار PUT
 *
 * @param string $route المسار
 * @param string|array $handler المتحكم والإجراء
 * @return void
 */
function put_route($route, $handler)
{
    $router = \App\Core\ModuleRouter::getInstance();
    $router->put($route, $handler);
}

/**
 * إضافة مسار DELETE
 *
 * @param string $route المسار
 * @param string|array $handler المتحكم والإجراء
 * @return void
 */
function delete_route($route, $handler)
{
    $router = \App\Core\ModuleRouter::getInstance();
    $router->delete($route, $handler);
}

/**
 * تحميل الوحدات
 *
 * @return void
 */
function load_modules()
{
    // تحميل وحدات النظام الأساسي (System)
    load_system_modules();

    // تحميل وحدات الأعمال (Business Modules)
    load_business_modules();
}

/**
 * تحميل وحدات النظام الأساسي
 *
 * @return void
 */
function load_system_modules()
{
    $system_dir = BASE_PATH . '/App/System';

    if (!is_dir($system_dir)) {
        return;
    }

    $modules = array_filter(scandir($system_dir), function($item) use ($system_dir) {
        return is_dir($system_dir . '/' . $item) && !in_array($item, ['.', '..']);
    });

    // تحميل كل وحدة نظام
    foreach ($modules as $module) {
        $module_file = $system_dir . '/' . $module . '/Module.php';

        if (file_exists($module_file)) {
            $module_class = 'App\\System\\' . $module . '\\Module';

            if (class_exists($module_class)) {
                $module_instance = new $module_class();
                $module_instance->boot();
            }
        }
    }
}

/**
 * تحميل وحدات الأعمال
 *
 * @return void
 */
function load_business_modules()
{
    // تحميل الوحدات من مجلد App/Modules
    $app_modules_dir = BASE_PATH . '/App/Modules';

    if (is_dir($app_modules_dir)) {
        $modules = array_filter(scandir($app_modules_dir), function($item) use ($app_modules_dir) {
            return is_dir($app_modules_dir . '/' . $item) && !in_array($item, ['.', '..']);
        });

        // تحميل كل وحدة من App/Modules
        foreach ($modules as $module) {
            $module_file = $app_modules_dir . '/' . $module . '/Module.php';

            if (file_exists($module_file)) {
                $module_class = 'App\\Modules\\' . $module . '\\Module';

                if (class_exists($module_class)) {
                    $module_instance = new $module_class();
                    $module_instance->boot();
                }
            }
        }
    }

    // تحميل الوحدات من مجلد Modules (للتوافق مع النسخة القديمة)
    $modules_dir = BASE_PATH . '/Modules';

    if (is_dir($modules_dir)) {
        $modules = array_filter(scandir($modules_dir), function($item) use ($modules_dir) {
            return is_dir($modules_dir . '/' . $item) && !in_array($item, ['.', '..']);
        });

        // تحميل كل وحدة أعمال
        foreach ($modules as $module) {
            $module_file = $modules_dir . '/' . $module . '/Module.php';

            if (file_exists($module_file)) {
                $module_class = 'Modules\\' . $module . '\\Module';

                if (class_exists($module_class)) {
                    $module_instance = new $module_class();
                    $module_instance->boot();
                }
            }
        }
    }
}
