<?php
namespace App\Controllers;

/**
 * Home Controller
 */
class HomeController {
    /**
     * Route parameters
     *
     * @var array
     */
    protected $params = [];

    /**
     * Constructor
     *
     * @param array $params Route parameters
     */
    public function __construct($params = []) {
        $this->params = $params;
    }

    /**
     * Index action - show the home page
     *
     * @return void
     */
    public function index() {
        // Always show the home page for visitors
        // If user is logged in, they can still access the dashboard directly

        // Show the home page
        view('home/index', [
            'title' => __('الصفحة الرئيسية')
        ]);
    }
}
