# ERP System - Production Security Settings

# منع الوصول للملفات الحساسة
<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.log">
    Order Allow,Deny
    Deny from all
</Files>

<Files ".env">
    Order Allow,Deny
    Deny from all
</Files>

# منع الوصول للمجلدات الحساسة
RedirectMatch 403 ^/erpapp/config/.*$
RedirectMatch 403 ^/erpapp/storage/logs/.*$
RedirectMatch 403 ^/erpapp/App/Core/.*$
RedirectMatch 403 ^/erpapp/App/System/.*$
RedirectMatch 403 ^/erpapp/App/Modules/.*$
RedirectMatch 403 ^/erpapp/App/Controllers/.*$
RedirectMatch 403 ^/erpapp/App/Helpers/.*$
RedirectMatch 403 ^/erpapp/App/Routes/.*$

<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # منع الوصول المباشر للملفات PHP (عدا index.php)
    RewriteCond %{REQUEST_FILENAME} !^.*index\.php$
    RewriteCond %{REQUEST_FILENAME} \.php$
    RewriteRule ^(.*)$ index.php [L,R=404]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>

# صفحات الأخطاء المخصصة
ErrorDocument 403 /erpapp/error-handler.php?error=403
ErrorDocument 404 /erpapp/error-handler.php?error=404
ErrorDocument 500 /erpapp/error-handler.php?error=500

# تحسين الأمان
ServerSignature Off

# حماية من XSS
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# ضغط الملفات لتحسين الأداء
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تخزين مؤقت للملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
    ExpiresByType image/icon "access plus 1 month"
    ExpiresByType text/plain "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
</IfModule>
