<?php
/**
 * اختبار واجهة المستخدم مع نظام الصلاحيات
 * يوضح كيفية إخفاء/إظهار العناصر حسب الصلاحيات
 */

// تحميل النظام
require_once 'loader.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار واجهة المستخدم مع الصلاحيات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-4">
    <h1 class="text-center mb-4">
        <i class="fas fa-user-shield text-primary"></i> اختبار واجهة المستخدم مع الصلاحيات
    </h1>

    <?php 
    $user = current_user();
    if (!$user): 
    ?>
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle"></i> يجب تسجيل الدخول لاختبار الصلاحيات
    </div>
    <?php 
    else:
        $companyId = $user['current_company_id'];
        if (!$companyId):
    ?>
    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i> يجب اختيار شركة لاختبار الصلاحيات
    </div>
    <?php 
        else:
    ?>

    <!-- معلومات المستخدم -->
    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5><i class="fas fa-user"></i> معلومات المستخدم</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <strong>اسم المستخدم:</strong> <?= htmlspecialchars($user['UserName']) ?><br>
                    <strong>معرف المستخدم:</strong> <?= $user['UserID'] ?><br>
                    <strong>معرف الشركة:</strong> <?= $companyId ?>
                </div>
                <div class="col-md-6">
                    <strong>مالك الشركة:</strong> 
                    <span class="badge bg-<?= isCompanyOwner() ? 'success' : 'secondary' ?>">
                        <?= isCompanyOwner() ? 'نعم' : 'لا' ?>
                    </span><br>
                    <strong>عضو نشط:</strong> 
                    <span class="badge bg-<?= isUserActiveInCompany() ? 'success' : 'danger' ?>">
                        <?= isUserActiveInCompany() ? 'نعم' : 'لا' ?>
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- اختبار صلاحيات البرامج المختلفة -->
    <div class="row">
        <?php
        // قائمة البرامج للاختبار
        $testPrograms = [
            'products' => ['name' => 'المنتجات', 'icon' => 'fas fa-box', 'color' => 'primary'],
            'categories' => ['name' => 'الفئات', 'icon' => 'fas fa-tags', 'color' => 'success'],
            'warehouses' => ['name' => 'المستودعات', 'icon' => 'fas fa-warehouse', 'color' => 'warning'],
            'suppliers' => ['name' => 'الموردين', 'icon' => 'fas fa-truck', 'color' => 'info'],
            'customers' => ['name' => 'العملاء', 'icon' => 'fas fa-users', 'color' => 'secondary'],
            'chart_accounts' => ['name' => 'دليل الحسابات', 'icon' => 'fas fa-list', 'color' => 'dark']
        ];

        foreach ($testPrograms as $programCode => $programInfo):
        ?>
        <div class="col-md-6 mb-4">
            <div class="card border-<?= $programInfo['color'] ?>">
                <div class="card-header bg-<?= $programInfo['color'] ?> text-white">
                    <h6 class="mb-0">
                        <i class="<?= $programInfo['icon'] ?>"></i>
                        <?= $programInfo['name'] ?> (<?= $programCode ?>)
                    </h6>
                </div>
                <div class="card-body">
                    <!-- صلاحيات البرنامج -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <h6>الصلاحيات:</h6>
                            <div class="d-flex flex-wrap gap-1">
                                <span class="badge bg-<?= canView($programCode) ? 'success' : 'danger' ?>">
                                    <i class="fas fa-eye"></i> عرض
                                </span>
                                <span class="badge bg-<?= canCreate($programCode) ? 'success' : 'danger' ?>">
                                    <i class="fas fa-plus"></i> إنشاء
                                </span>
                                <span class="badge bg-<?= canEdit($programCode) ? 'success' : 'danger' ?>">
                                    <i class="fas fa-edit"></i> تعديل
                                </span>
                                <span class="badge bg-<?= canDelete($programCode) ? 'success' : 'danger' ?>">
                                    <i class="fas fa-trash"></i> حذف
                                </span>
                                <span class="badge bg-<?= canApprove($programCode) ? 'success' : 'danger' ?>">
                                    <i class="fas fa-check"></i> اعتماد
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="row">
                        <div class="col-12">
                            <h6>الأزرار المتاحة:</h6>
                            <div class="btn-group-vertical w-100" role="group">
                                
                                <!-- زر العرض -->
                                <?php if (canView($programCode)): ?>
                                <button type="button" class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-eye"></i> عرض <?= $programInfo['name'] ?>
                                </button>
                                <?php else: ?>
                                <button type="button" class="btn btn-outline-secondary btn-sm" disabled>
                                    <i class="fas fa-eye-slash"></i> عرض <?= $programInfo['name'] ?> (غير متاح)
                                </button>
                                <?php endif; ?>

                                <!-- زر الإنشاء -->
                                <?php if (canCreate($programCode)): ?>
                                <button type="button" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-plus"></i> إضافة <?= $programInfo['name'] ?>
                                </button>
                                <?php endif; ?>

                                <!-- زر التعديل -->
                                <?php if (canEdit($programCode)): ?>
                                <button type="button" class="btn btn-outline-warning btn-sm">
                                    <i class="fas fa-edit"></i> تعديل <?= $programInfo['name'] ?>
                                </button>
                                <?php endif; ?>

                                <!-- زر الحذف -->
                                <?php if (canDelete($programCode)): ?>
                                <button type="button" class="btn btn-outline-danger btn-sm">
                                    <i class="fas fa-trash"></i> حذف <?= $programInfo['name'] ?>
                                </button>
                                <?php endif; ?>

                                <!-- زر الاعتماد -->
                                <?php if (canApprove($programCode)): ?>
                                <button type="button" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-check"></i> اعتماد <?= $programInfo['name'] ?>
                                </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>

    <!-- ملخص الصلاحيات -->
    <div class="card mt-4">
        <div class="card-header bg-dark text-white">
            <h5><i class="fas fa-chart-bar"></i> ملخص الصلاحيات</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>البرنامج</th>
                            <th>عرض</th>
                            <th>إنشاء</th>
                            <th>تعديل</th>
                            <th>حذف</th>
                            <th>اعتماد</th>
                            <th>إجمالي الصلاحيات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($testPrograms as $programCode => $programInfo): ?>
                        <tr>
                            <td>
                                <i class="<?= $programInfo['icon'] ?>"></i>
                                <?= $programInfo['name'] ?>
                            </td>
                            <td>
                                <span class="badge bg-<?= canView($programCode) ? 'success' : 'danger' ?>">
                                    <?= canView($programCode) ? '✓' : '✗' ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-<?= canCreate($programCode) ? 'success' : 'danger' ?>">
                                    <?= canCreate($programCode) ? '✓' : '✗' ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-<?= canEdit($programCode) ? 'success' : 'danger' ?>">
                                    <?= canEdit($programCode) ? '✓' : '✗' ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-<?= canDelete($programCode) ? 'success' : 'danger' ?>">
                                    <?= canDelete($programCode) ? '✓' : '✗' ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-<?= canApprove($programCode) ? 'success' : 'danger' ?>">
                                    <?= canApprove($programCode) ? '✓' : '✗' ?>
                                </span>
                            </td>
                            <td>
                                <?php 
                                $totalPermissions = 0;
                                if (canView($programCode)) $totalPermissions++;
                                if (canCreate($programCode)) $totalPermissions++;
                                if (canEdit($programCode)) $totalPermissions++;
                                if (canDelete($programCode)) $totalPermissions++;
                                if (canApprove($programCode)) $totalPermissions++;
                                ?>
                                <span class="badge bg-primary"><?= $totalPermissions ?>/5</span>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- مثال على استخدام الصلاحيات في النماذج -->
    <div class="card mt-4">
        <div class="card-header bg-success text-white">
            <h5><i class="fas fa-code"></i> مثال على الكود المستخدم</h5>
        </div>
        <div class="card-body">
            <h6>كيفية استخدام دوال الصلاحيات في الكود:</h6>
            <pre class="bg-light p-3 rounded"><code>&lt;?php if (canCreate('products')): ?&gt;
&lt;a href="inventory/products/create" class="btn btn-primary"&gt;
    &lt;i class="fas fa-plus"&gt;&lt;/i&gt; إضافة منتج جديد
&lt;/a&gt;
&lt;?php endif; ?&gt;

&lt;?php if (canEdit('products')): ?&gt;
&lt;a href="inventory/products/edit" class="btn btn-warning"&gt;
    &lt;i class="fas fa-edit"&gt;&lt;/i&gt; تعديل
&lt;/a&gt;
&lt;?php endif; ?&gt;

&lt;?php if (canDelete('products')): ?&gt;
&lt;button class="btn btn-danger" onclick="confirmDelete()"&gt;
    &lt;i class="fas fa-trash"&gt;&lt;/i&gt; حذف
&lt;/button&gt;
&lt;?php endif; ?&gt;</code></pre>

            <h6 class="mt-3">الدوال المتاحة:</h6>
            <ul class="list-group">
                <li class="list-group-item"><code>canView($programCode)</code> - للتحقق من صلاحية العرض</li>
                <li class="list-group-item"><code>canCreate($programCode)</code> - للتحقق من صلاحية الإنشاء</li>
                <li class="list-group-item"><code>canEdit($programCode)</code> - للتحقق من صلاحية التعديل</li>
                <li class="list-group-item"><code>canDelete($programCode)</code> - للتحقق من صلاحية الحذف</li>
                <li class="list-group-item"><code>canApprove($programCode)</code> - للتحقق من صلاحية الاعتماد</li>
            </ul>
        </div>
    </div>

    <!-- روابط للاختبار -->
    <div class="card mt-4">
        <div class="card-header bg-warning text-dark">
            <h5><i class="fas fa-link"></i> روابط للاختبار</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>صفحات الاختبار:</h6>
                    <div class="d-grid gap-2">
                        <a href="example_products_page.php" class="btn btn-outline-primary" target="_blank">
                            <i class="fas fa-box"></i> مثال صفحة المنتجات
                        </a>
                        <a href="test_new_structure.php" class="btn btn-outline-info" target="_blank">
                            <i class="fas fa-sitemap"></i> اختبار الهيكل الجديد
                        </a>
                    </div>
                </div>
                <div class="col-md-6">
                    <h6>صفحات النظام:</h6>
                    <div class="d-grid gap-2">
                        <a href="inventory/products" class="btn btn-outline-secondary" target="_blank">
                            <i class="fas fa-box"></i> صفحة المنتجات الفعلية
                        </a>
                        <a href="inventory/categories" class="btn btn-outline-secondary" target="_blank">
                            <i class="fas fa-tags"></i> صفحة الفئات الفعلية
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php endif; ?>
    <?php endif; ?>
</div>

</body>
</html>
