<?php
/**
 * Test Inventory Module - اختبار وحدة المخزون
 */

echo "<h1>اختبار وحدة المخزون</h1>";

// تحميل النظام
require_once 'erpapp/loader.php';

echo "<h2>1. فحص ملفات الوحدة:</h2>";

$files_to_check = [
    'erpapp/Modules/Accounting/Module.php',
    'erpapp/Modules/Accounting/Inventory/Module.php',
    'erpapp/Modules/Accounting/Inventory/Controllers/InventoryController.php',
    'erpapp/Modules/Accounting/Inventory/Views/dashboard/index.php'
];

foreach ($files_to_check as $file) {
    $exists = file_exists($file);
    $color = $exists ? 'green' : 'red';
    $status = $exists ? '✅ موجود' : '❌ غير موجود';
    echo "<p style='color: $color;'>$file - $status</p>";
}

echo "<h2>2. اختبار تحميل الفئات:</h2>";

try {
    // محاولة تحميل وحدة المحاسبة
    require_once 'erpapp/Modules/Accounting/Module.php';
    echo "<p style='color: green;'>✅ تم تحميل وحدة المحاسبة</p>";
    
    $accountingModule = new \Modules\Accounting\Module();
    echo "<p style='color: green;'>✅ تم إنشاء كائن وحدة المحاسبة</p>";
    
    // محاولة تحميل وحدة المخزون
    require_once 'erpapp/Modules/Accounting/Inventory/Module.php';
    echo "<p style='color: green;'>✅ تم تحميل وحدة المخزون</p>";
    
    $inventoryModule = new \Modules\Accounting\Inventory\Module();
    echo "<p style='color: green;'>✅ تم إنشاء كائن وحدة المخزون</p>";
    
    // محاولة تحميل Controller
    require_once 'erpapp/Modules/Accounting/Inventory/Controllers/InventoryController.php';
    echo "<p style='color: green;'>✅ تم تحميل InventoryController</p>";
    
    $controller = new \Modules\Accounting\Inventory\Controllers\InventoryController();
    echo "<p style='color: green;'>✅ تم إنشاء كائن InventoryController</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}

echo "<h2>3. اختبار تسجيل المسارات:</h2>";

try {
    // تسجيل مسارات وحدة المحاسبة
    $accountingModule->boot();
    echo "<p style='color: green;'>✅ تم تشغيل وحدة المحاسبة</p>";
    
    // فحص المسارات المسجلة
    $moduleRouter = \App\Core\ModuleRouter::getInstance();
    $routes = $moduleRouter->getRoutes();
    
    $inventoryRoutes = array_filter($routes, function($route) {
        return strpos($route['route'], '/inventory') === 0;
    });
    
    echo "<p>عدد مسارات المخزون المسجلة: " . count($inventoryRoutes) . "</p>";
    
    if (count($inventoryRoutes) > 0) {
        echo "<ul>";
        foreach ($inventoryRoutes as $route) {
            echo "<li>" . implode(', ', $route['methods']) . " " . $route['route'] . "</li>";
        }
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في تسجيل المسارات: " . $e->getMessage() . "</p>";
}

echo "<h2>4. اختبار مطابقة المسار:</h2>";

try {
    $moduleRouter = \App\Core\ModuleRouter::getInstance();
    
    if ($moduleRouter->match('/inventory', 'GET')) {
        echo "<p style='color: green;'>✅ مسار /inventory يتطابق!</p>";
        
        $params = $moduleRouter->getParams();
        echo "<pre>" . print_r($params, true) . "</pre>";
        
        // محاولة تشغيل Controller
        echo "<h3>تشغيل Controller:</h3>";
        $controller->index();
        
    } else {
        echo "<p style='color: red;'>❌ مسار /inventory لا يتطابق</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في اختبار المسار: " . $e->getMessage() . "</p>";
}

echo "<h2>5. معلومات إضافية:</h2>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Current Directory: " . getcwd() . "</p>";
echo "<p>BASE_PATH: " . (defined('BASE_PATH') ? BASE_PATH : 'غير محدد') . "</p>";
?>
