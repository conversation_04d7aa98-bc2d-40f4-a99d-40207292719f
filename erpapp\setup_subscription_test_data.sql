-- إعداد بيانات اختبار نظام الاشتراكات والصلاحيات

-- 1. إن<PERSON><PERSON><PERSON> خطط الاشتراك
INSERT IGNORE INTO `subscription_plans` (`plan_id`, `plan_name_ar`, `plan_name_en`, `description_ar`, `description_en`, `price_monthly`, `price_yearly`, `max_users`, `max_programs`, `max_storage_gb`, `is_active`) VALUES
(1, 'الخطة الأساسية', 'Basic Plan', 'خطة أساسية للشركات الصغيرة', 'Basic plan for small businesses', 99.00, 999.00, 5, 3, 5, 1),
(2, 'الخطة المتقدمة', 'Advanced Plan', 'خطة متقدمة للشركات المتوسطة', 'Advanced plan for medium businesses', 199.00, 1999.00, 15, 8, 20, 1),
(3, 'الخطة الاحترافية', 'Professional Plan', 'خطة احترافية للشركات الكبيرة', 'Professional plan for large businesses', 399.00, 3999.00, 50, 15, 100, 1);

-- 2. إنشاء ميزات الاشتراك
INSERT IGNORE INTO `subscription_features` (`feature_id`, `feature_name_ar`, `feature_name_en`, `feature_description_ar`, `feature_description_en`, `feature_code`, `is_active`) VALUES
(1, 'إدارة المخزون', 'Inventory Management', 'إدارة شاملة للمخزون والمنتجات', 'Comprehensive inventory and product management', 'inventory_module', 1),
(2, 'إدارة المشتريات', 'Purchase Management', 'إدارة المشتريات والموردين', 'Purchase and supplier management', 'purchases_module', 1),
(3, 'إدارة المبيعات', 'Sales Management', 'إدارة المبيعات والعملاء', 'Sales and customer management', 'sales_module', 1),
(4, 'المحاسبة', 'Accounting', 'نظام محاسبي متكامل', 'Integrated accounting system', 'accounting_module', 1),
(5, 'الموارد البشرية', 'Human Resources', 'إدارة الموارد البشرية والرواتب', 'HR and payroll management', 'hr_module', 1),
(6, 'التقارير المتقدمة', 'Advanced Reports', 'تقارير تحليلية متقدمة', 'Advanced analytical reports', 'reports_module', 1);

-- 3. ربط الميزات بالخطط
-- الخطة الأساسية: المخزون + المشتريات + المبيعات
INSERT IGNORE INTO `plan_features` (`plan_id`, `feature_id`, `is_enabled`) VALUES
(1, 1, 1), -- المخزون
(1, 2, 1), -- المشتريات  
(1, 3, 1); -- المبيعات

-- الخطة المتقدمة: كل ما سبق + المحاسبة + التقارير
INSERT IGNORE INTO `plan_features` (`plan_id`, `feature_id`, `is_enabled`) VALUES
(2, 1, 1), -- المخزون
(2, 2, 1), -- المشتريات
(2, 3, 1), -- المبيعات
(2, 4, 1), -- المحاسبة
(2, 6, 1); -- التقارير

-- الخطة الاحترافية: جميع الميزات
INSERT IGNORE INTO `plan_features` (`plan_id`, `feature_id`, `is_enabled`) VALUES
(3, 1, 1), -- المخزون
(3, 2, 1), -- المشتريات
(3, 3, 1), -- المبيعات
(3, 4, 1), -- المحاسبة
(3, 5, 1), -- الموارد البشرية
(3, 6, 1); -- التقارير

-- 4. ربط البرامج بالخطط (إذا كانت البيانات موجودة)
-- الخطة الأساسية
INSERT IGNORE INTO `plan_available_programs` (`plan_id`, `program_id`, `is_included`)
SELECT 1, mp.program_id, 1
FROM module_programs mp
JOIN system_modules sm ON sm.module_id = mp.module_id
WHERE mp.company_id = 4 
AND sm.module_code IN ('inventory', 'purchases', 'sales');

-- الخطة المتقدمة
INSERT IGNORE INTO `plan_available_programs` (`plan_id`, `program_id`, `is_included`)
SELECT 2, mp.program_id, 1
FROM module_programs mp
JOIN system_modules sm ON sm.module_id = mp.module_id
WHERE mp.company_id = 4 
AND sm.module_code IN ('inventory', 'purchases', 'sales', 'accounting', 'reports');

-- الخطة الاحترافية
INSERT IGNORE INTO `plan_available_programs` (`plan_id`, `program_id`, `is_included`)
SELECT 3, mp.program_id, 1
FROM module_programs mp
JOIN system_modules sm ON sm.module_id = mp.module_id
WHERE mp.company_id = 4;

-- 5. إنشاء اشتراكات للشركات

-- الشركة 4: اشتراك نشط في الخطة المتقدمة
INSERT IGNORE INTO `subscriptions` (`subscription_id`, `company_id`, `plan_id`, `start_date`, `end_date`, `status`, `billing_cycle`) VALUES
(1, 4, 2, '2024-01-01 00:00:00', '2024-12-31 23:59:59', 'active', 'yearly');

-- تحديث الشركة 4 لربطها بالاشتراك
UPDATE `companies` SET 
    `subscription_id` = 1,
    `CompanyStatus` = 'Active'
WHERE `CompanyID` = 4;

-- 6. إنشاء شركة تجريبية للاختبار
INSERT IGNORE INTO `companies` (`CompanyID`, `OwnerID`, `CompanyName`, `CompanyEmail`, `CompanyPhone`, `CompanyStatus`, `trial_start_date`, `trial_end_date`) VALUES
(5, 32, 'شركة تجريبية', '<EMAIL>', '0501234567', 'Trial', NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY));

-- 7. إنشاء شركة منتهية الاشتراك للاختبار
INSERT IGNORE INTO `companies` (`CompanyID`, `OwnerID`, `CompanyName`, `CompanyEmail`, `CompanyPhone`, `CompanyStatus`) VALUES
(6, 32, 'شركة منتهية الاشتراك', '<EMAIL>', '0501234568', 'Expired');

-- اشتراك منتهي للشركة 6
INSERT IGNORE INTO `subscriptions` (`subscription_id`, `company_id`, `plan_id`, `start_date`, `end_date`, `status`, `billing_cycle`) VALUES
(2, 6, 1, '2023-01-01 00:00:00', '2023-12-31 23:59:59', 'expired', 'yearly');

UPDATE `companies` SET `subscription_id` = 2 WHERE `CompanyID` = 6;

-- 8. إضافة المستخدم 32 للشركات الجديدة
INSERT IGNORE INTO `company_users` (`user_id`, `company_id`, `position_id`, `added_by`, `status`, `user_status`) VALUES
(32, 5, NULL, 32, 'accepted', 'active'), -- الشركة التجريبية
(32, 6, NULL, 32, 'accepted', 'active'); -- الشركة المنتهية

-- 9. تحديث نوع الوحدات في system_modules
UPDATE `system_modules` SET `module_type` = 'core' WHERE `module_code` IN ('inventory', 'purchases', 'sales');
UPDATE `system_modules` SET `module_type` = 'addon' WHERE `module_code` IN ('accounting', 'reports');
UPDATE `system_modules` SET `module_type` = 'premium' WHERE `module_code` = 'hr';

-- 10. إنشاء دفعة للاشتراك النشط
INSERT IGNORE INTO `payments` (`payment_id`, `subscription_id`, `company_id`, `amount`, `currency`, `payment_method`, `status`, `payment_date`, `invoice_number`) VALUES
(1, 1, 4, 1999.00, 'SAR', 'credit_card', 'completed', '2024-01-01 10:00:00', 'INV-2024-001');

-- عرض النتائج
SELECT 'تم إنشاء بيانات الاختبار بنجاح!' as message;

-- عرض ملخص الشركات والاشتراكات
SELECT 
    c.CompanyID,
    c.CompanyName,
    c.CompanyStatus,
    c.trial_end_date,
    s.status as subscription_status,
    s.start_date,
    s.end_date,
    sp.plan_name_ar
FROM companies c
LEFT JOIN subscriptions s ON s.subscription_id = c.subscription_id
LEFT JOIN subscription_plans sp ON sp.plan_id = s.plan_id
WHERE c.CompanyID IN (4, 5, 6)
ORDER BY c.CompanyID;

-- عرض الوحدات المنزلة للشركة 4
SELECT 
    sm.module_code,
    sm.module_name_ar,
    sm.module_type,
    cm.is_active,
    cm.license_expires_at
FROM company_modules cm
JOIN system_modules sm ON sm.module_id = cm.module_id
WHERE cm.company_id = 4
ORDER BY sm.module_code;
