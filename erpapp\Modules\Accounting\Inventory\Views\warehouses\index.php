<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title">
                    <i class="fas fa-warehouse text-primary"></i> <?= $title ?? 'إدارة المخازن' ?>
                </h1>
                <div class="btn-group">
                    <a href="<?= base_url('inventory/warehouses/create') ?>" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-2"></i> مخزن جديد
                    </a>
                    <a href="<?= base_url('inventory/export/warehouses') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-download me-2"></i> تصدير
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (isset($_SESSION['flash_message'])): ?>
        <div class="alert alert-<?= $_SESSION['flash_message']['type'] ?> alert-dismissible fade show" role="alert">
            <?= $_SESSION['flash_message']['message'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['flash_message']); ?>
    <?php endif; ?>

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي المخازن</h6>
                            <h3><?= number_format($total_count) ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-warehouse fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">المخازن النشطة</h6>
                            <h3><?= number_format(count(array_filter($warehouses, function($w) { return $w['is_active']; }))) ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي السعة</h6>
                            <h3><?= number_format(array_sum(array_column($warehouses, 'total_capacity'))) ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-expand-arrows-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">متوسط الاستخدام</h6>
                            <h3><?= number_format(array_sum(array_column($warehouses, 'usage_percentage')) / max(count($warehouses), 1), 1) ?>%</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-pie fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-filter me-2"></i> فلترة النتائج
                    </h6>
                </div>
                <div class="card-body">
                    <form method="GET" action="<?= base_url('inventory/warehouses') ?>">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="search" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="<?= htmlspecialchars($filters['search'] ?? '') ?>" 
                                       placeholder="اسم المخزن أو الكود">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="warehouse_type" class="form-label">نوع المخزن</label>
                                <select class="form-select" id="warehouse_type" name="warehouse_type">
                                    <option value="">جميع الأنواع</option>
                                    <option value="main" <?= ($filters['warehouse_type'] ?? '') == 'main' ? 'selected' : '' ?>>رئيسي</option>
                                    <option value="branch" <?= ($filters['warehouse_type'] ?? '') == 'branch' ? 'selected' : '' ?>>فرعي</option>
                                    <option value="transit" <?= ($filters['warehouse_type'] ?? '') == 'transit' ? 'selected' : '' ?>>عبور</option>
                                    <option value="virtual" <?= ($filters['warehouse_type'] ?? '') == 'virtual' ? 'selected' : '' ?>>افتراضي</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="is_active" class="form-label">الحالة</label>
                                <select class="form-select" id="is_active" name="is_active">
                                    <option value="">جميع الحالات</option>
                                    <option value="1" <?= ($filters['is_active'] ?? '') === 1 ? 'selected' : '' ?>>نشط</option>
                                    <option value="0" <?= ($filters['is_active'] ?? '') === 0 ? 'selected' : '' ?>>غير نشط</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search me-1"></i> بحث
                                </button>
                                <a href="<?= base_url('inventory/warehouses') ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i> إعادة تعيين
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Warehouses Table -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">قائمة المخازن</h6>
                </div>
                <div class="card-body">
                    <?php if (empty($warehouses)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-warehouse fa-3x text-gray-300 mb-3"></i>
                            <h5 class="text-muted">لا توجد مخازن</h5>
                            <p class="text-muted">لم يتم العثور على مخازن تطابق معايير البحث</p>
                            <a href="<?= base_url('inventory/warehouses/create') ?>" class="btn btn-primary">
                                <i class="fas fa-plus-circle me-2"></i> إضافة مخزن جديد
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="warehousesTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الكود</th>
                                        <th>اسم المخزن</th>
                                        <th>النوع</th>
                                        <th>الموقع</th>
                                        <th>السعة</th>
                                        <th>الاستخدام</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($warehouses as $warehouse): ?>
                                        <tr>
                                            <td>
                                                <strong><?= htmlspecialchars($warehouse['warehouse_code']) ?></strong>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?= htmlspecialchars($warehouse['warehouse_name_ar']) ?></strong>
                                                    <?php if (!empty($warehouse['warehouse_name_en'])): ?>
                                                        <br><small class="text-muted"><?= htmlspecialchars($warehouse['warehouse_name_en']) ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <?php
                                                $type_labels = [
                                                    'main' => ['رئيسي', 'bg-primary'],
                                                    'branch' => ['فرعي', 'bg-info'],
                                                    'transit' => ['عبور', 'bg-warning'],
                                                    'virtual' => ['افتراضي', 'bg-secondary']
                                                ];
                                                $type_info = $type_labels[$warehouse['warehouse_type']] ?? ['غير محدد', 'bg-light'];
                                                ?>
                                                <span class="badge <?= $type_info[1] ?>"><?= $type_info[0] ?></span>
                                            </td>
                                            <td>
                                                <?php if (!empty($warehouse['address'])): ?>
                                                    <small><?= htmlspecialchars($warehouse['address']) ?></small>
                                                <?php else: ?>
                                                    <span class="text-muted">غير محدد</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($warehouse['total_capacity'] > 0): ?>
                                                    <?= number_format($warehouse['total_capacity'], 2) ?>
                                                    <br><small class="text-muted"><?= htmlspecialchars($warehouse['capacity_unit'] ?? 'وحدة') ?></small>
                                                <?php else: ?>
                                                    <span class="text-muted">غير محدود</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php 
                                                $usage_percentage = $warehouse['usage_percentage'] ?? 0;
                                                $progress_class = 'bg-success';
                                                if ($usage_percentage > 70) $progress_class = 'bg-warning';
                                                if ($usage_percentage > 90) $progress_class = 'bg-danger';
                                                ?>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar <?= $progress_class ?>" role="progressbar" 
                                                         style="width: <?= $usage_percentage ?>%" 
                                                         aria-valuenow="<?= $usage_percentage ?>" aria-valuemin="0" aria-valuemax="100">
                                                        <?= number_format($usage_percentage, 1) ?>%
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if ($warehouse['is_active']): ?>
                                                    <span class="badge bg-success">نشط</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">غير نشط</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="<?= base_url('inventory/warehouses/' . $warehouse['warehouse_id']) ?>" 
                                                       class="btn btn-outline-primary" title="عرض">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('inventory/warehouses/' . $warehouse['warehouse_id'] . '/edit') ?>" 
                                                       class="btn btn-outline-secondary" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-outline-danger" 
                                                            onclick="deleteWarehouse(<?= $warehouse['warehouse_id'] ?>)" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i> تأكيد الحذف
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من رغبتك في حذف هذا المخزن؟</p>
                <p class="text-danger"><strong>تحذير:</strong> لا يمكن حذف مخزن يحتوي على مخزون.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i> حذف
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function deleteWarehouse(warehouseId) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `<?= base_url('inventory/warehouses/') ?>${warehouseId}/delete`;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

// Initialize DataTable if available
document.addEventListener('DOMContentLoaded', function() {
    if (typeof $.fn.DataTable !== 'undefined') {
        $('#warehousesTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
            },
            "pageLength": 25,
            "order": [[1, "asc"]],
            "columnDefs": [
                { "orderable": false, "targets": -1 }
            ]
        });
    }
});
</script>
