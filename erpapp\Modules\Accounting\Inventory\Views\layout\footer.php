    </main>

    <!-- Footer -->
    <footer class="bg-white border-top mt-5 py-4">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6">
                    <p class="text-muted mb-0">
                        <i class="fas fa-boxes text-primary me-2"></i>
                        وحدة إدارة المخزون - نظام ERP
                    </p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="text-muted mb-0">
                        <i class="fas fa-calendar-alt me-1"></i>
                        <?= date('Y') ?> &copy; جميع الحقوق محفوظة
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="position-fixed top-0 start-0 w-100 h-100 d-none" 
         style="background-color: rgba(0, 0, 0, 0.5); z-index: 9999;">
        <div class="d-flex justify-content-center align-items-center h-100">
            <div class="text-center text-white">
                <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <h5>جاري التحميل...</h5>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    
    <!-- Toastr JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    
    <!-- Chart.js (for reports) -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // Global configuration
        $(document).ready(function() {
            // Configure Toastr
            toastr.options = {
                "closeButton": true,
                "debug": false,
                "newestOnTop": true,
                "progressBar": true,
                "positionClass": "toast-top-left",
                "preventDuplicates": false,
                "onclick": null,
                "showDuration": "300",
                "hideDuration": "1000",
                "timeOut": "5000",
                "extendedTimeOut": "1000",
                "showEasing": "swing",
                "hideEasing": "linear",
                "showMethod": "fadeIn",
                "hideMethod": "fadeOut"
            };

            // Configure DataTables defaults
            if (typeof $.fn.DataTable !== 'undefined') {
                $.extend(true, $.fn.dataTable.defaults, {
                    "language": {
                        "url": "//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json"
                    },
                    "pageLength": 25,
                    "responsive": true,
                    "autoWidth": false,
                    "columnDefs": [
                        { "orderable": false, "targets": "no-sort" }
                    ]
                });
            }

            // Add loading overlay to forms
            $('form').on('submit', function() {
                showLoading();
            });

            // Add confirmation to delete buttons
            $('.btn-delete, .delete-btn').on('click', function(e) {
                e.preventDefault();
                const message = $(this).data('message') || 'هل أنت متأكد من رغبتك في الحذف؟';
                if (confirm(message)) {
                    if ($(this).is('a')) {
                        window.location.href = $(this).attr('href');
                    } else {
                        $(this).closest('form').submit();
                    }
                }
            });

            // Auto-hide alerts after 5 seconds
            $('.alert:not(.alert-permanent)').delay(5000).fadeOut();

            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Initialize popovers
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });

            // Add animation to cards
            $('.card').addClass('fade-in');

            // Number formatting for Arabic
            $('.number-format').each(function() {
                const number = parseFloat($(this).text());
                if (!isNaN(number)) {
                    $(this).text(number.toLocaleString('ar-SA'));
                }
            });

            // Auto-refresh for real-time data (every 5 minutes)
            if ($('.auto-refresh').length > 0) {
                setInterval(function() {
                    location.reload();
                }, 300000); // 5 minutes
            }
        });

        // Utility functions
        function showLoading() {
            $('#loadingOverlay').removeClass('d-none');
        }

        function hideLoading() {
            $('#loadingOverlay').addClass('d-none');
        }

        function showToast(type, message, title = '') {
            switch(type) {
                case 'success':
                    toastr.success(message, title || 'نجح');
                    break;
                case 'error':
                    toastr.error(message, title || 'خطأ');
                    break;
                case 'warning':
                    toastr.warning(message, title || 'تحذير');
                    break;
                case 'info':
                    toastr.info(message, title || 'معلومات');
                    break;
            }
        }

        function formatNumber(number, decimals = 2) {
            return parseFloat(number).toLocaleString('ar-SA', {
                minimumFractionDigits: decimals,
                maximumFractionDigits: decimals
            });
        }

        function formatCurrency(amount, currency = 'ر.س') {
            return formatNumber(amount, 2) + ' ' + currency;
        }

        function confirmAction(message, callback) {
            if (confirm(message)) {
                callback();
            }
        }

        // AJAX helper function
        function makeAjaxRequest(url, data, method = 'POST', successCallback = null, errorCallback = null) {
            showLoading();
            
            $.ajax({
                url: url,
                method: method,
                data: data,
                dataType: 'json',
                success: function(response) {
                    hideLoading();
                    if (response.success) {
                        if (response.message) {
                            showToast('success', response.message);
                        }
                        if (successCallback) {
                            successCallback(response);
                        }
                    } else {
                        showToast('error', response.message || 'حدث خطأ غير متوقع');
                        if (errorCallback) {
                            errorCallback(response);
                        }
                    }
                },
                error: function(xhr, status, error) {
                    hideLoading();
                    showToast('error', 'حدث خطأ في الاتصال بالخادم');
                    if (errorCallback) {
                        errorCallback({success: false, message: error});
                    }
                }
            });
        }

        // Print function
        function printPage() {
            window.print();
        }

        // Export functions
        function exportToExcel(tableId, filename = 'export') {
            // This would require additional library like SheetJS
            showToast('info', 'ميزة التصدير إلى Excel قيد التطوير');
        }

        function exportToPDF(elementId, filename = 'export') {
            // This would require additional library like jsPDF
            showToast('info', 'ميزة التصدير إلى PDF قيد التطوير');
        }

        // Search functionality
        function initializeSearch(inputId, tableId) {
            $('#' + inputId).on('keyup', function() {
                const value = $(this).val().toLowerCase();
                $('#' + tableId + ' tbody tr').filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
                });
            });
        }

        // Form validation helpers
        function validateRequired(formId) {
            let isValid = true;
            $('#' + formId + ' [required]').each(function() {
                if (!$(this).val().trim()) {
                    $(this).addClass('is-invalid');
                    isValid = false;
                } else {
                    $(this).removeClass('is-invalid');
                }
            });
            return isValid;
        }

        // Auto-save functionality
        function enableAutoSave(formId, saveUrl, interval = 30000) {
            setInterval(function() {
                const formData = $('#' + formId).serialize();
                $.ajax({
                    url: saveUrl,
                    method: 'POST',
                    data: formData + '&auto_save=1',
                    success: function(response) {
                        if (response.success) {
                            showToast('info', 'تم الحفظ التلقائي', '');
                        }
                    }
                });
            }, interval);
        }

        // Keyboard shortcuts
        $(document).on('keydown', function(e) {
            // Ctrl+S for save
            if (e.ctrlKey && e.which === 83) {
                e.preventDefault();
                $('form:visible').first().submit();
            }
            
            // Ctrl+N for new
            if (e.ctrlKey && e.which === 78) {
                e.preventDefault();
                const createBtn = $('.btn-primary:contains("جديد"), .btn-primary:contains("إضافة")').first();
                if (createBtn.length) {
                    createBtn.click();
                }
            }
            
            // ESC to close modals
            if (e.which === 27) {
                $('.modal:visible').modal('hide');
            }
        });

        // Real-time clock
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA');
            const dateString = now.toLocaleDateString('ar-SA');
            
            if ($('#current-time').length) {
                $('#current-time').text(timeString);
            }
            if ($('#current-date').length) {
                $('#current-date').text(dateString);
            }
        }

        // Update clock every second
        setInterval(updateClock, 1000);
        updateClock(); // Initial call
    </script>

    <!-- Page-specific scripts -->
    <?php if (isset($scripts)): ?>
        <?php foreach ($scripts as $script): ?>
            <script src="<?= $script ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Inline scripts -->
    <?php if (isset($inline_scripts)): ?>
        <script>
            <?= $inline_scripts ?>
        </script>
    <?php endif; ?>

</body>
</html>
