<?php
/**
 * قالب أساسي للنماذج
 * يوفر هيكل موحد لجميع نماذج النظام
 */

// المتغيرات المطلوبة:
// $page_title - عنوان الصفحة
// $page_icon - أيقونة الصفحة
// $back_url - رابط العودة
// $back_text - نص زر العودة
// $form_action - action للنموذج
// $form_id - معرف النموذج
// $card_title - عنوان البطاقة
// $card_icon - أيقونة البطاقة
// $tabs - مصفوفة التبويبات
// $flash_key - مفتاح رسائل Flash
// $submit_text - نص زر الإرسال
// $additional_buttons - أزرار إضافية (اختياري)
?>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title">
                    <i class="<?= $page_icon ?>"></i> <?= __($page_title) ?>
                </h1>
                <a href="<?= $back_url ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> <?= __($back_text) ?>
                </a>
            </div>
        </div>
    </div>

    <?php if (isset($flash_key)): ?>
        <?php display_flash($flash_key); ?>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-10 mx-auto">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="<?= $card_icon ?> me-2"></i> <?= __($card_title) ?>
                    </h5>
                </div>
                <div class="card-body">
                    <form action="<?= $form_action ?>" method="post" enctype="multipart/form-data" id="<?= $form_id ?>">
                        <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">

                        <?php if (isset($tabs) && !empty($tabs)): ?>
                        <!-- تبويبات النموذج -->
                        <ul class="nav nav-tabs mb-4" id="<?= $form_id ?>Tabs" role="tablist">
                            <?php foreach ($tabs as $index => $tab): ?>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link <?= $index === 0 ? 'active' : '' ?>" 
                                        id="<?= $tab['id'] ?>-tab" 
                                        data-bs-toggle="tab" 
                                        data-bs-target="#<?= $tab['id'] ?>" 
                                        type="button" 
                                        role="tab" 
                                        aria-controls="<?= $tab['id'] ?>" 
                                        aria-selected="<?= $index === 0 ? 'true' : 'false' ?>">
                                    <i class="<?= $tab['icon'] ?> me-1"></i> <?= __($tab['title']) ?>
                                </button>
                            </li>
                            <?php endforeach; ?>
                        </ul>

                        <!-- محتوى التبويبات -->
                        <div class="tab-content" id="<?= $form_id ?>TabsContent">
                            <?php foreach ($tabs as $index => $tab): ?>
                            <div class="tab-pane fade <?= $index === 0 ? 'show active' : '' ?>" 
                                 id="<?= $tab['id'] ?>" 
                                 role="tabpanel" 
                                 aria-labelledby="<?= $tab['id'] ?>-tab">
                                <?php 
                                // تضمين محتوى التبويب
                                if (isset($tab['content_file'])) {
                                    include $tab['content_file'];
                                } elseif (isset($tab['content'])) {
                                    echo $tab['content'];
                                }
                                ?>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php else: ?>
                        <!-- محتوى النموذج بدون تبويبات -->
                        <?php 
                        if (isset($form_content_file)) {
                            include $form_content_file;
                        } elseif (isset($form_content)) {
                            echo $form_content;
                        }
                        ?>
                        <?php endif; ?>

                        <!-- أزرار النموذج -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <a href="<?= $back_url ?>" class="btn btn-light me-md-2">
                                <i class="fas fa-times me-1"></i> <?= __('إلغاء') ?>
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> <?= __($submit_text) ?>
                            </button>
                            <?php if (isset($additional_buttons)): ?>
                                <?php echo $additional_buttons; ?>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if (isset($custom_js_file)): ?>
    <script src="<?= $custom_js_file ?>"></script>
<?php elseif (isset($custom_js)): ?>
    <script><?= $custom_js ?></script>
<?php endif; ?>
