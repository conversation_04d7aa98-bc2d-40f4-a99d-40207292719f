<?php
namespace Modules\Accounting\Sales;

use App\Core\Module as BaseModule;

/**
 * وحدة المبيعات
 */
class Module extends BaseModule
{
    /**
     * اسم الوحدة
     */
    protected $name = 'Sales';

    /**
     * وصف الوحدة
     */
    protected $description = 'إدارة المبيعات والفواتير';

    /**
     * إصدار الوحدة
     */
    protected $version = '1.0.0';

    /**
     * الوحدات المطلوبة
     */
    protected $dependencies = ['System.Companies', 'System.Users'];

    /**
     * تسجيل المسارات الخاصة بالوحدة
     */
    public function registerRoutes()
    {
        // مسارات المبيعات
        add_route('GET', '/sales', 'App\Modules\Accounting\Sales\Controllers\SalesController@index');
        add_route('GET', '/sales/invoices', 'App\Modules\Accounting\Sales\Controllers\InvoiceController@index');
        add_route('GET', '/sales/invoices/create', 'App\Modules\Accounting\Sales\Controllers\InvoiceController@create');
        add_route('POST', '/sales/invoices/store', 'App\Modules\Accounting\Sales\Controllers\InvoiceController@store');
        add_route('GET', '/sales/invoices/{id}', 'App\Modules\Accounting\Sales\Controllers\InvoiceController@show');
        add_route('GET', '/sales/invoices/{id}/edit', 'App\Modules\Accounting\Sales\Controllers\InvoiceController@edit');
        add_route('POST', '/sales/invoices/{id}/update', 'App\Modules\Accounting\Sales\Controllers\InvoiceController@update');
        add_route('DELETE', '/sales/invoices/{id}', 'App\Modules\Accounting\Sales\Controllers\InvoiceController@delete');

        // مسارات العملاء
        add_route('GET', '/sales/customers', 'App\Modules\Accounting\Sales\Controllers\CustomerController@index');
        add_route('GET', '/sales/customers/create', 'App\Modules\Accounting\Sales\Controllers\CustomerController@create');
        add_route('POST', '/sales/customers/store', 'App\Modules\Accounting\Sales\Controllers\CustomerController@store');
        add_route('GET', '/sales/customers/{id}', 'App\Modules\Accounting\Sales\Controllers\CustomerController@show');
        add_route('GET', '/sales/customers/{id}/edit', 'App\Modules\Accounting\Sales\Controllers\CustomerController@edit');
        add_route('POST', '/sales/customers/{id}/update', 'App\Modules\Accounting\Sales\Controllers\CustomerController@update');

        // مسارات التقارير
        add_route('GET', '/sales/reports', 'App\Modules\Accounting\Sales\Controllers\ReportController@index');
        add_route('GET', '/sales/reports/daily', 'App\Modules\Accounting\Sales\Controllers\ReportController@daily');
        add_route('GET', '/sales/reports/monthly', 'App\Modules\Accounting\Sales\Controllers\ReportController@monthly');
        add_route('GET', '/sales/reports/yearly', 'App\Modules\Accounting\Sales\Controllers\ReportController@yearly');
    }

    /**
     * تهيئة الوحدة
     */
    public function boot()
    {
        parent::boot();

        // تسجيل المسارات
        $this->registerRoutes();

        // تسجيل الخدمات
        $this->registerServices();

        // تسجيل الأحداث
        $this->registerEvents();
    }

    /**
     * تسجيل الخدمات
     */
    protected function registerServices()
    {
        // يمكن تسجيل خدمات إضافية هنا
    }

    /**
     * تسجيل الأحداث
     */
    protected function registerEvents()
    {
        // يمكن تسجيل أحداث الوحدة هنا
    }

    /**
     * التحقق من صلاحيات الوحدة
     */
    public function hasPermission($user_id, $company_id)
    {
        // التحقق من أن الشركة لديها اشتراك يسمح بوحدة المبيعات
        // يمكن تطوير هذا لاحقاً
        return true;
    }

    /**
     * الحصول على معلومات الوحدة
     */
    public function getInfo()
    {
        return [
            'name' => $this->name,
            'description' => $this->description,
            'version' => $this->version,
            'dependencies' => $this->dependencies,
            'category' => 'Accounting'
        ];
    }
}
