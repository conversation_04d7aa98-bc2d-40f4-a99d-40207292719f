<?php
namespace App\Modules\Inventory\Models;

use PDO;
use Exception;

/**
 * Category Model - نموذج الفئات
 */
class Category
{
    /**
     * Database connection
     */
    protected $db;

    /**
     * Table name
     */
    protected $table = 'inventory_categories';

    /**
     * Constructor
     */
    public function __construct()
    {
        global $db;
        $this->db = $db;
    }

    /**
     * الحصول على جميع الفئات للشركة
     */
    public function getByCompany($company_id, $filters = [])
    {
        $sql = "SELECT * FROM {$this->table} WHERE company_id = ?";
        $params = [$company_id];

        // تطبيق الفلاتر
        if (isset($filters['is_active'])) {
            $sql .= " AND is_active = ?";
            $params[] = $filters['is_active'];
        }

        if (!empty($filters['search'])) {
            $sql .= " AND (category_name_ar LIKE ? OR category_name_en LIKE ? OR category_code LIKE ?)";
            $search = '%' . $filters['search'] . '%';
            $params[] = $search;
            $params[] = $search;
            $params[] = $search;
        }

        $sql .= " ORDER BY display_order, category_name_ar";

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على فئة بالمعرف
     */
    public function getById($category_id, $company_id)
    {
        $sql = "SELECT * FROM {$this->table} WHERE category_id = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$category_id, $company_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على فئة بالكود
     */
    public function getByCode($category_code, $company_id)
    {
        $sql = "SELECT * FROM {$this->table} WHERE category_code = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$category_code, $company_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * إنشاء فئة جديدة
     */
    public function create($data)
    {
        // التحقق من عدم تكرار الكود
        if ($this->getByCode($data['category_code'], $data['company_id'])) {
            throw new Exception('كود الفئة موجود مسبقاً');
        }

        $sql = "INSERT INTO {$this->table} (
                    company_id, module_code, category_code, parent_category_id,
                    category_name_ar, category_name_en, description_ar, description_en,
                    image_url, display_order, is_active, created_by, created_at
                ) VALUES (
                    ?, 'inventory', ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW()
                )";

        $stmt = $this->db->prepare($sql);
        $result = $stmt->execute([
            $data['company_id'],
            $data['category_code'],
            $data['parent_category_id'] ?: null,
            $data['category_name_ar'],
            $data['category_name_en'] ?: null,
            $data['description_ar'] ?: null,
            $data['description_en'] ?: null,
            $data['image_url'] ?: null,
            $data['display_order'] ?: 0,
            $data['is_active'] ?: 1,
            $data['created_by']
        ]);

        return $result ? $this->db->lastInsertId() : false;
    }

    /**
     * تحديث فئة
     */
    public function update($category_id, $data, $company_id)
    {
        // التحقق من عدم تكرار الكود (إذا تم تغييره)
        if (isset($data['category_code'])) {
            $existing = $this->getByCode($data['category_code'], $company_id);
            if ($existing && $existing['category_id'] != $category_id) {
                throw new Exception('كود الفئة موجود مسبقاً');
            }
        }

        $sql = "UPDATE {$this->table} SET
                    category_code = ?, parent_category_id = ?,
                    category_name_ar = ?, category_name_en = ?,
                    description_ar = ?, description_en = ?,
                    image_url = ?, display_order = ?, is_active = ?,
                    updated_by = ?, updated_at = NOW()
                WHERE category_id = ? AND company_id = ?";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            $data['category_code'],
            $data['parent_category_id'] ?: null,
            $data['category_name_ar'],
            $data['category_name_en'] ?: null,
            $data['description_ar'] ?: null,
            $data['description_en'] ?: null,
            $data['image_url'] ?: null,
            $data['display_order'] ?: 0,
            $data['is_active'] ?: 1,
            $data['updated_by'],
            $category_id,
            $company_id
        ]);
    }

    /**
     * حذف فئة
     */
    public function delete($category_id, $company_id)
    {
        // التحقق من عدم وجود منتجات في هذه الفئة
        $sql = "SELECT COUNT(*) FROM inventory_products WHERE category_id = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$category_id, $company_id]);

        if ($stmt->fetchColumn() > 0) {
            throw new Exception('لا يمكن حذف الفئة لوجود منتجات مرتبطة بها');
        }

        // التحقق من عدم وجود فئات فرعية
        $sql = "SELECT COUNT(*) FROM {$this->table} WHERE parent_category_id = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$category_id, $company_id]);

        if ($stmt->fetchColumn() > 0) {
            throw new Exception('لا يمكن حذف الفئة لوجود فئات فرعية تابعة لها');
        }

        // حذف الفئة
        $sql = "DELETE FROM {$this->table} WHERE category_id = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$category_id, $company_id]);
    }

    /**
     * الحصول على الفئات الرئيسية
     */
    public function getMainCategories($company_id)
    {
        $sql = "SELECT * FROM {$this->table} 
                WHERE company_id = ? AND parent_category_id IS NULL AND is_active = 1
                ORDER BY display_order, category_name_ar";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على الفئات الفرعية
     */
    public function getSubCategories($parent_category_id, $company_id)
    {
        $sql = "SELECT * FROM {$this->table} 
                WHERE company_id = ? AND parent_category_id = ? AND is_active = 1
                ORDER BY display_order, category_name_ar";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id, $parent_category_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على إحصائيات الفئات
     */
    public function getStats($company_id)
    {
        $stats = [];

        // إجمالي الفئات
        $sql = "SELECT COUNT(*) FROM {$this->table} WHERE company_id = ? AND is_active = 1";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        $stats['total_categories'] = $stmt->fetchColumn();

        // الفئات الرئيسية
        $sql = "SELECT COUNT(*) FROM {$this->table} WHERE company_id = ? AND parent_category_id IS NULL AND is_active = 1";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        $stats['main_categories'] = $stmt->fetchColumn();

        // الفئات الفرعية
        $sql = "SELECT COUNT(*) FROM {$this->table} WHERE company_id = ? AND parent_category_id IS NOT NULL AND is_active = 1";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        $stats['sub_categories'] = $stmt->fetchColumn();

        return $stats;
    }

    /**
     * الحصول على عدد المنتجات في كل فئة
     */
    public function getCategoriesWithProductCount($company_id)
    {
        $sql = "SELECT c.*, COUNT(p.product_id) as product_count
                FROM {$this->table} c
                LEFT JOIN inventory_products p ON c.category_id = p.category_id AND p.company_id = c.company_id
                WHERE c.company_id = ? AND c.is_active = 1
                GROUP BY c.category_id
                ORDER BY c.display_order, c.category_name_ar";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
