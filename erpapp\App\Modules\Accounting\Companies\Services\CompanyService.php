<?php
namespace App\System\Companies\Services;

/**
 * خدمة الشركات داخل وحدة الشركات
 * تتعامل مع جميع عمليات الشركات
 */
class CompanyService
{
    /**
     * اتصال قاعدة البيانات
     */
    protected $db;

    /**
     * Constructor
     */
    public function __construct()
    {
        global $db;
        $this->db = $db;
    }

    /**
     * الحصول على معلومات الاشتراك للشركة
     *
     * @param int $company_id معرف الشركة
     * @return array|null
     */
    public function getCompanySubscriptionInfo($company_id)
    {
        try {
            $stmt = $this->db->prepare("
                SELECT
                    s.subscription_id,
                    s.plan_id,
                    s.start_date,
                    s.end_date,
                    s.status,
                    s.is_auto_renew,
                    s.billing_cycle,
                    sp.plan_name_ar,
                    sp.plan_name_en,
                    sp.price_monthly,
                    sp.price_yearly,
                    sp.features
                FROM subscriptions s
                LEFT JOIN subscription_plans sp ON s.plan_id = sp.plan_id
                WHERE s.company_id = :company_id
                AND s.status IN ('active', 'trial')
                ORDER BY s.created_at DESC
                LIMIT 1
            ");

            $stmt->bindParam(':company_id', $company_id, \PDO::PARAM_INT);
            $stmt->execute();

            return $stmt->fetch(\PDO::FETCH_ASSOC) ?: null;

        } catch (\Exception $e) {
            // في حالة عدم وجود جدول الاشتراكات أو خطأ، إرجاع null
            error_log("Error fetching subscription info: " . $e->getMessage());
            return null;
        }
    }

    /**
     * التحقق من وجود اشتراك نشط للشركة
     *
     * @param int $company_id معرف الشركة
     * @return bool
     */
    public function hasActiveSubscription($company_id)
    {
        $subscription = $this->getCompanySubscriptionInfo($company_id);
        return $subscription !== null && $subscription['status'] === 'active';
    }

    /**
     * الحصول على حالة الاشتراك للشركة
     *
     * @param int $company_id معرف الشركة
     * @return string
     */
    public function getSubscriptionStatus($company_id)
    {
        $subscription = $this->getCompanySubscriptionInfo($company_id);

        if (!$subscription) {
            return 'no_subscription';
        }

        return $subscription['status'];
    }
}
