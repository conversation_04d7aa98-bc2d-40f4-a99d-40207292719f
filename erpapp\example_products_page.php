<?php
/**
 * مثال على صفحة المنتجات مع استخدام نظام الصلاحيات
 * هذا مثال يوضح كيفية إخفاء/إظهار الأزرار حسب الصلاحيات
 */

// تحميل النظام
require_once 'loader.php';

// التحقق من الصلاحية الأساسية للوصول للصفحة
PermissionManager::checkCurrentRoute();

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المنتجات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="fas fa-box text-primary"></i> إدارة المنتجات</h1>
                
                <!-- زر إضافة منتج جديد - يظهر فقط إذا كان لديه صلاحية إنشاء -->
                <?php if (canCreate('products')): ?>
                <a href="inventory/products/create" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة منتج جديد
                </a>
                <?php endif; ?>
            </div>

            <!-- رسالة إذا لم يكن لديه صلاحية عرض -->
            <?php if (!canView('products')): ?>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                ليس لديك صلاحية لعرض المنتجات
            </div>
            <?php else: ?>

            <!-- جدول المنتجات -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">قائمة المنتجات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>اسم المنتج</th>
                                    <th>الفئة</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                // هنا يتم جلب المنتجات من قاعدة البيانات
                                // مثال على بيانات تجريبية
                                $products = [
                                    ['id' => 1, 'name' => 'لابتوب Dell', 'category' => 'إلكترونيات', 'price' => 2500, 'quantity' => 10, 'status' => 'متوفر'],
                                    ['id' => 2, 'name' => 'ماوس لاسلكي', 'category' => 'إكسسوارات', 'price' => 50, 'quantity' => 25, 'status' => 'متوفر'],
                                    ['id' => 3, 'name' => 'كيبورد ميكانيكي', 'category' => 'إكسسوارات', 'price' => 150, 'quantity' => 0, 'status' => 'غير متوفر'],
                                ];
                                
                                foreach ($products as $product):
                                ?>
                                <tr>
                                    <td><?= $product['id'] ?></td>
                                    <td><?= htmlspecialchars($product['name']) ?></td>
                                    <td><?= htmlspecialchars($product['category']) ?></td>
                                    <td><?= number_format($product['price']) ?> ريال</td>
                                    <td>
                                        <span class="badge bg-<?= $product['quantity'] > 0 ? 'success' : 'danger' ?>">
                                            <?= $product['quantity'] ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $product['status'] === 'متوفر' ? 'success' : 'warning' ?>">
                                            <?= $product['status'] ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <!-- زر العرض - يظهر دائماً إذا كان لديه صلاحية عرض -->
                                            <?php if (canView('products')): ?>
                                            <a href="inventory/products/<?= $product['id'] ?>" 
                                               class="btn btn-sm btn-outline-info" 
                                               title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php endif; ?>

                                            <!-- زر التعديل - يظهر فقط إذا كان لديه صلاحية تعديل -->
                                            <?php if (canEdit('products')): ?>
                                            <a href="inventory/products/<?= $product['id'] ?>/edit" 
                                               class="btn btn-sm btn-outline-warning" 
                                               title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php endif; ?>

                                            <!-- زر الحذف - يظهر فقط إذا كان لديه صلاحية حذف -->
                                            <?php if (canDelete('products')): ?>
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-danger" 
                                                    onclick="confirmDelete(<?= $product['id'] ?>)"
                                                    title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <?php endif; ?>

                                            <!-- زر الاعتماد - يظهر فقط إذا كان لديه صلاحية اعتماد -->
                                            <?php if (canApprove('products')): ?>
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-success" 
                                                    onclick="approveProduct(<?= $product['id'] ?>)"
                                                    title="اعتماد">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <?php endif; ?>
        </div>
    </div>

    <!-- أزرار إضافية في أسفل الصفحة -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">إجراءات إضافية</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- إجراءات الإنشاء -->
                        <?php if (canCreate('products')): ?>
                        <div class="col-md-3">
                            <div class="d-grid">
                                <a href="inventory/products/import" class="btn btn-outline-primary">
                                    <i class="fas fa-upload"></i> استيراد منتجات
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-grid">
                                <a href="inventory/products/bulk-create" class="btn btn-outline-success">
                                    <i class="fas fa-plus-square"></i> إضافة مجمعة
                                </a>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- إجراءات التعديل -->
                        <?php if (canEdit('products')): ?>
                        <div class="col-md-3">
                            <div class="d-grid">
                                <button type="button" class="btn btn-outline-warning" onclick="bulkEdit()">
                                    <i class="fas fa-edit"></i> تعديل مجمع
                                </button>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- إجراءات الحذف -->
                        <?php if (canDelete('products')): ?>
                        <div class="col-md-3">
                            <div class="d-grid">
                                <button type="button" class="btn btn-outline-danger" onclick="bulkDelete()">
                                    <i class="fas fa-trash"></i> حذف مجمع
                                </button>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات الصلاحيات للمطور -->
    <?php if (defined('DEBUG_MODE') && DEBUG_MODE): ?>
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">معلومات الصلاحيات (وضع التطوير)</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>صلاحيات المستخدم للمنتجات:</h6>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>عرض (View)</span>
                                    <span class="badge bg-<?= canView('products') ? 'success' : 'danger' ?>">
                                        <?= canView('products') ? 'متاح' : 'غير متاح' ?>
                                    </span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>إنشاء (Create)</span>
                                    <span class="badge bg-<?= canCreate('products') ? 'success' : 'danger' ?>">
                                        <?= canCreate('products') ? 'متاح' : 'غير متاح' ?>
                                    </span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>تعديل (Edit)</span>
                                    <span class="badge bg-<?= canEdit('products') ? 'success' : 'danger' ?>">
                                        <?= canEdit('products') ? 'متاح' : 'غير متاح' ?>
                                    </span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>حذف (Delete)</span>
                                    <span class="badge bg-<?= canDelete('products') ? 'success' : 'danger' ?>">
                                        <?= canDelete('products') ? 'متاح' : 'غير متاح' ?>
                                    </span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>اعتماد (Approve)</span>
                                    <span class="badge bg-<?= canApprove('products') ? 'success' : 'danger' ?>">
                                        <?= canApprove('products') ? 'متاح' : 'غير متاح' ?>
                                    </span>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>معلومات المستخدم:</h6>
                            <?php $user = current_user(); ?>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">
                                    <strong>المستخدم:</strong> <?= $user['UserName'] ?? 'غير محدد' ?>
                                </li>
                                <li class="list-group-item">
                                    <strong>الشركة:</strong> <?= $user['current_company_id'] ?? 'غير محدد' ?>
                                </li>
                                <li class="list-group-item">
                                    <strong>مالك الشركة:</strong> 
                                    <span class="badge bg-<?= isCompanyOwner() ? 'success' : 'secondary' ?>">
                                        <?= isCompanyOwner() ? 'نعم' : 'لا' ?>
                                    </span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
function confirmDelete(productId) {
    if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
        // هنا يتم إرسال طلب الحذف
        alert('تم حذف المنتج رقم ' + productId);
    }
}

function approveProduct(productId) {
    if (confirm('هل تريد اعتماد هذا المنتج؟')) {
        // هنا يتم إرسال طلب الاعتماد
        alert('تم اعتماد المنتج رقم ' + productId);
    }
}

function bulkEdit() {
    alert('تعديل مجمع للمنتجات المحددة');
}

function bulkDelete() {
    if (confirm('هل أنت متأكد من حذف المنتجات المحددة؟')) {
        alert('تم حذف المنتجات المحددة');
    }
}
</script>

</body>
</html>
