<?php
/**
 * دوال مساعدة للسايدبار
 * جلب الوحدات والبرامج للشركة الحالية
 */

/**
 * جلب الوحدات المنزلة للشركة مع البرامج التابعة لها (مع فحص الصلاحيات)
 */
function getCompanyModulesWithPrograms($companyId = null) {
    if (!$companyId) {
        $user = current_user();
        if (!$user) return [];
        $companyId = $user['current_company_id'];
    }

    if (!$companyId) return [];

    try {
        global $db;

        // جلب الوحدات المنزلة للشركة
        $stmt = $db->prepare("
            SELECT
                sm.module_id,
                sm.module_code,
                sm.module_name_ar,
                sm.module_name_en,
                sm.base_url,
                sm.icon_name,
                sm.module_type,
                sm.display_order,
                cm.is_active as module_active,
                cm.license_expires_at
            FROM company_modules cm
            JOIN system_modules sm ON sm.module_id = cm.module_id
            WHERE cm.company_id = ?
            AND cm.is_active = 1
            AND sm.is_active = 1
            AND (cm.license_expires_at IS NULL OR cm.license_expires_at > NOW())
            ORDER BY sm.display_order, sm.module_name_ar
        ");

        $stmt->execute([$companyId]);
        $modules = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // فلترة الوحدات حسب صلاحيات الاشتراك
        $allowedModules = [];
        foreach ($modules as $module) {
            // التحقق من صلاحيات الاشتراك
            if (function_exists('isModuleAllowedInSubscription') &&
                !isModuleAllowedInSubscription($module['module_code'], $companyId)) {
                continue; // تخطي الوحدة غير المسموحة
            }

            // جلب البرامج للوحدة
            $module['programs'] = getModulePrograms($module['module_id'], $companyId);

            // إضافة الوحدة فقط إذا كان لديها برامج يمكن الوصول إليها
            if (!empty($module['programs'])) {
                $allowedModules[] = $module;
            }
        }

        return $allowedModules;

    } catch (Exception $e) {
        error_log("خطأ في جلب وحدات الشركة: " . $e->getMessage());
        return [];
    }
}

/**
 * جلب البرامج الفرعية لوحدة معينة (مع فحص الصلاحيات)
 */
function getModulePrograms($moduleId, $companyId) {
    try {
        global $db;

        // جلب البرامج الرئيسية أولاً
        $stmt = $db->prepare("
            SELECT
                mp.program_id,
                mp.program_name_ar,
                mp.program_name_en,
                mp.program_code,
                mp.page_url,
                mp.icon_name,
                mp.program_type,
                mp.parent_program_id,
                mp.display_order
            FROM module_programs mp
            WHERE mp.module_id = ?
            AND mp.company_id = ?
            AND mp.is_active = 1
            ORDER BY mp.program_type DESC, mp.display_order, mp.program_name_ar
        ");

        $stmt->execute([$moduleId, $companyId]);
        $programs = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // تنظيم البرامج في هيكل هرمي مع فحص الصلاحيات
        $mainPrograms = [];
        $subPrograms = [];

        foreach ($programs as $program) {
            // فحص صلاحية المستخدم للبرنامج
            if (!hasUserPermissionForProgram($program['program_id'], 'view')) {
                continue; // تخطي البرنامج إذا لم يكن للمستخدم صلاحية
            }

            if ($program['program_type'] === 'Main') {
                $program['sub_programs'] = [];
                $mainPrograms[$program['program_id']] = $program;
            } else {
                $subPrograms[] = $program;
            }
        }

        // ربط البرامج الفرعية بالرئيسية (مع فحص الصلاحيات)
        foreach ($subPrograms as $subProgram) {
            $parentId = $subProgram['parent_program_id'];
            if ($parentId && isset($mainPrograms[$parentId])) {
                $mainPrograms[$parentId]['sub_programs'][] = $subProgram;
            } else {
                // إذا لم يكن له برنامج رئيسي، أضفه كبرنامج مستقل
                $mainPrograms['sub_' . $subProgram['program_id']] = $subProgram;
            }
        }

        // إزالة البرامج الرئيسية التي لا تحتوي على برامج فرعية مسموحة
        $filteredPrograms = [];
        foreach ($mainPrograms as $program) {
            if ($program['program_type'] === 'Main') {
                // إذا كان برنامج رئيسي وله صفحة، أو له برامج فرعية
                if (!empty($program['page_url']) || !empty($program['sub_programs'])) {
                    $filteredPrograms[] = $program;
                }
            } else {
                // برنامج فرعي مستقل
                $filteredPrograms[] = $program;
            }
        }

        return $filteredPrograms;

    } catch (Exception $e) {
        error_log("خطأ في جلب برامج الوحدة: " . $e->getMessage());
        return [];
    }
}

/**
 * التحقق من صلاحية المستخدم لبرنامج معين
 */
function hasUserPermissionForProgram($programId, $action = 'view') {
    $user = current_user();
    if (!$user) return false;

    $companyId = $user['current_company_id'];
    if (!$companyId) return false;

    // مالك الشركة له صلاحيات كاملة
    if (function_exists('isCompanyOwner') && isCompanyOwner($user['UserID'], $companyId)) {
        return true;
    }

    try {
        global $db;

        // التحقق من وجود صلاحيات محددة للمستخدم
        $stmt = $db->prepare("
            SELECT p.*
            FROM permissions p
            JOIN company_users cu ON cu.position_id = p.position_id
            WHERE cu.user_id = ?
            AND cu.company_id = ?
            AND cu.status = 'accepted'
            AND cu.user_status = 'active'
            AND p.program_id = ?
            AND p.company_id = ?
            AND cu.position_id IS NOT NULL
        ");

        $stmt->execute([$user['UserID'], $companyId, $programId, $companyId]);
        $permission = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$permission) {
            // إذا لم توجد صلاحيات محددة، فحص إذا كان المستخدم عضو في الشركة بدون منصب (مالك)
            $stmt = $db->prepare("
                SELECT cu.position_id, c.OwnerID
                FROM company_users cu
                JOIN companies c ON c.CompanyID = cu.company_id
                WHERE cu.user_id = ?
                AND cu.company_id = ?
                AND cu.status = 'accepted'
                AND cu.user_status = 'active'
            ");

            $stmt->execute([$user['UserID'], $companyId]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$result) return false;

            // إذا كان مالك الشركة أو ليس له منصب محدد، السماح بجميع الصلاحيات
            if ($result['OwnerID'] == $user['UserID'] || $result['position_id'] === null) {
                return true;
            }

            // إذا كان عضو عادي بدون صلاحيات محددة، السماح بالعرض فقط
            return $action === 'view';
        }

        switch ($action) {
            case 'view': return (bool)$permission['can_view'];
            case 'create': return (bool)$permission['can_create'];
            case 'edit': return (bool)$permission['can_edit'];
            case 'delete': return (bool)$permission['can_delete'];
            case 'approve': return (bool)$permission['can_approve'];
            default: return false;
        }

    } catch (Exception $e) {
        error_log("خطأ في التحقق من صلاحية البرنامج: " . $e->getMessage());
        return false;
    }
}

/**
 * تحديد نوع الصفحة الحالية (system أم module) بشكل ديناميكي
 */
function getCurrentPageType() {
    // الحصول على المسار الحالي
    $currentPath = $_SERVER['REQUEST_URI'];
    $path = parse_url($currentPath, PHP_URL_PATH);

    // إزالة البادئة إذا وجدت
    $path = str_replace('/erpapp/', '', $path);
    $path = trim($path, '/');

    // تحديد النوع بناءً على بداية المسار
    if (empty($path)) {
        // الصفحة الرئيسية - افتراضي system
        return 'system';
    }

    // الحصول على الجزء الأول من المسار
    $firstSegment = explode('/', $path)[0];

    // فحص إذا كان المسار ينتمي لوحدة من قاعدة البيانات
    if (isModulePath($firstSegment)) {
        return 'module';
    }

    // افتراضي: system للمسارات الأخرى
    return 'system';
}

/**
 * فحص إذا كان المسار ينتمي لوحدة من قاعدة البيانات
 */
function isModulePath($pathSegment) {
    try {
        global $db;

        // الحصول على المستخدم والشركة الحالية
        $user = current_user();
        if (!$user) return false;

        $companyId = $user['current_company_id'];
        if (!$companyId) return false;

        // فحص إذا كان المسار يطابق أي وحدة منزلة للشركة
        $stmt = $db->prepare("
            SELECT COUNT(*) as count
            FROM company_modules cm
            JOIN system_modules sm ON sm.module_id = cm.module_id
            WHERE cm.company_id = ?
            AND cm.is_active = 1
            AND sm.is_active = 1
            AND (cm.license_expires_at IS NULL OR cm.license_expires_at > NOW())
            AND (sm.module_code = ? OR sm.base_url = ?)
        ");

        $stmt->execute([$companyId, $pathSegment, $pathSegment]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result['count'] > 0) {
            // التحقق من صلاحيات الاشتراك
            if (function_exists('isModuleAllowedInSubscription')) {
                return isModuleAllowedInSubscription($pathSegment, $companyId);
            }
            return true;
        }

        return false;

    } catch (Exception $e) {
        error_log("خطأ في فحص مسار الوحدة: " . $e->getMessage());
        return false;
    }
}

/**
 * جلب الوحدة الحالية من المسار
 */
function getCurrentModule() {
    $currentPath = $_SERVER['REQUEST_URI'];
    $path = parse_url($currentPath, PHP_URL_PATH);

    // إزالة البادئة إذا وجدت
    $path = str_replace('/erpapp/', '', $path);
    $path = trim($path, '/');

    $segments = explode('/', $path);
    return isset($segments[0]) ? $segments[0] : null;
}

/**
 * التحقق من أن المسار الحالي نشط
 */
function isCurrentPath($url) {
    $currentPath = $_SERVER['REQUEST_URI'];
    $currentPath = str_replace('/erpapp/', '', $currentPath);
    $currentPath = trim($currentPath, '/');

    $url = trim($url, '/');

    return $currentPath === $url || strpos($currentPath, $url . '/') === 0;
}

/**
 * إنشاء رابط مع البادئة الصحيحة
 */
function moduleUrl($path) {
    $baseUrl = rtrim(dirname($_SERVER['SCRIPT_NAME']), '/');
    return $baseUrl . '/' . ltrim($path, '/');
}
