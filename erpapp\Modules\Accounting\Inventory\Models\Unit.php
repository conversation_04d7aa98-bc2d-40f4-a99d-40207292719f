<?php
namespace Modules\Accounting\Inventory\Models;

/**
 * Unit Model
 * نموذج وحدات القياس
 */
class Unit {
    /**
     * Database connection
     *
     * @var \PDO
     */
    protected $db;

    /**
     * Constructor
     */
    public function __construct() {
        global $db;
        $this->db = $db;
    }

    /**
     * الحصول على جميع وحدات القياس للشركة
     *
     * @param int $company_id معرف الشركة
     * @param bool $active_only الحصول على الوحدات النشطة فقط
     * @return array
     */
    public function getAll($company_id, $active_only = true) {
        $where_clause = 'company_id = :company_id';
        if ($active_only) {
            $where_clause .= ' AND is_active = 1';
        }

        $sql = "
            SELECT u.*, 
                   base_unit.unit_name_ar as base_unit_name_ar,
                   base_unit.unit_name_en as base_unit_name_en,
                   COUNT(p.product_id) as product_count
            FROM inventory_units u
            LEFT JOIN inventory_units base_unit ON u.base_unit_id = base_unit.unit_id
            LEFT JOIN inventory_products p ON u.unit_id = p.unit_id AND p.is_active = 1
            WHERE {$where_clause}
            GROUP BY u.unit_id
            ORDER BY u.is_base_unit DESC, u.unit_name_ar ASC
        ";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':company_id', $company_id);
        $stmt->execute();
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على وحدة قياس بواسطة المعرف
     *
     * @param int $unit_id معرف الوحدة
     * @param int $company_id معرف الشركة
     * @return array|false
     */
    public function getById($unit_id, $company_id) {
        $sql = "
            SELECT u.*, 
                   base_unit.unit_name_ar as base_unit_name_ar,
                   base_unit.unit_name_en as base_unit_name_en,
                   creator.UserName as created_by_name,
                   updater.UserName as updated_by_name
            FROM inventory_units u
            LEFT JOIN inventory_units base_unit ON u.base_unit_id = base_unit.unit_id
            LEFT JOIN users creator ON u.created_by = creator.UserID
            LEFT JOIN users updater ON u.updated_by = updater.UserID
            WHERE u.unit_id = :unit_id AND u.company_id = :company_id
        ";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':unit_id', $unit_id);
        $stmt->bindParam(':company_id', $company_id);
        $stmt->execute();
        return $stmt->fetch(\PDO::FETCH_ASSOC);
    }

    /**
     * إنشاء وحدة قياس جديدة
     *
     * @param array $data بيانات الوحدة
     * @return int|false معرف الوحدة الجديدة أو false في حالة الفشل
     */
    public function create($data) {
        $sql = "
            INSERT INTO inventory_units (
                company_id, module_code, unit_code, unit_name_ar, unit_name_en,
                unit_symbol_ar, unit_symbol_en, base_unit_id, conversion_factor,
                is_base_unit, is_active, created_by
            ) VALUES (
                :company_id, :module_code, :unit_code, :unit_name_ar, :unit_name_en,
                :unit_symbol_ar, :unit_symbol_en, :base_unit_id, :conversion_factor,
                :is_base_unit, :is_active, :created_by
            )
        ";

        $stmt = $this->db->prepare($sql);
        
        // تعيين القيم الافتراضية
        $data['module_code'] = $data['module_code'] ?? 'inventory';
        $data['is_active'] = $data['is_active'] ?? 1;
        $data['is_base_unit'] = $data['is_base_unit'] ?? 0;
        $data['conversion_factor'] = $data['conversion_factor'] ?? 1.0000;

        $stmt->bindParam(':company_id', $data['company_id']);
        $stmt->bindParam(':module_code', $data['module_code']);
        $stmt->bindParam(':unit_code', $data['unit_code']);
        $stmt->bindParam(':unit_name_ar', $data['unit_name_ar']);
        $stmt->bindParam(':unit_name_en', $data['unit_name_en']);
        $stmt->bindParam(':unit_symbol_ar', $data['unit_symbol_ar']);
        $stmt->bindParam(':unit_symbol_en', $data['unit_symbol_en']);
        $stmt->bindParam(':base_unit_id', $data['base_unit_id']);
        $stmt->bindParam(':conversion_factor', $data['conversion_factor']);
        $stmt->bindParam(':is_base_unit', $data['is_base_unit']);
        $stmt->bindParam(':is_active', $data['is_active']);
        $stmt->bindParam(':created_by', $data['created_by']);

        if ($stmt->execute()) {
            return $this->db->lastInsertId();
        }

        return false;
    }

    /**
     * تحديث وحدة قياس
     *
     * @param int $unit_id معرف الوحدة
     * @param array $data بيانات الوحدة
     * @param int $company_id معرف الشركة
     * @return bool
     */
    public function update($unit_id, $data, $company_id) {
        $sql = "
            UPDATE inventory_units SET
                unit_code = :unit_code,
                unit_name_ar = :unit_name_ar,
                unit_name_en = :unit_name_en,
                unit_symbol_ar = :unit_symbol_ar,
                unit_symbol_en = :unit_symbol_en,
                base_unit_id = :base_unit_id,
                conversion_factor = :conversion_factor,
                is_base_unit = :is_base_unit,
                is_active = :is_active,
                updated_by = :updated_by,
                updated_at = NOW()
            WHERE unit_id = :unit_id AND company_id = :company_id
        ";

        $stmt = $this->db->prepare($sql);
        
        $stmt->bindParam(':unit_id', $unit_id);
        $stmt->bindParam(':company_id', $company_id);
        $stmt->bindParam(':unit_code', $data['unit_code']);
        $stmt->bindParam(':unit_name_ar', $data['unit_name_ar']);
        $stmt->bindParam(':unit_name_en', $data['unit_name_en']);
        $stmt->bindParam(':unit_symbol_ar', $data['unit_symbol_ar']);
        $stmt->bindParam(':unit_symbol_en', $data['unit_symbol_en']);
        $stmt->bindParam(':base_unit_id', $data['base_unit_id']);
        $stmt->bindParam(':conversion_factor', $data['conversion_factor']);
        $stmt->bindParam(':is_base_unit', $data['is_base_unit']);
        $stmt->bindParam(':is_active', $data['is_active']);
        $stmt->bindParam(':updated_by', $data['updated_by']);

        return $stmt->execute();
    }

    /**
     * حذف وحدة قياس
     *
     * @param int $unit_id معرف الوحدة
     * @param int $company_id معرف الشركة
     * @return bool
     */
    public function delete($unit_id, $company_id) {
        // التحقق من عدم وجود منتجات تستخدم هذه الوحدة
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as product_count 
            FROM inventory_products 
            WHERE unit_id = :unit_id AND company_id = :company_id
        ");
        $stmt->bindParam(':unit_id', $unit_id);
        $stmt->bindParam(':company_id', $company_id);
        $stmt->execute();
        $result = $stmt->fetch(\PDO::FETCH_ASSOC);

        if ($result['product_count'] > 0) {
            return false; // لا يمكن حذف وحدة تستخدمها منتجات
        }

        // التحقق من عدم وجود وحدات فرعية
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as sub_unit_count 
            FROM inventory_units 
            WHERE base_unit_id = :unit_id AND company_id = :company_id
        ");
        $stmt->bindParam(':unit_id', $unit_id);
        $stmt->bindParam(':company_id', $company_id);
        $stmt->execute();
        $result = $stmt->fetch(\PDO::FETCH_ASSOC);

        if ($result['sub_unit_count'] > 0) {
            return false; // لا يمكن حذف وحدة أساسية لها وحدات فرعية
        }

        $stmt = $this->db->prepare("
            DELETE FROM inventory_units 
            WHERE unit_id = :unit_id AND company_id = :company_id
        ");
        $stmt->bindParam(':unit_id', $unit_id);
        $stmt->bindParam(':company_id', $company_id);

        return $stmt->execute();
    }

    /**
     * التحقق من وجود كود الوحدة
     *
     * @param string $unit_code كود الوحدة
     * @param int $company_id معرف الشركة
     * @param int|null $exclude_id معرف الوحدة المستثناة (للتحديث)
     * @return bool
     */
    public function isUnitCodeExists($unit_code, $company_id, $exclude_id = null) {
        $sql = "SELECT COUNT(*) as count FROM inventory_units WHERE unit_code = :unit_code AND company_id = :company_id";
        $params = [':unit_code' => $unit_code, ':company_id' => $company_id];

        if ($exclude_id) {
            $sql .= " AND unit_id != :exclude_id";
            $params[':exclude_id'] = $exclude_id;
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetch(\PDO::FETCH_ASSOC);

        return $result['count'] > 0;
    }

    /**
     * الحصول على الوحدات الأساسية فقط
     *
     * @param int $company_id معرف الشركة
     * @return array
     */
    public function getBaseUnits($company_id) {
        $sql = "
            SELECT * FROM inventory_units 
            WHERE company_id = :company_id 
            AND is_base_unit = 1 
            AND is_active = 1
            ORDER BY unit_name_ar ASC
        ";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':company_id', $company_id);
        $stmt->execute();
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * تحويل الكمية من وحدة إلى أخرى
     *
     * @param float $quantity الكمية
     * @param int $from_unit_id وحدة المصدر
     * @param int $to_unit_id وحدة الهدف
     * @param int $company_id معرف الشركة
     * @return float|false الكمية المحولة أو false في حالة الفشل
     */
    public function convertQuantity($quantity, $from_unit_id, $to_unit_id, $company_id) {
        if ($from_unit_id == $to_unit_id) {
            return $quantity;
        }

        // الحصول على معلومات الوحدتين
        $from_unit = $this->getById($from_unit_id, $company_id);
        $to_unit = $this->getById($to_unit_id, $company_id);

        if (!$from_unit || !$to_unit) {
            return false;
        }

        // التحقق من أن الوحدتين من نفس النوع الأساسي
        $from_base = $from_unit['is_base_unit'] ? $from_unit_id : $from_unit['base_unit_id'];
        $to_base = $to_unit['is_base_unit'] ? $to_unit_id : $to_unit['base_unit_id'];

        if ($from_base != $to_base) {
            return false; // لا يمكن التحويل بين وحدات من أنواع مختلفة
        }

        // تحويل إلى الوحدة الأساسية أولاً
        $base_quantity = $quantity * $from_unit['conversion_factor'];

        // ثم تحويل من الوحدة الأساسية إلى الوحدة المطلوبة
        $converted_quantity = $base_quantity / $to_unit['conversion_factor'];

        return $converted_quantity;
    }
}
