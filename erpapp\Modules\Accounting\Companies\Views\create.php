<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title">
                    <i class="fas fa-plus-circle"></i> <?= __('إنشاء شركة جديدة') ?>
                </h1>
                <a href="<?= base_url('companies') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> <?= __('العودة إلى قائمة الشركات') ?>
                </a>
            </div>
        </div>
    </div>

    <?php display_flash('company_error'); ?>

    <div class="row">
        <div class="col-lg-10 mx-auto">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-building me-2"></i> <?= __('إنشاء شركة جديدة') ?>
                    </h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('companies/store') ?>" method="post" enctype="multipart/form-data" id="company-form">
                        <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">

                        <!-- تبويبات النموذج -->
                        <ul class="nav nav-tabs mb-4" id="companyTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="basic-info-tab" data-bs-toggle="tab" data-bs-target="#basic-info" type="button" role="tab" aria-controls="basic-info" aria-selected="true">
                                    <i class="fas fa-info-circle me-1"></i> <?= __('المعلومات الأساسية') ?>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="contact-info-tab" data-bs-toggle="tab" data-bs-target="#contact-info" type="button" role="tab" aria-controls="contact-info" aria-selected="false">
                                    <i class="fas fa-address-card me-1"></i> <?= __('معلومات الاتصال') ?>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="additional-info-tab" data-bs-toggle="tab" data-bs-target="#additional-info" type="button" role="tab" aria-controls="additional-info" aria-selected="false">
                                    <i class="fas fa-clipboard-list me-1"></i> <?= __('معلومات إضافية') ?>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="social-media-tab" data-bs-toggle="tab" data-bs-target="#social-media" type="button" role="tab" aria-controls="social-media" aria-selected="false">
                                    <i class="fas fa-share-alt me-1"></i> <?= __('وسائل التواصل') ?>
                                </button>
                            </li>
                        </ul>

                        <!-- محتوى التبويبات -->
                        <div class="tab-content" id="companyTabsContent">
                            <!-- تبويب المعلومات الأساسية -->
                            <div class="tab-pane fade show active" id="basic-info" role="tabpanel" aria-labelledby="basic-info-tab">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="company_name" class="form-label"><?= __('اسم الشركة') ?> <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="company_name" name="company_name" required>
                                        <div class="form-text"><?= __('اسم الشركة باللغة العربية') ?></div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="company_name_en" class="form-label"><?= __('اسم الشركة (بالإنجليزية)') ?></label>
                                        <input type="text" class="form-control" id="company_name_en" name="company_name_en">
                                        <div class="form-text"><?= __('اسم الشركة باللغة الإنجليزية (اختياري)') ?></div>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="industry_type" class="form-label"><?= __('نوع النشاط التجاري') ?></label>
                                        <select class="form-select" id="industry_type" name="industry_type">
                                            <option value=""><?= __('اختر نوع النشاط') ?></option>
                                            <option value="retail"><?= __('تجارة التجزئة') ?></option>
                                            <option value="wholesale"><?= __('تجارة الجملة') ?></option>
                                            <option value="manufacturing"><?= __('التصنيع') ?></option>
                                            <option value="services"><?= __('الخدمات') ?></option>
                                            <option value="technology"><?= __('التكنولوجيا') ?></option>
                                            <option value="healthcare"><?= __('الرعاية الصحية') ?></option>
                                            <option value="education"><?= __('التعليم') ?></option>
                                            <option value="food"><?= __('المطاعم والأغذية') ?></option>
                                            <option value="construction"><?= __('البناء والمقاولات') ?></option>
                                            <option value="real_estate"><?= __('العقارات') ?></option>
                                            <option value="other"><?= __('أخرى') ?></option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="company_size" class="form-label"><?= __('حجم الشركة (عدد الموظفين)') ?></label>
                                        <select class="form-select" id="company_size" name="company_size">
                                            <option value=""><?= __('اختر حجم الشركة') ?></option>
                                            <option value="1-10">1-10</option>
                                            <option value="11-50">11-50</option>
                                            <option value="51-200">51-200</option>
                                            <option value="201-500">201-500</option>
                                            <option value="501-1000">501-1000</option>
                                            <option value="1000+">1000+</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="tax_id" class="form-label"><?= __('الرقم الضريبي') ?></label>
                                        <input type="text" class="form-control" id="tax_id" name="tax_id">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="foundation_date" class="form-label"><?= __('تاريخ التأسيس') ?></label>
                                        <input type="date" class="form-control" id="foundation_date" name="foundation_date">
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label for="company_logo" class="form-label"><?= __('شعار الشركة') ?></label>
                                    <input type="file" class="form-control" id="company_logo" name="company_logo" accept="image/jpeg,image/png,image/gif">
                                    <div class="form-text"><?= __('الحد الأقصى لحجم الملف: 5 ميجابايت. الصيغ المدعومة: JPG, PNG, GIF') ?></div>
                                    <div class="mt-2" id="logo-preview"></div>
                                </div>
                            </div>

                            <!-- تبويب معلومات الاتصال -->
                            <div class="tab-pane fade" id="contact-info" role="tabpanel" aria-labelledby="contact-info-tab">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="company_email" class="form-label"><?= __('البريد الإلكتروني للشركة') ?> <span class="text-danger">*</span></label>
                                        <input type="email" class="form-control" id="company_email" name="company_email" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="company_phone" class="form-label"><?= __('رقم هاتف الشركة') ?> <span class="text-danger">*</span></label>
                                        <input type="tel" class="form-control" id="company_phone" name="company_phone" required>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="company_address" class="form-label"><?= __('عنوان الشركة') ?></label>
                                    <textarea class="form-control" id="company_address" name="company_address" rows="3"></textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="company_website" class="form-label"><?= __('الموقع الإلكتروني') ?></label>
                                    <input type="url" class="form-control" id="company_website" name="company_website" placeholder="https://">
                                </div>
                            </div>

                            <!-- تبويب المعلومات الإضافية -->
                            <div class="tab-pane fade" id="additional-info" role="tabpanel" aria-labelledby="additional-info-tab">
                                <div class="mb-4">
                                    <label for="notes" class="form-label"><?= __('ملاحظات') ?></label>
                                    <textarea class="form-control" id="notes" name="notes" rows="4"></textarea>
                                    <div class="form-text"><?= __('أي معلومات إضافية عن الشركة') ?></div>
                                </div>

                                <div class="alert alert-info">
                                    <div class="d-flex">
                                        <div class="me-3">
                                            <i class="fas fa-info-circle fa-2x"></i>
                                        </div>
                                        <div>
                                            <h5><?= __('معلومات الفترة التجريبية') ?></h5>
                                            <p><?= __('ستبدأ الشركة بفترة تجريبية مجانية لمدة 14 يومًا. خلال هذه الفترة، يمكنك استخدام جميع ميزات النظام الأساسية.') ?></p>
                                            <p class="mb-0"><?= __('بعد انتهاء الفترة التجريبية، ستحتاج إلى اختيار خطة اشتراك مناسبة للاستمرار في استخدام النظام.') ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- تبويب وسائل التواصل الاجتماعي -->
                            <div class="tab-pane fade" id="social-media" role="tabpanel" aria-labelledby="social-media-tab">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="facebook" class="form-label">
                                            <i class="fab fa-facebook text-primary me-1"></i> <?= __('فيسبوك') ?>
                                        </label>
                                        <input type="url" class="form-control" id="facebook" name="social_media[facebook]" placeholder="https://facebook.com/yourcompany">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="twitter" class="form-label">
                                            <i class="fab fa-twitter text-info me-1"></i> <?= __('تويتر') ?>
                                        </label>
                                        <input type="url" class="form-control" id="twitter" name="social_media[twitter]" placeholder="https://twitter.com/yourcompany">
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="instagram" class="form-label">
                                            <i class="fab fa-instagram text-danger me-1"></i> <?= __('انستغرام') ?>
                                        </label>
                                        <input type="url" class="form-control" id="instagram" name="social_media[instagram]" placeholder="https://instagram.com/yourcompany">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="linkedin" class="form-label">
                                            <i class="fab fa-linkedin text-primary me-1"></i> <?= __('لينكد إن') ?>
                                        </label>
                                        <input type="url" class="form-control" id="linkedin" name="social_media[linkedin]" placeholder="https://linkedin.com/company/yourcompany">
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="youtube" class="form-label">
                                            <i class="fab fa-youtube text-danger me-1"></i> <?= __('يوتيوب') ?>
                                        </label>
                                        <input type="url" class="form-control" id="youtube" name="social_media[youtube]" placeholder="https://youtube.com/c/yourcompany">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="whatsapp" class="form-label">
                                            <i class="fab fa-whatsapp text-success me-1"></i> <?= __('واتساب') ?>
                                        </label>
                                        <input type="text" class="form-control" id="whatsapp" name="social_media[whatsapp]" placeholder="+966XXXXXXXXX">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <a href="<?= base_url('companies') ?>" class="btn btn-light me-md-2">
                                <i class="fas fa-times me-1"></i> <?= __('إلغاء') ?>
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> <?= __('إنشاء الشركة') ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من صحة النموذج قبل الإرسال
    const form = document.getElementById('company-form');
    form.addEventListener('submit', function(event) {
        let isValid = true;

        // التحقق من اسم الشركة
        const companyName = document.getElementById('company_name').value.trim();
        if (companyName === '') {
            isValid = false;
            document.getElementById('company_name').classList.add('is-invalid');
            // تبديل إلى التبويب المناسب
            document.getElementById('basic-info-tab').click();
        } else {
            document.getElementById('company_name').classList.remove('is-invalid');
        }

        // التحقق من البريد الإلكتروني
        const companyEmail = document.getElementById('company_email').value.trim();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (companyEmail === '' || !emailRegex.test(companyEmail)) {
            isValid = false;
            document.getElementById('company_email').classList.add('is-invalid');
            // تبديل إلى التبويب المناسب إذا لم يتم تبديله بالفعل
            if (companyName !== '') {
                document.getElementById('contact-info-tab').click();
            }
        } else {
            document.getElementById('company_email').classList.remove('is-invalid');
        }

        // التحقق من رقم الهاتف
        const companyPhone = document.getElementById('company_phone').value.trim();
        if (companyPhone === '') {
            isValid = false;
            document.getElementById('company_phone').classList.add('is-invalid');
            // تبديل إلى التبويب المناسب إذا لم يتم تبديله بالفعل
            if (companyName !== '' && (companyEmail !== '' && emailRegex.test(companyEmail))) {
                document.getElementById('contact-info-tab').click();
            }
        } else {
            document.getElementById('company_phone').classList.remove('is-invalid');
        }

        // التحقق من حجم الشعار
        const companyLogo = document.getElementById('company_logo');
        if (companyLogo.files.length > 0) {
            const fileSize = companyLogo.files[0].size;
            const maxSize = 5 * 1024 * 1024; // 5 ميجابايت

            if (fileSize > maxSize) {
                isValid = false;
                companyLogo.classList.add('is-invalid');
                document.getElementById('basic-info-tab').click();
                alert('حجم الشعار كبير جدًا. الحد الأقصى هو 5 ميجابايت.');
            } else {
                companyLogo.classList.remove('is-invalid');
            }
        }

        // التحقق من صحة روابط وسائل التواصل الاجتماعي
        const socialMediaInputs = document.querySelectorAll('input[name^="social_media"]');
        socialMediaInputs.forEach(input => {
            const value = input.value.trim();
            if (value !== '' && input.type === 'url' && !isValidUrl(value)) {
                isValid = false;
                input.classList.add('is-invalid');
                document.getElementById('social-media-tab').click();
            } else {
                input.classList.remove('is-invalid');
            }
        });

        if (!isValid) {
            event.preventDefault();
        }
    });

    // معاينة الشعار قبل الرفع
    const companyLogo = document.getElementById('company_logo');
    const logoPreview = document.getElementById('logo-preview');

    companyLogo.addEventListener('change', function() {
        logoPreview.innerHTML = '';

        if (this.files && this.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = document.createElement('img');
                img.src = e.target.result;
                img.classList.add('img-thumbnail', 'mt-2');
                img.style.maxHeight = '150px';
                logoPreview.appendChild(img);
            };
            reader.readAsDataURL(this.files[0]);
        }
    });

    // التحقق من صحة URL
    function isValidUrl(url) {
        try {
            new URL(url);
            return true;
        } catch (e) {
            return false;
        }
    }

    // تحويل وسائل التواصل الاجتماعي إلى JSON عند الإرسال
    form.addEventListener('submit', function() {
        const socialMediaInputs = document.querySelectorAll('input[name^="social_media"]');
        const socialMediaData = {};

        socialMediaInputs.forEach(input => {
            const key = input.name.match(/\[([^\]]+)\]/)[1];
            const value = input.value.trim();
            if (value !== '') {
                socialMediaData[key] = value;
            }
        });

        // إنشاء حقل مخفي لتخزين البيانات كـ JSON
        if (Object.keys(socialMediaData).length > 0) {
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = 'social_media_json';
            hiddenInput.value = JSON.stringify(socialMediaData);
            form.appendChild(hiddenInput);
        }
    });
});
</script>
