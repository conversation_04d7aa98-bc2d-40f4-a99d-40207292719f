<?php
namespace Modules\Accounting\Companies;

use App\Core\Module as BaseModule;

/**
 * وحدة الشركات - نسخة محاسبية
 */
class Module extends BaseModule
{
    /**
     * اسم الوحدة
     */
    protected $name = 'Companies';

    /**
     * وصف الوحدة
     */
    protected $description = 'إدارة الشركات والمؤسسات';

    /**
     * إصدار الوحدة
     */
    protected $version = '1.0.0';

    /**
     * الوحدات المطلوبة
     */
    protected $dependencies = ['System.Users'];

    /**
     * تسجيل المسارات الخاصة بالوحدة
     */
    public function registerRoutes()
    {
        // تسجيل مسارات الشركات مع بادئة erpapp
        add_route('GET', '/erpapp/companies', 'Modules\Accounting\Companies\Controllers\CompanyController@index');
        add_route('GET', '/erpapp/companies/create', 'Modules\Accounting\Companies\Controllers\CompanyController@create');
        add_route('POST', '/erpapp/companies/store', 'Modules\Accounting\Companies\Controllers\CompanyController@store');
        add_route('GET', '/erpapp/companies/{id}', 'Modules\Accounting\Companies\Controllers\CompanyController@show');
        add_route('GET', '/erpapp/companies/{id}/edit', 'Modules\Accounting\Companies\Controllers\CompanyController@edit');
        add_route('POST', '/erpapp/companies/{id}/update', 'Modules\Accounting\Companies\Controllers\CompanyController@update');
        add_route('POST', '/erpapp/companies/{id}/deactivate', 'Modules\Accounting\Companies\Controllers\CompanyController@deactivate');
        add_route('POST', '/erpapp/companies/{id}/activate', 'Modules\Accounting\Companies\Controllers\CompanyController@activate');
        add_route('POST', '/erpapp/companies/{id}/delete', 'Modules\Accounting\Companies\Controllers\CompanyController@delete');

        // مسارات الدعوات
        add_route('POST', '/erpapp/companies/{id}/invite', 'Modules\Accounting\Companies\Controllers\CompanyController@invite');
        add_route('GET', '/erpapp/companies/{id}/accept-invitation', 'Modules\Accounting\Companies\Controllers\CompanyController@acceptInvitation');
        add_route('GET', '/erpapp/companies/{id}/reject-invitation', 'Modules\Accounting\Companies\Controllers\CompanyController@rejectInvitation');

        // مسارات المستخدمين
        add_route('GET', '/erpapp/companies/{id}/users', 'Modules\Accounting\Companies\Controllers\CompanyController@users');
        add_route('POST', '/erpapp/companies/{id}/users/{user_id}/remove', 'Modules\Accounting\Companies\Controllers\CompanyController@removeUser');
        add_route('POST', '/erpapp/companies/{id}/users/{user_id}/change-role', 'Modules\Accounting\Companies\Controllers\CompanyController@changeUserRole');
    }

    /**
     * تهيئة الوحدة
     */
    public function boot()
    {
        // تسجيل المسارات
        $this->registerRoutes();

        // تسجيل الخدمات
        $this->registerServices();
    }

    /**
     * تسجيل الخدمات
     */
    protected function registerServices()
    {
        // يمكن تسجيل خدمات إضافية هنا
    }

    /**
     * التحقق من صلاحيات الوحدة
     */
    public function hasPermission($user_id, $company_id)
    {
        // التحقق من أن المستخدم لديه صلاحية إدارة الشركات
        return true;
    }

    /**
     * الحصول على معلومات الوحدة
     */
    public function getInfo()
    {
        return [
            'name' => $this->name,
            'description' => $this->description,
            'version' => $this->version,
            'dependencies' => $this->dependencies,
            'category' => 'Accounting',
            'icon' => 'fas fa-building',
            'status' => 'active'
        ];
    }

    /**
     * الحصول على صلاحيات الوحدة
     */
    public function getPermissions()
    {
        return [
            'companies_view' => 'عرض الشركات',
            'companies_create' => 'إنشاء شركات',
            'companies_edit' => 'تعديل الشركات',
            'companies_delete' => 'حذف الشركات',
            'companies_activate' => 'تفعيل الشركات',
            'companies_deactivate' => 'إلغاء تفعيل الشركات',
            'companies_invite_users' => 'دعوة المستخدمين',
            'companies_manage_users' => 'إدارة مستخدمي الشركة',
            'companies_settings' => 'إعدادات الشركة'
        ];
    }

    /**
     * الحصول على عناصر القائمة
     */
    public function getMenuItems()
    {
        return [
            [
                'title' => 'إدارة الشركات',
                'icon' => 'fas fa-building',
                'url' => '/erpapp/companies',
                'permission' => 'companies_view',
                'children' => [
                    [
                        'title' => 'قائمة الشركات',
                        'icon' => 'fas fa-list',
                        'url' => '/erpapp/companies',
                        'permission' => 'companies_view'
                    ],
                    [
                        'title' => 'إضافة شركة',
                        'icon' => 'fas fa-plus',
                        'url' => '/erpapp/companies/create',
                        'permission' => 'companies_create'
                    ]
                ]
            ]
        ];
    }
}
