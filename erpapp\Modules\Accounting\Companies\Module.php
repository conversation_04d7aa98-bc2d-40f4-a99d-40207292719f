<?php
namespace Modules\Accounting\Companies;

use App\Core\Module as BaseModule;

/**
 * وحدة الشركات
 */
class Module extends BaseModule
{
    /**
     * تسجيل المسارات الخاصة بالوحدة
     */
    public function registerRoutes()
    {
        // تسجيل مسارات الشركات
        add_route('GET', '/erpapp/companies', 'Modules\Accounting\Companies\Controllers\CompanyController@index');
        add_route('GET', '/erpapp/companies/create', 'Modules\Accounting\Companies\Controllers\CompanyController@create');
        add_route('POST', '/erpapp/companies/store', 'Modules\Accounting\Companies\Controllers\CompanyController@store');
        add_route('GET', '/erpapp/companies/{id}', 'Modules\Accounting\Companies\Controllers\CompanyController@show');
        add_route('GET', '/erpapp/companies/{id}/edit', 'Modules\Accounting\Companies\Controllers\CompanyController@edit');
        add_route('POST', '/erpapp/companies/{id}/update', 'Modules\Accounting\Companies\Controllers\CompanyController@update');
        add_route('POST', '/erpapp/companies/{id}/deactivate', 'Modules\Accounting\Companies\Controllers\CompanyController@deactivate');
        add_route('POST', '/erpapp/companies/{id}/activate', 'Modules\Accounting\Companies\Controllers\CompanyController@activate');
        add_route('POST', '/erpapp/companies/{id}/delete', 'Modules\Accounting\Companies\Controllers\CompanyController@delete');

        // مسارات الدعوات
        add_route('POST', '/erpapp/companies/{id}/invite', 'Modules\Accounting\Companies\Controllers\CompanyController@invite');
        add_route('GET', '/erpapp/companies/{id}/accept-invitation', 'Modules\Accounting\Companies\Controllers\CompanyController@acceptInvitation');
        add_route('GET', '/erpapp/companies/{id}/reject-invitation', 'Modules\Accounting\Companies\Controllers\CompanyController@rejectInvitation');

        // مسارات المستخدمين
        add_route('GET', '/erpapp/companies/{id}/users', 'Modules\Accounting\Companies\Controllers\CompanyController@users');
        add_route('POST', '/erpapp/companies/{id}/users/{user_id}/remove', 'Modules\Accounting\Companies\Controllers\CompanyController@removeUser');
        add_route('POST', '/erpapp/companies/{id}/users/{user_id}/change-role', 'Modules\Accounting\Companies\Controllers\CompanyController@changeUserRole');
    }
}
