<?php
namespace Modules\Accounting\Inventory\Controllers;

use Modules\Accounting\Inventory\Models\Stock;
use Modules\Accounting\Inventory\Models\Product;
use Modules\Accounting\Inventory\Models\Warehouse;
use Modules\Accounting\Inventory\Models\Category;

/**
 * Stock Controller
 * متحكم أرصدة المخزون
 */
class StockController {
    
    private $stockModel;
    private $productModel;
    private $warehouseModel;
    private $categoryModel;

    public function __construct() {
        $this->stockModel = new Stock();
        $this->productModel = new Product();
        $this->warehouseModel = new Warehouse();
        $this->categoryModel = new Category();
    }

    /**
     * عرض قائمة أرصدة المخزون
     */
    public function index() {
        try {
            $company_id = get_user_company_id();
            
            // الحصول على الفلاتر من الطلب
            $filters = [
                'warehouse_id' => $_GET['warehouse_id'] ?? '',
                'category_id' => $_GET['category_id'] ?? '',
                'need_reorder' => isset($_GET['need_reorder']) ? (bool)$_GET['need_reorder'] : false,
                'out_of_stock' => isset($_GET['out_of_stock']) ? (bool)$_GET['out_of_stock'] : false,
                'search' => $_GET['search'] ?? ''
            ];

            // إزالة الفلاتر الفارغة
            $filters = array_filter($filters, function($value) {
                return $value !== '' && $value !== null && $value !== false;
            });

            $stocks = $this->stockModel->getAll($company_id, $filters);
            $warehouses = $this->warehouseModel->getAll($company_id);
            $categories = $this->categoryModel->getAll($company_id);

            $data = [
                'title' => 'أرصدة المخزون',
                'stocks' => $stocks,
                'warehouses' => $warehouses,
                'categories' => $categories,
                'filters' => $filters,
                'total_count' => count($stocks)
            ];

            load_view('Modules/Accounting/Inventory/Views/stock/index', $data);

        } catch (Exception $e) {
            error_log("Error in StockController::index: " . $e->getMessage());
            show_error('حدث خطأ أثناء تحميل أرصدة المخزون');
        }
    }

    /**
     * عرض تفاصيل رصيد منتج في مخزن معين
     */
    public function show($product_id, $warehouse_id) {
        try {
            $company_id = get_user_company_id();
            $stock = $this->stockModel->getStock($product_id, $warehouse_id, $company_id);

            if (!$stock) {
                show_404('رصيد المخزون غير موجود');
                return;
            }

            $data = [
                'title' => 'تفاصيل رصيد المخزون',
                'stock' => $stock
            ];

            load_view('Modules/Accounting/Inventory/Views/stock/show', $data);

        } catch (Exception $e) {
            error_log("Error in StockController::show: " . $e->getMessage());
            show_error('حدث خطأ أثناء تحميل تفاصيل رصيد المخزون');
        }
    }

    /**
     * عرض نموذج تعديل الكمية
     */
    public function adjust($product_id, $warehouse_id) {
        try {
            $company_id = get_user_company_id();
            $stock = $this->stockModel->getStock($product_id, $warehouse_id, $company_id);

            if (!$stock) {
                show_404('رصيد المخزون غير موجود');
                return;
            }

            $data = [
                'title' => 'تعديل رصيد المخزون',
                'stock' => $stock,
                'adjustment_types' => [
                    'increase' => 'زيادة',
                    'decrease' => 'نقص',
                    'set' => 'تعيين'
                ]
            ];

            load_view('Modules/Accounting/Inventory/Views/stock/adjust', $data);

        } catch (Exception $e) {
            error_log("Error in StockController::adjust: " . $e->getMessage());
            show_error('حدث خطأ أثناء تحميل نموذج تعديل الرصيد');
        }
    }

    /**
     * تنفيذ تعديل الكمية
     */
    public function processAdjustment($product_id, $warehouse_id) {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                redirect('/inventory/stock');
                return;
            }

            $company_id = get_user_company_id();
            $user_id = get_current_user_id();

            // التحقق من وجود الرصيد
            $stock = $this->stockModel->getStock($product_id, $warehouse_id, $company_id);
            if (!$stock) {
                show_404('رصيد المخزون غير موجود');
                return;
            }

            // التحقق من صحة البيانات
            $validation_errors = $this->validateAdjustmentData($_POST);
            
            if (!empty($validation_errors)) {
                set_flash_message('error', implode('<br>', $validation_errors));
                $this->adjust($product_id, $warehouse_id);
                return;
            }

            $adjustment_type = $_POST['adjustment_type'];
            $quantity = (float)$_POST['quantity'];
            $cost = (float)($_POST['cost'] ?? 0);
            $notes = $_POST['notes'] ?? '';

            $quantity_change = 0;
            $current_quantity = $stock['quantity_on_hand'];

            switch ($adjustment_type) {
                case 'increase':
                    $quantity_change = $quantity;
                    break;
                case 'decrease':
                    $quantity_change = -$quantity;
                    break;
                case 'set':
                    $quantity_change = $quantity - $current_quantity;
                    break;
            }

            // التحقق من عدم جعل الرصيد سالباً
            if ($current_quantity + $quantity_change < 0) {
                set_flash_message('error', 'لا يمكن أن يكون الرصيد سالباً');
                $this->adjust($product_id, $warehouse_id);
                return;
            }

            $success = $this->stockModel->adjustQuantity($product_id, $warehouse_id, $company_id, $quantity_change, $cost, $user_id);

            if ($success) {
                // TODO: إضافة حركة مخزون لتسجيل التعديل
                set_flash_message('success', 'تم تعديل رصيد المخزون بنجاح');
                redirect('/inventory/stock');
            } else {
                set_flash_message('error', 'حدث خطأ أثناء تعديل رصيد المخزون');
                $this->adjust($product_id, $warehouse_id);
            }

        } catch (Exception $e) {
            error_log("Error in StockController::processAdjustment: " . $e->getMessage());
            set_flash_message('error', 'حدث خطأ أثناء تعديل رصيد المخزون');
            $this->adjust($product_id, $warehouse_id);
        }
    }

    /**
     * حجز كمية من المخزون
     */
    public function reserve($product_id, $warehouse_id) {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                redirect('/inventory/stock');
                return;
            }

            $company_id = get_user_company_id();
            $user_id = get_current_user_id();

            $quantity = (float)($_POST['quantity'] ?? 0);

            if ($quantity <= 0) {
                set_flash_message('error', 'الكمية يجب أن تكون أكبر من الصفر');
                redirect('/inventory/stock');
                return;
            }

            $success = $this->stockModel->reserveQuantity($product_id, $warehouse_id, $company_id, $quantity, $user_id);

            if ($success) {
                set_flash_message('success', 'تم حجز الكمية بنجاح');
            } else {
                set_flash_message('error', 'لا توجد كمية كافية متاحة للحجز');
            }

            redirect('/inventory/stock');

        } catch (Exception $e) {
            error_log("Error in StockController::reserve: " . $e->getMessage());
            set_flash_message('error', 'حدث خطأ أثناء حجز الكمية');
            redirect('/inventory/stock');
        }
    }

    /**
     * إلغاء حجز كمية من المخزون
     */
    public function unreserve($product_id, $warehouse_id) {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                redirect('/inventory/stock');
                return;
            }

            $company_id = get_user_company_id();
            $user_id = get_current_user_id();

            $quantity = (float)($_POST['quantity'] ?? 0);

            if ($quantity <= 0) {
                set_flash_message('error', 'الكمية يجب أن تكون أكبر من الصفر');
                redirect('/inventory/stock');
                return;
            }

            $success = $this->stockModel->unreserveQuantity($product_id, $warehouse_id, $company_id, $quantity, $user_id);

            if ($success) {
                set_flash_message('success', 'تم إلغاء حجز الكمية بنجاح');
            } else {
                set_flash_message('error', 'لا توجد كمية محجوزة كافية لإلغاء الحجز');
            }

            redirect('/inventory/stock');

        } catch (Exception $e) {
            error_log("Error in StockController::unreserve: " . $e->getMessage());
            set_flash_message('error', 'حدث خطأ أثناء إلغاء حجز الكمية');
            redirect('/inventory/stock');
        }
    }

    /**
     * عرض إجمالي رصيد منتج في جميع المخازن
     */
    public function totalStock($product_id) {
        try {
            $company_id = get_user_company_id();
            $product = $this->productModel->getById($product_id, $company_id);

            if (!$product) {
                show_404('المنتج غير موجود');
                return;
            }

            $total_stock = $this->stockModel->getTotalStock($product_id, $company_id);
            $stocks = $this->stockModel->getAll($company_id, ['product_id' => $product_id]);

            $data = [
                'title' => 'إجمالي رصيد المنتج: ' . $product['product_name_ar'],
                'product' => $product,
                'total_stock' => $total_stock,
                'stocks' => $stocks
            ];

            load_view('Modules/Accounting/Inventory/Views/stock/total', $data);

        } catch (Exception $e) {
            error_log("Error in StockController::totalStock: " . $e->getMessage());
            show_error('حدث خطأ أثناء تحميل إجمالي رصيد المنتج');
        }
    }

    /**
     * تقرير المنتجات النافدة
     */
    public function outOfStock() {
        try {
            $company_id = get_user_company_id();
            $stocks = $this->stockModel->getAll($company_id, ['out_of_stock' => true]);

            $data = [
                'title' => 'المنتجات النافدة',
                'stocks' => $stocks,
                'total_count' => count($stocks)
            ];

            load_view('Modules/Accounting/Inventory/Views/stock/out_of_stock', $data);

        } catch (Exception $e) {
            error_log("Error in StockController::outOfStock: " . $e->getMessage());
            show_error('حدث خطأ أثناء تحميل المنتجات النافدة');
        }
    }

    /**
     * تقرير المنتجات التي تحتاج إعادة طلب
     */
    public function needReorder() {
        try {
            $company_id = get_user_company_id();
            $stocks = $this->stockModel->getAll($company_id, ['need_reorder' => true]);

            $data = [
                'title' => 'المنتجات التي تحتاج إعادة طلب',
                'stocks' => $stocks,
                'total_count' => count($stocks)
            ];

            load_view('Modules/Accounting/Inventory/Views/stock/need_reorder', $data);

        } catch (Exception $e) {
            error_log("Error in StockController::needReorder: " . $e->getMessage());
            show_error('حدث خطأ أثناء تحميل المنتجات التي تحتاج إعادة طلب');
        }
    }

    /**
     * التحقق من صحة بيانات تعديل الرصيد
     */
    private function validateAdjustmentData($data) {
        $errors = [];

        if (empty($data['adjustment_type'])) {
            $errors[] = 'نوع التعديل مطلوب';
        }

        if (!in_array($data['adjustment_type'], ['increase', 'decrease', 'set'])) {
            $errors[] = 'نوع التعديل غير صحيح';
        }

        if (empty($data['quantity']) || !is_numeric($data['quantity'])) {
            $errors[] = 'الكمية مطلوبة ويجب أن تكون رقماً';
        }

        if (!empty($data['quantity']) && (float)$data['quantity'] <= 0) {
            $errors[] = 'الكمية يجب أن تكون أكبر من الصفر';
        }

        if (!empty($data['cost']) && !is_numeric($data['cost'])) {
            $errors[] = 'التكلفة يجب أن تكون رقماً';
        }

        if (!empty($data['cost']) && (float)$data['cost'] < 0) {
            $errors[] = 'التكلفة لا يمكن أن تكون سالبة';
        }

        return $errors;
    }
}
