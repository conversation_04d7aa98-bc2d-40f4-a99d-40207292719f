# ===========================================
# ERP System Security Configuration
# ===========================================

# منع عرض محتوى المجلدات
Options -Indexes

# منع الوصول للملفات الحساسة
<Files "*.env">
    Order Allow,Deny
    Deny from all
</Files>

<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.log">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.sql">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.bak">
    Order Allow,Deny
    Deny from all
</Files>

# منع الوصول للمجلدات الحساسة
RedirectMatch 403 ^/erpapp/config/.*$
RedirectMatch 403 ^/erpapp/storage/logs/.*$
RedirectMatch 403 ^/erpapp/App/Core/.*$
RedirectMatch 403 ^/erpapp/App/System/.*$
RedirectMatch 403 ^/erpapp/Modules/.*$
RedirectMatch 403 ^/erpapp/App/Controllers/.*$
RedirectMatch 403 ^/erpapp/App/Helpers/.*$
RedirectMatch 403 ^/erpapp/App/Routes/.*$

# Security Headers
<IfModule mod_headers.c>
    Header always set X-Frame-Options "DENY"
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
    Header always unset X-Powered-By
    Header always unset Server

    # HTTPS Security Headers
    <If "%{HTTPS} == 'on'">
        Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    </If>
</IfModule>

# منع Hotlinking للصور
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{HTTP_REFERER} !^$
    RewriteCond %{HTTP_REFERER} !^https?://(www\.)?yourdomain\.com [NC]
    RewriteRule \.(jpg|jpeg|png|gif|bmp|svg)$ - [F,L]
</IfModule>

<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # ===========================================
    # Inventory Module Routes
    # ===========================================

    # Dashboard
    RewriteRule ^inventory/?$ Modules/Accounting/Inventory/Controllers/InventoryController.php?action=index [L,QSA]
    RewriteRule ^inventory/dashboard/?$ Modules/Accounting/Inventory/Controllers/InventoryController.php?action=dashboard [L,QSA]

    # Products Routes
    RewriteRule ^inventory/products/?$ Modules/Accounting/Inventory/Controllers/ProductController.php?action=index [L,QSA]
    RewriteRule ^inventory/products/create/?$ Modules/Accounting/Inventory/Controllers/ProductController.php?action=create [L,QSA]
    RewriteRule ^inventory/products/([0-9]+)/?$ Modules/Accounting/Inventory/Controllers/ProductController.php?action=show&id=$1 [L,QSA]
    RewriteRule ^inventory/products/([0-9]+)/edit/?$ Modules/Accounting/Inventory/Controllers/ProductController.php?action=edit&id=$1 [L,QSA]
    RewriteRule ^inventory/products/([0-9]+)/update/?$ Modules/Accounting/Inventory/Controllers/ProductController.php?action=update&id=$1 [L,QSA]
    RewriteRule ^inventory/products/([0-9]+)/delete/?$ Modules/Accounting/Inventory/Controllers/ProductController.php?action=delete&id=$1 [L,QSA]
    RewriteRule ^inventory/products/reorder/?$ Modules/Accounting/Inventory/Controllers/ProductController.php?action=reorder [L,QSA]

    # Categories Routes
    RewriteRule ^inventory/categories/?$ Modules/Accounting/Inventory/Controllers/CategoryController.php?action=index [L,QSA]
    RewriteRule ^inventory/categories/create/?$ Modules/Accounting/Inventory/Controllers/CategoryController.php?action=create [L,QSA]
    RewriteRule ^inventory/categories/([0-9]+)/?$ Modules/Accounting/Inventory/Controllers/CategoryController.php?action=show&id=$1 [L,QSA]
    RewriteRule ^inventory/categories/([0-9]+)/edit/?$ Modules/Accounting/Inventory/Controllers/CategoryController.php?action=edit&id=$1 [L,QSA]
    RewriteRule ^inventory/categories/([0-9]+)/update/?$ Modules/Accounting/Inventory/Controllers/CategoryController.php?action=update&id=$1 [L,QSA]
    RewriteRule ^inventory/categories/([0-9]+)/delete/?$ Modules/Accounting/Inventory/Controllers/CategoryController.php?action=delete&id=$1 [L,QSA]
    RewriteRule ^inventory/categories/hierarchy/?$ Modules/Accounting/Inventory/Controllers/CategoryController.php?action=hierarchy [L,QSA]

    # Warehouses Routes
    RewriteRule ^inventory/warehouses/?$ Modules/Accounting/Inventory/Controllers/WarehouseController.php?action=index [L,QSA]
    RewriteRule ^inventory/warehouses/create/?$ Modules/Accounting/Inventory/Controllers/WarehouseController.php?action=create [L,QSA]
    RewriteRule ^inventory/warehouses/([0-9]+)/?$ Modules/Accounting/Inventory/Controllers/WarehouseController.php?action=show&id=$1 [L,QSA]
    RewriteRule ^inventory/warehouses/([0-9]+)/edit/?$ Modules/Accounting/Inventory/Controllers/WarehouseController.php?action=edit&id=$1 [L,QSA]

    # Stock Routes
    RewriteRule ^inventory/stock/?$ Modules/Accounting/Inventory/Controllers/StockController.php?action=index [L,QSA]
    RewriteRule ^inventory/stock/out-of-stock/?$ Modules/Accounting/Inventory/Controllers/StockController.php?action=outOfStock [L,QSA]
    RewriteRule ^inventory/stock/need-reorder/?$ Modules/Accounting/Inventory/Controllers/StockController.php?action=needReorder [L,QSA]

    # Reports Routes
    RewriteRule ^inventory/reports/?$ Modules/Accounting/Inventory/Controllers/InventoryController.php?action=reports [L,QSA]
    RewriteRule ^inventory/reports/([a-zA-Z_]+)/?$ Modules/Accounting/Inventory/Controllers/ReportController.php?action=$1 [L,QSA]

    # Export Routes
    RewriteRule ^inventory/export/([a-zA-Z_]+)/?$ Modules/Accounting/Inventory/Controllers/InventoryController.php?action=export&type=$1 [L,QSA]

    # Search and Settings
    RewriteRule ^inventory/search/?$ Modules/Accounting/Inventory/Controllers/InventoryController.php?action=search [L,QSA]
    RewriteRule ^inventory/settings/?$ Modules/Accounting/Inventory/Controllers/InventoryController.php?action=settings [L,QSA]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>
