<?php
namespace App\System\Companies;

use App\Core\Module as BaseModule;

/**
 * وحدة الشركات
 */
class Module extends BaseModule
{
    /**
     * تسجيل المسارات الخاصة بالوحدة
     */
    public function registerRoutes()
    {
        // تسجيل مسارات الشركات
        add_route('GET', '/companies', 'App\System\Companies\Controllers\CompanyController@index');
        add_route('GET', '/companies/create', 'App\System\Companies\Controllers\CompanyController@create');
        add_route('POST', '/companies/store', 'App\System\Companies\Controllers\CompanyController@store');
        add_route('GET', '/companies/{id}', 'App\System\Companies\Controllers\CompanyController@show');
        add_route('GET', '/companies/{id}/edit', 'App\System\Companies\Controllers\CompanyController@edit');
        add_route('POST', '/companies/{id}/update', 'App\System\Companies\Controllers\CompanyController@update');
        add_route('POST', '/companies/{id}/deactivate', 'App\System\Companies\Controllers\CompanyController@deactivate');
        add_route('POST', '/companies/{id}/activate', 'App\System\Companies\Controllers\CompanyController@activate');
        add_route('POST', '/companies/{id}/delete', 'App\System\Companies\Controllers\CompanyController@delete');

        // مسارات الدعوات
        add_route('POST', '/companies/{id}/invite', 'App\System\Companies\Controllers\CompanyController@invite');
        add_route('GET', '/companies/{id}/accept-invitation', 'App\System\Companies\Controllers\CompanyController@acceptInvitation');
        add_route('GET', '/companies/{id}/reject-invitation', 'App\System\Companies\Controllers\CompanyController@rejectInvitation');

        // مسارات المستخدمين
        add_route('GET', '/companies/{id}/users', 'App\System\Companies\Controllers\CompanyController@users');
        add_route('POST', '/companies/{id}/users/{user_id}/remove', 'App\System\Companies\Controllers\CompanyController@removeUser');
        add_route('POST', '/companies/{id}/users/{user_id}/change-role', 'App\System\Companies\Controllers\CompanyController@changeUserRole');
    }
}
