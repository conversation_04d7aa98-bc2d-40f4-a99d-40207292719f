<?php
/**
 * مثال على كيفية استخدام نظام الصلاحيات الجديد
 */

// تحميل النظام (يتم تلقائياً في loader.php)
// require_once 'App/Helpers/permissions.php';

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>مثال على نظام الصلاحيات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-4">
    <h1>مثال على استخدام نظام الصلاحيات</h1>

    <!-- 1. عرض قائمة البرامج المتاحة للمستخدم -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>البرامج المتاحة للمستخدم</h5>
        </div>
        <div class="card-body">
            <?php
            $availablePrograms = getAvailablePrograms();
            if (!empty($availablePrograms)):
            ?>
            <div class="row">
                <?php foreach ($availablePrograms as $program): ?>
                <div class="col-md-4 mb-3">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="<?= $program['icon_name'] ?> fa-2x mb-2"></i>
                            <h6><?= $program['name_ar'] ?></h6>
                            <?php if (!empty($program['page_url'])): ?>
                            <a href="<?= base_url($program['page_url']) ?>" class="btn btn-sm btn-primary">
                                دخول
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php else: ?>
            <div class="alert alert-warning">
                لا توجد برامج متاحة لك حالياً
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- 2. مثال على صفحة المنتجات مع الصلاحيات -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>صفحة المنتجات (مع نظام الصلاحيات)</h5>
        </div>
        <div class="card-body">
            <!-- عرض زر الإضافة فقط إذا كان لديه صلاحية إنشاء -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6>قائمة المنتجات</h6>
                <?php showIfCan('inventory_management', 'create', '
                    <a href="'.base_url('inventory/products/create').'" class="btn btn-success">
                        <i class="fas fa-plus"></i> إضافة منتج جديد
                    </a>
                '); ?>
            </div>

            <!-- جدول المنتجات -->
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>اسم المنتج</th>
                        <th>السعر</th>
                        <th>الكمية</th>
                        <?php if (canEdit('inventory_management') || canDelete('inventory_management')): ?>
                        <th>الإجراءات</th>
                        <?php endif; ?>
                    </tr>
                </thead>
                <tbody>
                    <!-- مثال على منتج -->
                    <tr>
                        <td>منتج تجريبي</td>
                        <td>100 ر.س</td>
                        <td>50</td>
                        <?php if (canEdit('inventory_management') || canDelete('inventory_management')): ?>
                        <td>
                            <?php showIfCan('inventory_management', 'edit', '
                                <a href="#" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                            '); ?>
                            
                            <?php showIfCan('inventory_management', 'delete', '
                                <button class="btn btn-sm btn-danger" onclick="confirmDelete()">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                            '); ?>
                        </td>
                        <?php endif; ?>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 3. مثال على التحقق من الصلاحيات في JavaScript -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>معلومات الصلاحيات الحالية</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>صلاحيات المخزون:</h6>
                    <ul class="list-group">
                        <li class="list-group-item d-flex justify-content-between">
                            عرض المنتجات
                            <span class="badge bg-<?= canView('inventory_management') ? 'success' : 'danger' ?>">
                                <?= canView('inventory_management') ? 'متاح' : 'غير متاح' ?>
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            إضافة منتجات
                            <span class="badge bg-<?= canCreate('inventory_management') ? 'success' : 'danger' ?>">
                                <?= canCreate('inventory_management') ? 'متاح' : 'غير متاح' ?>
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            تعديل منتجات
                            <span class="badge bg-<?= canEdit('inventory_management') ? 'success' : 'danger' ?>">
                                <?= canEdit('inventory_management') ? 'متاح' : 'غير متاح' ?>
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            حذف منتجات
                            <span class="badge bg-<?= canDelete('inventory_management') ? 'success' : 'danger' ?>">
                                <?= canDelete('inventory_management') ? 'متاح' : 'غير متاح' ?>
                            </span>
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>صلاحيات أخرى:</h6>
                    <ul class="list-group">
                        <li class="list-group-item d-flex justify-content-between">
                            إدارة الشركات
                            <span class="badge bg-<?= canAccessProgram('company_management') ? 'success' : 'danger' ?>">
                                <?= canAccessProgram('company_management') ? 'متاح' : 'غير متاح' ?>
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            إدارة المستخدمين
                            <span class="badge bg-<?= canAccessProgram('user_management') ? 'success' : 'danger' ?>">
                                <?= canAccessProgram('user_management') ? 'متاح' : 'غير متاح' ?>
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            المحاسبة
                            <span class="badge bg-<?= canAccessProgram('accounting') ? 'success' : 'danger' ?>">
                                <?= canAccessProgram('accounting') ? 'متاح' : 'غير متاح' ?>
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            التقارير
                            <span class="badge bg-<?= canAccessProgram('reports') ? 'success' : 'danger' ?>">
                                <?= canAccessProgram('reports') ? 'متاح' : 'غير متاح' ?>
                            </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 4. مثال على استخدام الصلاحيات المخصصة -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>الصلاحيات المخصصة</h5>
        </div>
        <div class="card-body">
            <p>يمكن إضافة صلاحيات مخصصة في حقل CustomPermissions كـ JSON:</p>
            <pre><code>{
    "export_data": true,
    "import_data": false,
    "bulk_operations": true,
    "advanced_reports": false
}</code></pre>

            <div class="mt-3">
                <h6>مثال على الاستخدام:</h6>
                <?php showIfCan('inventory_management', 'export_data', '
                    <button class="btn btn-info me-2">
                        <i class="fas fa-download"></i> تصدير البيانات
                    </button>
                '); ?>
                
                <?php showIfCan('inventory_management', 'import_data', '
                    <button class="btn btn-warning me-2">
                        <i class="fas fa-upload"></i> استيراد البيانات
                    </button>
                '); ?>
                
                <?php showIfCan('inventory_management', 'bulk_operations', '
                    <button class="btn btn-secondary me-2">
                        <i class="fas fa-tasks"></i> عمليات مجمعة
                    </button>
                '); ?>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete() {
    if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
        alert('تم الحذف (مثال فقط)');
    }
}
</script>

</body>
</html>
