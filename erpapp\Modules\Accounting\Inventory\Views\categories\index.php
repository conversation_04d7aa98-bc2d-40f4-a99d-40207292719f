<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title">
                    <i class="fas fa-tags text-primary"></i> <?= $title ?? 'إدارة فئات المنتجات' ?>
                </h1>
                <div class="btn-group">
                    <a href="<?= base_url('inventory/categories/create') ?>" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-2"></i> فئة جديدة
                    </a>
                    <a href="<?= base_url('inventory/categories/hierarchy') ?>" class="btn btn-outline-info" target="_blank">
                        <i class="fas fa-sitemap me-2"></i> الهيكل الهرمي
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (isset($_SESSION['flash_message'])): ?>
        <div class="alert alert-<?= $_SESSION['flash_message']['type'] ?> alert-dismissible fade show" role="alert">
            <?= $_SESSION['flash_message']['message'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['flash_message']); ?>
    <?php endif; ?>

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي الفئات</h6>
                            <h3><?= number_format($total_count) ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-tags fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">الفئات النشطة</h6>
                            <h3><?= number_format(count(array_filter($categories, function($c) { return $c['is_active']; }))) ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">الفئات الرئيسية</h6>
                            <h3><?= number_format(count(array_filter($categories, function($c) { return empty($c['parent_category_id']); }))) ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-layer-group fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي المنتجات</h6>
                            <h3><?= number_format(array_sum(array_column($categories, 'product_count'))) ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-cube fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Categories Table -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">قائمة الفئات</h6>
                </div>
                <div class="card-body">
                    <?php if (empty($categories)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-tags fa-3x text-gray-300 mb-3"></i>
                            <h5 class="text-muted">لا توجد فئات</h5>
                            <p class="text-muted">لم يتم إنشاء أي فئات بعد</p>
                            <a href="<?= base_url('inventory/categories/create') ?>" class="btn btn-primary">
                                <i class="fas fa-plus-circle me-2"></i> إضافة فئة جديدة
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="categoriesTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الكود</th>
                                        <th>اسم الفئة</th>
                                        <th>الفئة الرئيسية</th>
                                        <th>ترتيب العرض</th>
                                        <th>عدد المنتجات</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    // ترتيب الفئات هرمياً
                                    $main_categories = array_filter($categories, function($c) { return empty($c['parent_category_id']); });
                                    $sub_categories = array_filter($categories, function($c) { return !empty($c['parent_category_id']); });
                                    
                                    foreach ($main_categories as $category): 
                                    ?>
                                        <tr class="table-light">
                                            <td>
                                                <strong><?= htmlspecialchars($category['category_code']) ?></strong>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?= htmlspecialchars($category['category_name_ar']) ?></strong>
                                                    <?php if (!empty($category['category_name_en'])): ?>
                                                        <br><small class="text-muted"><?= htmlspecialchars($category['category_name_en']) ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">فئة رئيسية</span>
                                            </td>
                                            <td><?= $category['display_order'] ?></td>
                                            <td>
                                                <span class="badge bg-info"><?= number_format($category['product_count']) ?></span>
                                            </td>
                                            <td>
                                                <?php if ($category['is_active']): ?>
                                                    <span class="badge bg-success">نشط</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">غير نشط</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="<?= base_url('inventory/categories/' . $category['category_id']) ?>" 
                                                       class="btn btn-outline-primary" title="عرض">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('inventory/categories/' . $category['category_id'] . '/edit') ?>" 
                                                       class="btn btn-outline-secondary" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-outline-danger" 
                                                            onclick="deleteCategory(<?= $category['category_id'] ?>)" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        
                                        <?php 
                                        // عرض الفئات الفرعية
                                        $current_sub_categories = array_filter($sub_categories, function($sc) use ($category) { 
                                            return $sc['parent_category_id'] == $category['category_id']; 
                                        });
                                        
                                        foreach ($current_sub_categories as $sub_category): 
                                        ?>
                                            <tr>
                                                <td>
                                                    <span class="ms-3"><?= htmlspecialchars($sub_category['category_code']) ?></span>
                                                </td>
                                                <td>
                                                    <div class="ms-3">
                                                        <i class="fas fa-arrow-right text-muted me-2"></i>
                                                        <?= htmlspecialchars($sub_category['category_name_ar']) ?>
                                                        <?php if (!empty($sub_category['category_name_en'])): ?>
                                                            <br><small class="text-muted ms-4"><?= htmlspecialchars($sub_category['category_name_en']) ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <small class="text-muted"><?= htmlspecialchars($category['category_name_ar']) ?></small>
                                                </td>
                                                <td><?= $sub_category['display_order'] ?></td>
                                                <td>
                                                    <span class="badge bg-info"><?= number_format($sub_category['product_count']) ?></span>
                                                </td>
                                                <td>
                                                    <?php if ($sub_category['is_active']): ?>
                                                        <span class="badge bg-success">نشط</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">غير نشط</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="<?= base_url('inventory/categories/' . $sub_category['category_id']) ?>" 
                                                           class="btn btn-outline-primary" title="عرض">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="<?= base_url('inventory/categories/' . $sub_category['category_id'] . '/edit') ?>" 
                                                           class="btn btn-outline-secondary" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <button type="button" class="btn btn-outline-danger" 
                                                                onclick="deleteCategory(<?= $sub_category['category_id'] ?>)" title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i> تأكيد الحذف
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من رغبتك في حذف هذه الفئة؟</p>
                <p class="text-danger"><strong>تحذير:</strong> لا يمكن حذف فئة تحتوي على منتجات أو فئات فرعية.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i> حذف
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function deleteCategory(categoryId) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `<?= base_url('inventory/categories/') ?>${categoryId}/delete`;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

// Initialize DataTable if available
document.addEventListener('DOMContentLoaded', function() {
    if (typeof $.fn.DataTable !== 'undefined') {
        $('#categoriesTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
            },
            "pageLength": 25,
            "order": [[3, "asc"], [1, "asc"]],
            "columnDefs": [
                { "orderable": false, "targets": -1 }
            ]
        });
    }
});
</script>
