<?php
/**
 * نظام الصلاحيات الموحد
 * يتم تحميله تلقائياً مع loader.php
 */

class PermissionManager 
{
    private static $userPermissions = null;
    private static $routePermissions = [];
    
    /**
     * تسجيل صلاحية مطلوبة لمسار معين
     */
    public static function requirePermission($route, $program, $action = 'view') 
    {
        self::$routePermissions[$route] = [
            'program' => $program,
            'action' => $action
        ];
    }
    
    /**
     * التحقق من الصلاحية للمسار الحالي
     */
    public static function checkCurrentRoute() 
    {
        $currentRoute = self::getCurrentRoute();
        
        // إذا كان المسار يتطلب صلاحية
        if (isset(self::$routePermissions[$currentRoute])) {
            $permission = self::$routePermissions[$currentRoute];
            
            if (!self::hasPermission($permission['program'], $permission['action'])) {
                self::handleNoPermission();
            }
        }
    }
    
    /**
     * التحقق من صلاحية المستخدم
     */
    public static function hasPermission($program, $action = 'view') 
    {
        // تحميل صلاحيات المستخدم إذا لم تكن محملة
        if (self::$userPermissions === null) {
            self::loadUserPermissions();
        }
        
        // إذا لم يكن المستخدم مسجل دخول
        if (!self::$userPermissions) {
            return false;
        }
        
        // التحقق من الصلاحية
        $key = $program . '_' . $action;
        return isset(self::$userPermissions[$key]) && self::$userPermissions[$key];
    }
    
    /**
     * تحميل صلاحيات المستخدم
     */
    private static function loadUserPermissions() 
    {
        $user = current_user();
        if (!$user) {
            self::$userPermissions = false;
            return;
        }
        
        $companyId = $user['current_company_id'];
        $userId = $user['UserID'];
        
        if (!$companyId) {
            self::$userPermissions = false;
            return;
        }
        
        try {
            global $db;
            
            // الحصول على صلاحيات المستخدم
            $stmt = $db->prepare("
                SELECT 
                    prog.name_en as program_name,
                    perm.CanView,
                    perm.CanCreate,
                    perm.CanEdit,
                    perm.CanDelete,
                    perm.CanApprove
                FROM permissions perm
                JOIN positions pos ON pos.PositionID = perm.PositionID
                JOIN company_users cu ON cu.position_id = pos.PositionID
                JOIN company_all_programs prog ON prog.program_id = perm.ProgramID
                WHERE cu.user_id = ? 
                AND cu.company_id = ? 
                AND cu.status = 'accepted'
                AND cu.user_status = 'active'
                AND perm.CompanyID = ?
            ");
            
            $stmt->execute([$userId, $companyId, $companyId]);
            $permissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // تنظيم الصلاحيات
            self::$userPermissions = [];
            foreach ($permissions as $perm) {
                $program = $perm['program_name'];
                self::$userPermissions[$program . '_view'] = (bool)$perm['CanView'];
                self::$userPermissions[$program . '_create'] = (bool)$perm['CanCreate'];
                self::$userPermissions[$program . '_edit'] = (bool)$perm['CanEdit'];
                self::$userPermissions[$program . '_delete'] = (bool)$perm['CanDelete'];
                self::$userPermissions[$program . '_approve'] = (bool)$perm['CanApprove'];
            }
            
        } catch (Exception $e) {
            error_log("خطأ في تحميل الصلاحيات: " . $e->getMessage());
            self::$userPermissions = false;
        }
    }
    
    /**
     * الحصول على المسار الحالي
     */
    private static function getCurrentRoute() 
    {
        $url = $_SERVER['REQUEST_URI'];
        
        // إزالة المعاملات
        $url = strtok($url, '?');
        
        // إزالة المسار الأساسي
        $base_path = parse_url(APP_URL, PHP_URL_PATH);
        if ($base_path && strpos($url, $base_path) === 0) {
            $url = substr($url, strlen($base_path));
        }
        
        // إزالة الشرطة المائلة من البداية والنهاية
        return trim($url, '/');
    }
    
    /**
     * التعامل مع عدم وجود صلاحية
     */
    private static function handleNoPermission() 
    {
        // إذا كان طلب AJAX
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            http_response_code(403);
            header('Content-Type: application/json');
            echo json_encode([
                'error' => true,
                'message' => 'ليس لديك صلاحية لهذا الإجراء'
            ]);
            exit;
        }
        
        // إذا كان طلب عادي - عرض صفحة خطأ
        http_response_code(403);
        include BASE_PATH . '/App/Views/errors/403.php';
        exit;
    }
}

/**
 * وظائف مساعدة سريعة
 */
function hasPermission($program, $action = 'view') {
    return PermissionManager::hasPermission($program, $action);
}

function canView($program) {
    return PermissionManager::hasPermission($program, 'view');
}

function canCreate($program) {
    return PermissionManager::hasPermission($program, 'create');
}

function canEdit($program) {
    return PermissionManager::hasPermission($program, 'edit');
}

function canDelete($program) {
    return PermissionManager::hasPermission($program, 'delete');
}

function canApprove($program) {
    return PermissionManager::hasPermission($program, 'approve');
}

function requirePermission($route, $program, $action = 'view') {
    PermissionManager::requirePermission($route, $program, $action);
}

function showIfCan($program, $action, $content) {
    if (PermissionManager::hasPermission($program, $action)) {
        echo $content;
    }
}

/**
 * تسجيل الصلاحيات المطلوبة للمسارات
 * يتم استدعاؤها من ملفات التوجيه
 */

// صلاحيات وحدة المخزون
requirePermission('inventory', 'inventory', 'view');
requirePermission('inventory/products', 'inventory', 'view');
requirePermission('inventory/products/create', 'inventory', 'create');
requirePermission('inventory/products/store', 'inventory', 'create');
requirePermission('inventory/products/edit', 'inventory', 'edit');
requirePermission('inventory/products/update', 'inventory', 'edit');
requirePermission('inventory/products/delete', 'inventory', 'delete');

// صلاحيات وحدة الشركات
requirePermission('companies', 'companies', 'view');
requirePermission('companies/create', 'companies', 'create');
requirePermission('companies/store', 'companies', 'create');
requirePermission('companies/edit', 'companies', 'edit');
requirePermission('companies/update', 'companies', 'edit');
requirePermission('companies/delete', 'companies', 'delete');

// صلاحيات وحدة المستخدمين
requirePermission('users', 'users', 'view');
requirePermission('users/create', 'users', 'create');
requirePermission('users/store', 'users', 'create');
requirePermission('users/edit', 'users', 'edit');
requirePermission('users/update', 'users', 'edit');
requirePermission('users/delete', 'users', 'delete');

// يمكن إضافة المزيد من المسارات هنا...
?>
