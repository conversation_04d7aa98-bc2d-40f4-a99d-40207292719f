<?php
/**
 * نظام الصلاحيات الموحد
 * يتم تحميله تلقائياً مع loader.php
 */

class PermissionManager
{
    private static $userPermissions = null;
    private static $routePermissions = [];

    /**
     * تسجيل صلاحية مطلوبة لمسار معين
     */
    public static function requirePermission($route, $program, $action = 'view')
    {
        self::$routePermissions[$route] = [
            'program' => $program,
            'action' => $action
        ];
    }

    /**
     * التحقق من الصلاحية للمسار الحالي
     */
    public static function checkCurrentRoute()
    {
        $currentRoute = self::getCurrentRoute();

        // إذا كان المسار يتطلب صلاحية
        if (isset(self::$routePermissions[$currentRoute])) {
            $permission = self::$routePermissions[$currentRoute];

            if (!self::hasPermission($permission['program'], $permission['action'])) {
                self::handleNoPermission();
            }
        }
    }

    /**
     * التحقق من صلاحية المستخدم
     */
    public static function hasPermission($program, $action = 'view')
    {
        // تحميل صلاحيات المستخدم إذا لم تكن محملة
        if (self::$userPermissions === null) {
            self::loadUserPermissions();
        }

        // إذا لم يكن المستخدم مسجل دخول
        if (!self::$userPermissions) {
            return false;
        }

        // التحقق من الصلاحية
        $key = $program . '_' . $action;
        return isset(self::$userPermissions[$key]) && self::$userPermissions[$key];
    }

    /**
     * تحميل صلاحيات المستخدم
     */
    private static function loadUserPermissions()
    {
        $user = current_user();
        if (!$user) {
            self::$userPermissions = false;
            return;
        }

        $companyId = $user['current_company_id'];
        $userId = $user['UserID'];

        if (!$companyId) {
            self::$userPermissions = false;
            return;
        }

        try {
            global $db;

            // الحصول على صلاحيات المستخدم من الجداول الفعلية
            $stmt = $db->prepare("
                SELECT
                    prog.name_en as program_name,
                    prog.page_url,
                    perm.CanView,
                    perm.CanCreate,
                    perm.CanEdit,
                    perm.CanDelete,
                    perm.CanApprove,
                    perm.CustomPermissions
                FROM permissions perm
                JOIN positions pos ON pos.PositionID = perm.PositionID
                JOIN company_users cu ON cu.position_id = pos.PositionID
                JOIN company_all_programs prog ON prog.program_id = perm.ProgramID
                WHERE cu.user_id = ?
                AND cu.company_id = ?
                AND cu.status = 'accepted'
                AND cu.user_status = 'active'
                AND perm.CompanyID = ?
                AND prog.company_id = ?
                AND prog.status_en = 'Active'
            ");

            $stmt->execute([$userId, $companyId, $companyId, $companyId]);
            $permissions = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // تنظيم الصلاحيات
            self::$userPermissions = [];
            foreach ($permissions as $perm) {
                $program = strtolower($perm['program_name']);

                // الصلاحيات الأساسية
                self::$userPermissions[$program . '_view'] = (bool)$perm['CanView'];
                self::$userPermissions[$program . '_create'] = (bool)$perm['CanCreate'];
                self::$userPermissions[$program . '_edit'] = (bool)$perm['CanEdit'];
                self::$userPermissions[$program . '_delete'] = (bool)$perm['CanDelete'];
                self::$userPermissions[$program . '_approve'] = (bool)$perm['CanApprove'];

                // الصلاحيات المخصصة (JSON)
                if (!empty($perm['CustomPermissions'])) {
                    $customPerms = json_decode($perm['CustomPermissions'], true);
                    if ($customPerms) {
                        foreach ($customPerms as $key => $value) {
                            self::$userPermissions[$program . '_' . $key] = (bool)$value;
                        }
                    }
                }

                // ربط المسارات بالصلاحيات
                if (!empty($perm['page_url'])) {
                    $pageUrl = trim($perm['page_url'], '/');
                    self::$userPermissions['url_' . $pageUrl] = (bool)$perm['CanView'];
                }
            }

        } catch (Exception $e) {
            error_log("خطأ في تحميل الصلاحيات: " . $e->getMessage());
            self::$userPermissions = false;
        }
    }

    /**
     * الحصول على المسار الحالي
     */
    private static function getCurrentRoute()
    {
        $url = $_SERVER['REQUEST_URI'];

        // إزالة المعاملات
        $url = strtok($url, '?');

        // إزالة المسار الأساسي
        $base_path = parse_url(APP_URL, PHP_URL_PATH);
        if ($base_path && strpos($url, $base_path) === 0) {
            $url = substr($url, strlen($base_path));
        }

        // إزالة الشرطة المائلة من البداية والنهاية
        return trim($url, '/');
    }

    /**
     * التعامل مع عدم وجود صلاحية
     */
    private static function handleNoPermission()
    {
        // إذا كان طلب AJAX
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            http_response_code(403);
            header('Content-Type: application/json');
            echo json_encode([
                'error' => true,
                'message' => 'ليس لديك صلاحية لهذا الإجراء'
            ]);
            exit;
        }

        // إذا كان طلب عادي - عرض صفحة خطأ
        http_response_code(403);
        include BASE_PATH . '/App/Views/errors/403.php';
        exit;
    }
}

/**
 * وظائف مساعدة سريعة
 */
function hasPermission($program, $action = 'view') {
    return PermissionManager::hasPermission($program, $action);
}

function canView($program) {
    return PermissionManager::hasPermission($program, 'view');
}

function canCreate($program) {
    return PermissionManager::hasPermission($program, 'create');
}

function canEdit($program) {
    return PermissionManager::hasPermission($program, 'edit');
}

function canDelete($program) {
    return PermissionManager::hasPermission($program, 'delete');
}

function canApprove($program) {
    return PermissionManager::hasPermission($program, 'approve');
}

function requirePermission($route, $program, $action = 'view') {
    PermissionManager::requirePermission($route, $program, $action);
}

function showIfCan($program, $action, $content) {
    if (PermissionManager::hasPermission($program, $action)) {
        echo $content;
    }
}

/**
 * التحقق من صلاحية مخصصة
 */
function hasCustomPermission($program, $customAction) {
    return PermissionManager::hasPermission($program, $customAction);
}

/**
 * الحصول على جميع صلاحيات المستخدم
 */
function getUserPermissions() {
    return PermissionManager::$userPermissions;
}

/**
 * التحقق من صلاحية المستخدم لبرنامج معين
 */
function canAccessProgram($program) {
    return PermissionManager::hasPermission($program, 'view');
}

/**
 * عرض قائمة البرامج المتاحة للمستخدم
 */
function getAvailablePrograms() {
    $user = current_user();
    if (!$user) return [];

    $companyId = $user['current_company_id'];
    $userId = $user['UserID'];

    if (!$companyId) return [];

    try {
        global $db;
        $stmt = $db->prepare("
            SELECT DISTINCT
                prog.program_id,
                prog.name_ar,
                prog.name_en,
                prog.icon_name,
                prog.page_url,
                prog.program_type,
                prog.parent_program_id,
                prog.display_order
            FROM company_all_programs prog
            JOIN permissions perm ON perm.ProgramID = prog.program_id
            JOIN positions pos ON pos.PositionID = perm.PositionID
            JOIN company_users cu ON cu.position_id = pos.PositionID
            WHERE cu.user_id = ?
            AND cu.company_id = ?
            AND cu.status = 'accepted'
            AND cu.user_status = 'active'
            AND perm.CompanyID = ?
            AND prog.company_id = ?
            AND prog.status_en = 'Active'
            AND perm.CanView = 1
            ORDER BY prog.display_order, prog.name_ar
        ");

        $stmt->execute([$userId, $companyId, $companyId, $companyId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);

    } catch (Exception $e) {
        error_log("خطأ في جلب البرامج المتاحة: " . $e->getMessage());
        return [];
    }
}

/**
 * تسجيل الصلاحيات المطلوبة للمسارات
 * يتم استدعاؤها من ملفات التوجيه
 *
 * ملاحظة: أسماء البرامج يجب أن تطابق name_en في جدول company_all_programs
 */

// صلاحيات وحدة المخزون (Inventory Management)
requirePermission('inventory', 'inventory_management', 'view');
requirePermission('inventory/products', 'inventory_management', 'view');
requirePermission('inventory/products/create', 'inventory_management', 'create');
requirePermission('inventory/products/store', 'inventory_management', 'create');
requirePermission('inventory/products/edit', 'inventory_management', 'edit');
requirePermission('inventory/products/update', 'inventory_management', 'edit');
requirePermission('inventory/products/delete', 'inventory_management', 'delete');

// صلاحيات إدارة الفئات
requirePermission('inventory/categories', 'inventory_management', 'view');
requirePermission('inventory/categories/create', 'inventory_management', 'create');
requirePermission('inventory/categories/store', 'inventory_management', 'create');
requirePermission('inventory/categories/edit', 'inventory_management', 'edit');
requirePermission('inventory/categories/update', 'inventory_management', 'edit');
requirePermission('inventory/categories/delete', 'inventory_management', 'delete');

// صلاحيات إدارة الوحدات
requirePermission('inventory/units', 'inventory_management', 'view');
requirePermission('inventory/units/create', 'inventory_management', 'create');
requirePermission('inventory/units/store', 'inventory_management', 'create');
requirePermission('inventory/units/edit', 'inventory_management', 'edit');
requirePermission('inventory/units/update', 'inventory_management', 'edit');
requirePermission('inventory/units/delete', 'inventory_management', 'delete');

// صلاحيات وحدة الشركات (Company Management)
requirePermission('companies', 'company_management', 'view');
requirePermission('companies/create', 'company_management', 'create');
requirePermission('companies/store', 'company_management', 'create');
requirePermission('companies/edit', 'company_management', 'edit');
requirePermission('companies/update', 'company_management', 'edit');
requirePermission('companies/delete', 'company_management', 'delete');

// صلاحيات وحدة المستخدمين (User Management)
requirePermission('users', 'user_management', 'view');
requirePermission('users/create', 'user_management', 'create');
requirePermission('users/store', 'user_management', 'create');
requirePermission('users/edit', 'user_management', 'edit');
requirePermission('users/update', 'user_management', 'edit');
requirePermission('users/delete', 'user_management', 'delete');

// صلاحيات وحدة المحاسبة (Accounting)
requirePermission('accounting', 'accounting', 'view');
requirePermission('accounting/invoices', 'accounting', 'view');
requirePermission('accounting/invoices/create', 'accounting', 'create');
requirePermission('accounting/invoices/edit', 'accounting', 'edit');
requirePermission('accounting/invoices/delete', 'accounting', 'delete');
requirePermission('accounting/invoices/approve', 'accounting', 'approve');

// صلاحيات وحدة الموارد البشرية (HR)
requirePermission('hr', 'hr_management', 'view');
requirePermission('hr/employees', 'hr_management', 'view');
requirePermission('hr/employees/create', 'hr_management', 'create');
requirePermission('hr/employees/edit', 'hr_management', 'edit');
requirePermission('hr/employees/delete', 'hr_management', 'delete');

// صلاحيات وحدة التقارير (Reports)
requirePermission('reports', 'reports', 'view');
requirePermission('reports/inventory', 'reports', 'view');
requirePermission('reports/financial', 'reports', 'view');
requirePermission('reports/hr', 'reports', 'view');

// صلاحيات الإعدادات (Settings)
requirePermission('settings', 'system_settings', 'view');
requirePermission('settings/positions', 'system_settings', 'edit');
requirePermission('settings/permissions', 'system_settings', 'edit');
requirePermission('settings/programs', 'system_settings', 'edit');

// يمكن إضافة المزيد من المسارات هنا...
?>
