<?php
/**
 * نظام الصلاحيات الموحد
 * يتم تحميله تلقائياً مع loader.php
 */

class PermissionManager
{
    private static $userPermissions = null;
    private static $routePermissions = [];
    private static $programRoutes = null;

    /**
     * تحميل مسارات البرامج من قاعدة البيانات
     */
    private static function loadProgramRoutes()
    {
        if (self::$programRoutes !== null) {
            return;
        }

        $user = current_user();
        if (!$user) {
            self::$programRoutes = [];
            return;
        }

        $companyId = $user['current_company_id'];
        if (!$companyId) {
            self::$programRoutes = [];
            return;
        }

        try {
            global $db;
            $stmt = $db->prepare("
                SELECT
                    name_en,
                    page_url,
                    second_page_url
                FROM company_all_programs
                WHERE company_id = ?
                AND status_en = 'Active'
                AND page_url IS NOT NULL
                AND page_url != ''
            ");

            $stmt->execute([$companyId]);
            $programs = $stmt->fetchAll(PDO::FETCH_ASSOC);

            self::$programRoutes = [];
            foreach ($programs as $program) {
                $programName = strtolower($program['name_en']);

                // المسار الأساسي
                if (!empty($program['page_url'])) {
                    $baseUrl = trim($program['page_url'], '/');
                    self::$programRoutes[$baseUrl] = [
                        'program' => $programName,
                        'action' => 'view'
                    ];

                    // إضافة المسارات الفرعية التلقائية
                    self::addSubRoutes($baseUrl, $programName);
                }

                // المسار الثانوي
                if (!empty($program['second_page_url'])) {
                    $secondUrl = trim($program['second_page_url'], '/');
                    self::$programRoutes[$secondUrl] = [
                        'program' => $programName,
                        'action' => 'view'
                    ];

                    self::addSubRoutes($secondUrl, $programName);
                }
            }

        } catch (Exception $e) {
            error_log("خطأ في تحميل مسارات البرامج: " . $e->getMessage());
            self::$programRoutes = [];
        }
    }

    /**
     * إضافة المسارات الفرعية التلقائية
     */
    private static function addSubRoutes($baseUrl, $programName)
    {
        $actions = [
            'create' => 'create',
            'store' => 'create',
            'edit' => 'edit',
            'update' => 'edit',
            'delete' => 'delete',
            'destroy' => 'delete',
            'show' => 'view',
            'index' => 'view'
        ];

        foreach ($actions as $route => $action) {
            // مسارات مباشرة
            self::$programRoutes[$baseUrl . '/' . $route] = [
                'program' => $programName,
                'action' => $action
            ];

            // مسارات مع معرف
            self::$programRoutes[$baseUrl . '/*/'. $route] = [
                'program' => $programName,
                'action' => $action
            ];
        }

        // مسارات فرعية شائعة
        $subModules = ['products', 'categories', 'units', 'items', 'reports'];
        foreach ($subModules as $subModule) {
            foreach ($actions as $route => $action) {
                self::$programRoutes[$baseUrl . '/' . $subModule] = [
                    'program' => $programName,
                    'action' => 'view'
                ];

                self::$programRoutes[$baseUrl . '/' . $subModule . '/' . $route] = [
                    'program' => $programName,
                    'action' => $action
                ];

                self::$programRoutes[$baseUrl . '/' . $subModule . '/*/'. $route] = [
                    'program' => $programName,
                    'action' => $action
                ];
            }
        }
    }

    /**
     * التحقق من الصلاحية للمسار الحالي
     */
    public static function checkCurrentRoute()
    {
        // التحقق الأساسي من تسجيل الدخول
        $user = current_user();
        if (!$user) {
            // السماح بالوصول للصفحات العامة فقط
            $publicRoutes = ['login', 'register', 'forgot-password', 'reset-password', '', 'home'];
            $currentRoute = self::getCurrentRoute();

            if (!in_array($currentRoute, $publicRoutes)) {
                self::handleNoPermission();
            }
            return;
        }

        // التحقق من وجود شركة حالية
        $companyId = $user['current_company_id'];
        if (!$companyId) {
            // السماح بصفحات اختيار الشركة فقط
            $companySelectionRoutes = ['companies/select', 'companies/join', 'companies/create', 'profile', 'logout'];
            $currentRoute = self::getCurrentRoute();

            if (!in_array($currentRoute, $companySelectionRoutes)) {
                self::handleNoPermission();
            }
            return;
        }

        // التحقق من أن المستخدم مضاف في الشركة
        if (!self::isCompanyOwner($user['UserID'], $companyId) && !self::isUserInCompany($user['UserID'], $companyId)) {
            self::handleNoPermission();
            return;
        }

        // تحميل مسارات البرامج
        self::loadProgramRoutes();

        $currentRoute = self::getCurrentRoute();

        // البحث عن تطابق مباشر
        if (isset(self::$programRoutes[$currentRoute])) {
            $permission = self::$programRoutes[$currentRoute];

            if (!self::hasPermission($permission['program'], $permission['action'])) {
                self::handleNoPermission();
            }
            return;
        }

        // البحث عن تطابق مع أنماط wildcard
        foreach (self::$programRoutes as $pattern => $permission) {
            if (strpos($pattern, '*') !== false) {
                $regex = str_replace('*', '[^/]+', $pattern);
                $regex = '#^' . $regex . '$#';

                if (preg_match($regex, $currentRoute)) {
                    if (!self::hasPermission($permission['program'], $permission['action'])) {
                        self::handleNoPermission();
                    }
                    return;
                }
            }
        }
    }

    /**
     * التحقق من أن المستخدم مضاف في الشركة
     */
    private static function isUserInCompany($userId, $companyId)
    {
        try {
            global $db;
            $stmt = $db->prepare("
                SELECT COUNT(*) as is_member
                FROM company_users
                WHERE user_id = ?
                AND company_id = ?
                AND status = 'accepted'
                AND user_status = 'active'
                AND position_id IS NOT NULL
            ");

            $stmt->execute([$userId, $companyId]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            return $result['is_member'] > 0;

        } catch (Exception $e) {
            error_log("خطأ في التحقق من عضوية المستخدم: " . $e->getMessage());
            return false;
        }
    }

    /**
     * التحقق من صلاحية المستخدم
     */
    public static function hasPermission($program, $action = 'view')
    {
        $user = current_user();
        if (!$user) {
            return false;
        }

        $companyId = $user['current_company_id'];
        if (!$companyId) {
            return false;
        }

        // التحقق من أن المستخدم هو مالك الشركة
        if (self::isCompanyOwner($user['UserID'], $companyId)) {
            return true; // مالك الشركة له صلاحيات كاملة
        }

        // تحميل صلاحيات المستخدم إذا لم تكن محملة
        if (self::$userPermissions === null) {
            self::loadUserPermissions();
        }

        // إذا لم تكن هناك صلاحيات محملة
        if (!self::$userPermissions) {
            return false;
        }

        // التحقق من الصلاحية
        $key = $program . '_' . $action;
        return isset(self::$userPermissions[$key]) && self::$userPermissions[$key];
    }

    /**
     * التحقق من أن المستخدم هو مالك الشركة
     */
    public static function isCompanyOwner($userId, $companyId)
    {
        try {
            global $db;

            // التحقق من جدول companies إذا كان المستخدم هو المالك
            $stmt = $db->prepare("
                SELECT COUNT(*) as is_owner
                FROM companies
                WHERE CompanyID = ?
                AND OwnerID = ?
                AND CompanyStatus IN ('Active', 'Trial')
            ");

            $stmt->execute([$companyId, $userId]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            return $result['is_owner'] > 0;

        } catch (Exception $e) {
            error_log("خطأ في التحقق من ملكية الشركة: " . $e->getMessage());
            return false;
        }
    }

    /**
     * تحميل صلاحيات المستخدم
     */
    private static function loadUserPermissions()
    {
        $user = current_user();
        if (!$user) {
            self::$userPermissions = false;
            return;
        }

        $companyId = $user['current_company_id'];
        $userId = $user['UserID'];

        if (!$companyId) {
            self::$userPermissions = false;
            return;
        }

        try {
            global $db;

            // الحصول على صلاحيات المستخدم من الجداول الفعلية
            $stmt = $db->prepare("
                SELECT
                    prog.name_en as program_name,
                    prog.page_url,
                    perm.CanView,
                    perm.CanCreate,
                    perm.CanEdit,
                    perm.CanDelete,
                    perm.CanApprove,
                    perm.CustomPermissions,
                    pos.PositionNameAR,
                    pos.PositionNameEN
                FROM company_users cu
                JOIN positions pos ON pos.PositionID = cu.position_id
                JOIN permissions perm ON perm.PositionID = pos.PositionID
                JOIN company_all_programs prog ON prog.program_id = perm.ProgramID
                WHERE cu.user_id = ?
                AND cu.company_id = ?
                AND cu.status = 'accepted'
                AND cu.user_status = 'active'
                AND cu.position_id IS NOT NULL
                AND perm.CompanyID = ?
                AND prog.company_id = ?
                AND prog.status_en = 'Active'
            ");

            $stmt->execute([$userId, $companyId, $companyId, $companyId]);
            $permissions = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // تنظيم الصلاحيات
            self::$userPermissions = [];
            foreach ($permissions as $perm) {
                $program = strtolower($perm['program_name']);

                // الصلاحيات الأساسية
                self::$userPermissions[$program . '_view'] = (bool)$perm['CanView'];
                self::$userPermissions[$program . '_create'] = (bool)$perm['CanCreate'];
                self::$userPermissions[$program . '_edit'] = (bool)$perm['CanEdit'];
                self::$userPermissions[$program . '_delete'] = (bool)$perm['CanDelete'];
                self::$userPermissions[$program . '_approve'] = (bool)$perm['CanApprove'];

                // الصلاحيات المخصصة (JSON)
                if (!empty($perm['CustomPermissions'])) {
                    $customPerms = json_decode($perm['CustomPermissions'], true);
                    if ($customPerms) {
                        foreach ($customPerms as $key => $value) {
                            self::$userPermissions[$program . '_' . $key] = (bool)$value;
                        }
                    }
                }

                // ربط المسارات بالصلاحيات
                if (!empty($perm['page_url'])) {
                    $pageUrl = trim($perm['page_url'], '/');
                    self::$userPermissions['url_' . $pageUrl] = (bool)$perm['CanView'];
                }
            }

        } catch (Exception $e) {
            error_log("خطأ في تحميل الصلاحيات: " . $e->getMessage());
            self::$userPermissions = false;
        }
    }

    /**
     * الحصول على المسار الحالي
     */
    private static function getCurrentRoute()
    {
        $url = $_SERVER['REQUEST_URI'];

        // إزالة المعاملات
        $url = strtok($url, '?');

        // إزالة المسار الأساسي
        $base_path = parse_url(APP_URL, PHP_URL_PATH);
        if ($base_path && strpos($url, $base_path) === 0) {
            $url = substr($url, strlen($base_path));
        }

        // إزالة الشرطة المائلة من البداية والنهاية
        return trim($url, '/');
    }

    /**
     * التعامل مع عدم وجود صلاحية
     */
    private static function handleNoPermission()
    {
        // إذا كان طلب AJAX
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            http_response_code(403);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'ليس لديك صلاحية لهذا الإجراء'
            ]);
            exit;
        }

        // إذا كان طلب عادي - استخدام نفس نمط System
        // حفظ رسالة في الجلسة وإعادة التوجيه
        if (function_exists('flash')) {
            flash('permission_error', 'ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger');
        }

        // إعادة التوجيه حسب حالة المستخدم
        $user = current_user();
        if (!$user) {
            // غير مسجل دخول - توجيه لصفحة تسجيل الدخول
            if (function_exists('redirect')) {
                redirect(base_url('login'));
            }
        } elseif (!$user['current_company_id']) {
            // مسجل دخول لكن بدون شركة - توجيه لاختيار الشركة
            if (function_exists('redirect')) {
                redirect(base_url('companies'));
            }
        } else {
            // مسجل دخول مع شركة لكن بدون صلاحية - توجيه للوحة التحكم
            if (function_exists('redirect')) {
                redirect(base_url('dashboard'));
            }
        }

        // إذا فشلت الدوال المساعدة، استخدم الطريقة المباشرة
        self::renderPermissionErrorPage();
    }

    /**
     * عرض صفحة خطأ الصلاحيات مباشرة
     */
    private static function renderPermissionErrorPage()
    {
        http_response_code(403);

        // تنظيف أي مخرجات سابقة
        if (ob_get_level()) {
            ob_clean();
        }

        // تحضير البيانات لصفحة الخطأ
        $error_message = 'ليس لديك صلاحية للوصول إلى هذه الصفحة';
        $error_type = 'غير مسموح بالوصول';
        $show_details = false;

        // عرض صفحة الخطأ
        $errorPagePath = BASE_PATH . '/App/Notifications/errors/403.php';

        if (file_exists($errorPagePath)) {
            include $errorPagePath;
        } else {
            // صفحة خطأ بسيطة إذا لم توجد صفحة الخطأ المخصصة
            echo self::getSimplePermissionErrorPage();
        }

        exit;
    }

    /**
     * صفحة خطأ صلاحيات بسيطة كبديل
     */
    private static function getSimplePermissionErrorPage()
    {
        return '
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <title>غير مسموح بالوصول</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body {
                    font-family: Arial, sans-serif;
                    text-align: center;
                    padding: 50px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                    margin: 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .error {
                    background: rgba(255, 255, 255, 0.95);
                    color: #721c24;
                    padding: 40px;
                    border-radius: 20px;
                    max-width: 500px;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                }
                .error h1 { color: #dc3545; margin-bottom: 20px; }
                .error a {
                    color: #007bff;
                    text-decoration: none;
                    background: #007bff;
                    color: white;
                    padding: 10px 20px;
                    border-radius: 5px;
                    display: inline-block;
                    margin-top: 20px;
                }
                .error a:hover { background: #0056b3; }
            </style>
        </head>
        <body>
            <div class="error">
                <h1>🔒 غير مسموح بالوصول</h1>
                <p>عذراً، ليس لديك الصلاحية المطلوبة للوصول إلى هذه الصفحة.</p>
                <p>يرجى التواصل مع المدير لمنحك الصلاحيات المطلوبة.</p>
                <a href="' . (function_exists('base_url') ? base_url() : '/') . '">العودة للرئيسية</a>
            </div>
        </body>
        </html>';
    }

    /**
     * تسجيل صلاحية مطلوبة لمسار معين (للاستخدام اليدوي)
     */
    public static function requirePermission($route, $program, $action = 'view')
    {
        self::$routePermissions[$route] = [
            'program' => $program,
            'action' => $action
        ];
    }

    /**
     * الحصول على المسارات المحملة (للتصحيح)
     */
    public static function getLoadedRoutes()
    {
        self::loadProgramRoutes();
        return self::$programRoutes;
    }

    /**
     * الحصول على معلومات منصب المستخدم
     */
    public static function getUserPosition($userId = null, $companyId = null)
    {
        if (!$userId || !$companyId) {
            $user = current_user();
            if (!$user) return null;

            $userId = $userId ?: $user['UserID'];
            $companyId = $companyId ?: $user['current_company_id'];
        }

        try {
            global $db;
            $stmt = $db->prepare("
                SELECT
                    cu.position_id,
                    pos.PositionNameAR,
                    pos.PositionNameEN,
                    cu.status,
                    cu.user_status,
                    cu.join_date
                FROM company_users cu
                JOIN positions pos ON pos.PositionID = cu.position_id
                WHERE cu.user_id = ?
                AND cu.company_id = ?
                AND cu.status = 'accepted'
                AND cu.user_status = 'active'
            ");

            $stmt->execute([$userId, $companyId]);
            return $stmt->fetch(PDO::FETCH_ASSOC);

        } catch (Exception $e) {
            error_log("خطأ في جلب معلومات المنصب: " . $e->getMessage());
            return null;
        }
    }

    /**
     * التحقق من حالة المستخدم في الشركة
     */
    public static function getUserCompanyStatus($userId = null, $companyId = null)
    {
        if (!$userId || !$companyId) {
            $user = current_user();
            if (!$user) return null;

            $userId = $userId ?: $user['UserID'];
            $companyId = $companyId ?: $user['current_company_id'];
        }

        try {
            global $db;
            $stmt = $db->prepare("
                SELECT
                    status,
                    user_status,
                    position_id,
                    join_date,
                    notes
                FROM company_users
                WHERE user_id = ?
                AND company_id = ?
            ");

            $stmt->execute([$userId, $companyId]);
            return $stmt->fetch(PDO::FETCH_ASSOC);

        } catch (Exception $e) {
            error_log("خطأ في جلب حالة المستخدم: " . $e->getMessage());
            return null;
        }
    }
}

/**
 * وظائف مساعدة سريعة
 */
function hasPermission($program, $action = 'view') {
    return PermissionManager::hasPermission($program, $action);
}

function canView($program) {
    return PermissionManager::hasPermission($program, 'view');
}

function canCreate($program) {
    return PermissionManager::hasPermission($program, 'create');
}

function canEdit($program) {
    return PermissionManager::hasPermission($program, 'edit');
}

function canDelete($program) {
    return PermissionManager::hasPermission($program, 'delete');
}

function canApprove($program) {
    return PermissionManager::hasPermission($program, 'approve');
}

function requirePermission($route, $program, $action = 'view') {
    PermissionManager::requirePermission($route, $program, $action);
}

function showIfCan($program, $action, $content) {
    if (PermissionManager::hasPermission($program, $action)) {
        echo $content;
    }
}

/**
 * التحقق من صلاحية مخصصة
 */
function hasCustomPermission($program, $customAction) {
    return PermissionManager::hasPermission($program, $customAction);
}

/**
 * الحصول على جميع صلاحيات المستخدم
 */
function getUserPermissions() {
    return PermissionManager::$userPermissions;
}

/**
 * التحقق من صلاحية المستخدم لبرنامج معين
 */
function canAccessProgram($program) {
    return PermissionManager::hasPermission($program, 'view');
}

/**
 * التحقق من أن المستخدم هو مالك الشركة
 */
function isCompanyOwner($userId = null, $companyId = null) {
    if (!$userId || !$companyId) {
        $user = current_user();
        if (!$user) return false;

        $userId = $userId ?: $user['UserID'];
        $companyId = $companyId ?: $user['current_company_id'];
    }

    return PermissionManager::isCompanyOwner($userId, $companyId);
}

/**
 * الحصول على معلومات منصب المستخدم
 */
function getUserPosition($userId = null, $companyId = null) {
    return PermissionManager::getUserPosition($userId, $companyId);
}

/**
 * الحصول على حالة المستخدم في الشركة
 */
function getUserCompanyStatus($userId = null, $companyId = null) {
    return PermissionManager::getUserCompanyStatus($userId, $companyId);
}

/**
 * التحقق من أن المستخدم نشط في الشركة
 */
function isUserActiveInCompany($userId = null, $companyId = null) {
    $status = getUserCompanyStatus($userId, $companyId);
    return $status &&
           $status['status'] === 'accepted' &&
           $status['user_status'] === 'active' &&
           !empty($status['position_id']);
}

/**
 * التحقق من أن المستخدم عضو في الشركة (بغض النظر عن الصلاحيات)
 */
function isUserInCompany($userId = null, $companyId = null) {
    if (!$userId || !$companyId) {
        $user = current_user();
        if (!$user) return false;

        $userId = $userId ?: $user['UserID'];
        $companyId = $companyId ?: $user['current_company_id'];
    }

    try {
        global $db;
        $stmt = $db->prepare("
            SELECT COUNT(*) as is_member
            FROM company_users
            WHERE user_id = ?
            AND company_id = ?
            AND status = 'accepted'
            AND user_status = 'active'
            AND position_id IS NOT NULL
        ");

        $stmt->execute([$userId, $companyId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return $result['is_member'] > 0;

    } catch (Exception $e) {
        error_log("خطأ في التحقق من عضوية المستخدم: " . $e->getMessage());
        return false;
    }
}

/**
 * عرض قائمة البرامج المتاحة للمستخدم
 */
function getAvailablePrograms() {
    $user = current_user();
    if (!$user) return [];

    $companyId = $user['current_company_id'];
    $userId = $user['UserID'];

    if (!$companyId) return [];

    try {
        global $db;
        $stmt = $db->prepare("
            SELECT DISTINCT
                prog.program_id,
                prog.name_ar,
                prog.name_en,
                prog.icon_name,
                prog.page_url,
                prog.program_type,
                prog.parent_program_id,
                prog.display_order
            FROM company_all_programs prog
            JOIN permissions perm ON perm.ProgramID = prog.program_id
            JOIN positions pos ON pos.PositionID = perm.PositionID
            JOIN company_users cu ON cu.position_id = pos.PositionID
            WHERE cu.user_id = ?
            AND cu.company_id = ?
            AND cu.status = 'accepted'
            AND cu.user_status = 'active'
            AND perm.CompanyID = ?
            AND prog.company_id = ?
            AND prog.status_en = 'Active'
            AND perm.CanView = 1
            ORDER BY prog.display_order, prog.name_ar
        ");

        $stmt->execute([$userId, $companyId, $companyId, $companyId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);

    } catch (Exception $e) {
        error_log("خطأ في جلب البرامج المتاحة: " . $e->getMessage());
        return [];
    }
}

/**
 * وظائف مساعدة لإدارة المسارات الديناميكية
 */

/**
 * إضافة مسار مخصص (للحالات الخاصة فقط)
 */
function addCustomRoute($route, $program, $action = 'view') {
    PermissionManager::requirePermission($route, $program, $action);
}

/**
 * عرض جميع المسارات المحملة (للتطوير والتصحيح)
 */
function debugRoutes() {
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        echo '<pre>';
        print_r(PermissionManager::getLoadedRoutes());
        echo '</pre>';
    }
}
?>
