<?php
namespace App\System\Companies\Models;

/**
 * Company Model
 */
class Company {
    /**
     * Database connection
     *
     * @var \PDO
     */
    protected $db;

    /**
     * Constructor
     */
    public function __construct() {
        global $db;
        $this->db = $db;
    }

    /**
     * الحصول على الشركات التي يملكها المستخدم
     *
     * @param int $user_id معرف المستخدم
     * @return array
     */
    public function getOwnedCompanies($user_id) {
        $stmt = $this->db->prepare("
            SELECT c.*,
                   CASE
                       WHEN c.CompanyStatus = 'Trial' AND c.trial_end_date < NOW() THEN 'Expired'
                       ELSE c.CompanyStatus
                   END AS ActualStatus,
                   CASE
                       WHEN c.CompanyStatus = 'Trial' THEN DATEDIFF(c.trial_end_date, NOW())
                       ELSE NULL
                   END AS RemainingDays
            FROM companies c
            WHERE c.OwnerID = :user_id
            ORDER BY c.created_at DESC
        ");
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * التحقق مما إذا كان المستخدم لديه شركة مجانية نشطة
     *
     * @param int $user_id معرف المستخدم
     * @return bool
     */
    public function hasActiveFreeCompany($user_id) {
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as count
            FROM companies c
            WHERE c.OwnerID = :user_id
            AND (
                (c.CompanyStatus = 'Trial' AND c.trial_end_date > NOW())
                OR c.CompanyStatus = 'Active'
            )
            AND c.subscription_id IS NULL
        ");
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $result = $stmt->fetch(\PDO::FETCH_ASSOC);

        return $result['count'] > 0;
    }

    /**
     * الحصول على الشركات التي انضم إليها المستخدم
     *
     * @param int $user_id معرف المستخدم
     * @return array
     */
    public function getJoinedCompanies($user_id) {
        $stmt = $this->db->prepare("
            SELECT c.*, cu.status, cu.status_ar, cu.user_status, p.PositionNameAR, p.PositionNameEN
            FROM companies c
            JOIN company_users cu ON c.CompanyID = cu.company_id
            LEFT JOIN positions p ON cu.position_id = p.PositionID
            WHERE cu.user_id = :user_id AND cu.user_id != c.OwnerID
            ORDER BY cu.join_date DESC
        ");
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * إنشاء شركة جديدة
     *
     * @param array $data بيانات الشركة
     * @return int|bool معرف الشركة الجديدة أو false في حالة الفشل
     */
    public function create($data) {
        $stmt = $this->db->prepare("
            INSERT INTO companies (
                OwnerID, CompanyName, CompanyNameEN, CompanyEmail, CompanyPhone,
                CompanyLogo, CompanyStatus, CompanyAddress, CompanyWebsite,
                TaxID, subscription_status, trial_start_date, trial_end_date, created_at, updated_at,
                industry_type, company_size, foundation_date, social_media_links, notes
            ) VALUES (
                :owner_id, :company_name, :company_name_en, :company_email, :company_phone,
                :company_logo, 'Active', :company_address, :company_website,
                :tax_id, 'Trial', NOW(), :trial_end_date, NOW(), NOW(),
                :industry_type, :company_size, :foundation_date, :social_media_links, :notes
            )
        ");

        $stmt->bindParam(':owner_id', $data['owner_id']);
        $stmt->bindParam(':company_name', $data['company_name']);
        $stmt->bindParam(':company_name_en', $data['company_name_en']);
        $stmt->bindParam(':company_email', $data['company_email']);
        $stmt->bindParam(':company_phone', $data['company_phone']);
        $stmt->bindParam(':company_logo', $data['company_logo']);
        $stmt->bindParam(':company_address', $data['company_address']);
        $stmt->bindParam(':company_website', $data['company_website']);
        $stmt->bindParam(':tax_id', $data['tax_id']);
        $stmt->bindParam(':trial_end_date', $data['trial_end_date']);

        // الحقول الجديدة
        $industry_type = $data['industry_type'] ?? null;
        $company_size = $data['company_size'] ?? null;
        $foundation_date = $data['foundation_date'] ?? null;
        $social_media_links = $data['social_media_links'] ?? null;
        $notes = $data['notes'] ?? null;

        $stmt->bindParam(':industry_type', $industry_type);
        $stmt->bindParam(':company_size', $company_size);
        $stmt->bindParam(':foundation_date', $foundation_date);
        $stmt->bindParam(':social_media_links', $social_media_links);
        $stmt->bindParam(':notes', $notes);

        if ($stmt->execute()) {
            $company_id = $this->db->lastInsertId();

            // إضافة المالك كمستخدم في الشركة
            $this->addUserToCompany($company_id, $data['owner_id'], 'Admin');

            return $company_id;
        }

        return false;
    }

    /**
     * إضافة مستخدم إلى شركة
     *
     * @param int $company_id معرف الشركة
     * @param int $user_id معرف المستخدم
     * @param string $role دور المستخدم (Admin, User)
     * @return bool
     */
    public function addUserToCompany($company_id, $user_id, $role = 'User') {
        $stmt = $this->db->prepare("
            INSERT INTO company_users (company_id, user_id, position_id, status, status_ar, user_status, join_date, request_date, added_by_user_id)
            VALUES (:company_id, :user_id, (SELECT PositionID FROM positions WHERE PositionNameEN = :role LIMIT 1), 'accepted', 'مقبول', 'active', NOW(), NOW(), :added_by_user_id)
        ");

        $stmt->bindParam(':company_id', $company_id);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':role', $role);
        $stmt->bindParam(':added_by_user_id', $user_id); // المستخدم نفسه هو من أضاف نفسه (المالك)

        return $stmt->execute();
    }

    /**
     * الحصول على شركة بواسطة المعرف
     *
     * @param int $company_id معرف الشركة
     * @return array|false
     */
    public function getById($company_id) {
        $stmt = $this->db->prepare("SELECT * FROM companies WHERE CompanyID = :company_id");
        $stmt->bindParam(':company_id', $company_id);
        $stmt->execute();
        return $stmt->fetch(\PDO::FETCH_ASSOC);
    }

    /**
     * التحقق مما إذا كان المستخدم هو مالك الشركة
     *
     * @param int $company_id معرف الشركة
     * @param int $user_id معرف المستخدم
     * @return bool
     */
    public function isOwner($company_id, $user_id) {
        $stmt = $this->db->prepare("
            SELECT * FROM companies
            WHERE CompanyID = :company_id AND OwnerID = :user_id
        ");
        $stmt->bindParam(':company_id', $company_id);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();

        return (bool) $stmt->fetch();
    }

    /**
     * التحقق مما إذا كان المستخدم لديه صلاحية الوصول إلى الشركة
     *
     * @param int $company_id معرف الشركة
     * @param int $user_id معرف المستخدم
     * @return bool
     */
    public function userHasAccess($company_id, $user_id) {
        $stmt = $this->db->prepare("
            SELECT * FROM companies
            WHERE CompanyID = :company_id AND (
                OwnerID = :user_id OR EXISTS (
                    SELECT 1 FROM company_users cu
                    JOIN positions p ON cu.position_id = p.PositionID
                    WHERE cu.company_id = :company_id2
                    AND cu.user_id = :user_id2
                    AND cu.status = 'accepted'
                    AND cu.user_status = 'active'
                )
            )
        ");
        $stmt->bindParam(':company_id', $company_id);
        $stmt->bindParam(':company_id2', $company_id);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':user_id2', $user_id);
        $stmt->execute();

        return (bool) $stmt->fetch();
    }

    /**
     * الحصول على عدد المستخدمين في الشركة
     *
     * @param int $company_id معرف الشركة
     * @return int
     */
    public function getUserCount($company_id) {
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as user_count
            FROM company_users
            WHERE company_id = :company_id AND status = 'accepted'
        ");
        $stmt->bindParam(':company_id', $company_id);
        $stmt->execute();
        return $stmt->fetch(\PDO::FETCH_ASSOC)['user_count'];
    }

    /**
     * الحصول على عدد البرامج في الشركة
     *
     * @param int $company_id معرف الشركة
     * @return int
     */
    public function getProgramCount($company_id) {
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as program_count
            FROM company_all_programs
            WHERE company_id = :company_id
        ");
        $stmt->bindParam(':company_id', $company_id);
        $stmt->execute();
        return $stmt->fetch(\PDO::FETCH_ASSOC)['program_count'];
    }

    /**
     * تحديث بيانات الشركة
     *
     * @param int $company_id معرف الشركة
     * @param array $data بيانات الشركة
     * @return bool
     */
    public function update($company_id, $data) {
        $stmt = $this->db->prepare("
            UPDATE companies SET
                CompanyName = :company_name,
                CompanyNameEN = :company_name_en,
                CompanyEmail = :company_email,
                CompanyPhone = :company_phone,
                CompanyLogo = :company_logo,
                CompanyAddress = :company_address,
                CompanyWebsite = :company_website,
                TaxID = :tax_id,
                CompanyStatus = :company_status,
                subscription_status = :subscription_status,
                industry_type = :industry_type,
                company_size = :company_size,
                foundation_date = :foundation_date,
                social_media_links = :social_media_links,
                notes = :notes,
                updated_at = NOW()
            WHERE CompanyID = :company_id
        ");

        // إعداد المتغيرات للربط
        $subscription_status = $data['subscription_status'] ?? 'Trial';
        $industry_type = $data['industry_type'] ?? null;
        $company_size = $data['company_size'] ?? null;
        $foundation_date = $data['foundation_date'] ?? null;
        $social_media_links = $data['social_media_links'] ?? null;
        $notes = $data['notes'] ?? null;

        $stmt->bindParam(':company_id', $company_id);
        $stmt->bindParam(':company_name', $data['company_name']);
        $stmt->bindParam(':company_name_en', $data['company_name_en']);
        $stmt->bindParam(':company_email', $data['company_email']);
        $stmt->bindParam(':company_phone', $data['company_phone']);
        $stmt->bindParam(':company_logo', $data['company_logo']);
        $stmt->bindParam(':company_address', $data['company_address']);
        $stmt->bindParam(':company_website', $data['company_website']);
        $stmt->bindParam(':tax_id', $data['tax_id']);
        $stmt->bindParam(':company_status', $data['company_status']);
        $stmt->bindParam(':subscription_status', $subscription_status);
        $stmt->bindParam(':industry_type', $industry_type);
        $stmt->bindParam(':company_size', $company_size);
        $stmt->bindParam(':foundation_date', $foundation_date);
        $stmt->bindParam(':social_media_links', $social_media_links);
        $stmt->bindParam(':notes', $notes);

        return $stmt->execute();
    }

    /**
     * تعطيل شركة
     *
     * @param int $company_id معرف الشركة
     * @param string|null $deactivation_reason سبب التعطيل
     * @return bool
     */
    public function deactivate($company_id, $deactivation_reason = null) {
        $stmt = $this->db->prepare("
            UPDATE companies SET
                CompanyStatus = 'Inactive',
                deactivation_reason = :deactivation_reason,
                deactivation_date = NOW(),
                updated_at = NOW()
            WHERE CompanyID = :company_id
        ");
        $stmt->bindParam(':company_id', $company_id);
        $stmt->bindParam(':deactivation_reason', $deactivation_reason);

        return $stmt->execute();
    }

    /**
     * تفعيل شركة
     *
     * @param int $company_id معرف الشركة
     * @return bool
     */
    public function activate($company_id) {
        $stmt = $this->db->prepare("
            UPDATE companies SET
                CompanyStatus = 'Active',
                deactivation_reason = NULL,
                deactivation_date = NULL,
                updated_at = NOW()
            WHERE CompanyID = :company_id
        ");
        $stmt->bindParam(':company_id', $company_id);

        return $stmt->execute();
    }

    /**
     * تعيين الشركة الحالية للمستخدم
     *
     * @param int $user_id معرف المستخدم
     * @param int $company_id معرف الشركة
     * @return bool
     */
    public function setCurrentCompanyForUser($user_id, $company_id) {
        $stmt = $this->db->prepare("
            UPDATE users SET
                current_company_id = :company_id
            WHERE UserID = :user_id
        ");
        $stmt->bindParam(':company_id', $company_id);
        $stmt->bindParam(':user_id', $user_id);

        return $stmt->execute();
    }

    /**
     * الحصول على دعوة للانضمام إلى شركة
     *
     * @param int $company_id معرف الشركة
     * @param int $user_id معرف المستخدم
     * @return array|false
     */
    public function getInvitation($company_id, $user_id) {
        $stmt = $this->db->prepare("
            SELECT * FROM company_users
            WHERE company_id = :company_id AND user_id = :user_id AND status = 'pending'
        ");
        $stmt->bindParam(':company_id', $company_id);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();

        return $stmt->fetch(\PDO::FETCH_ASSOC);
    }

    /**
     * قبول دعوة للانضمام إلى شركة
     *
     * @param int $invitation_id معرف الدعوة
     * @return bool
     */
    public function acceptInvitation($invitation_id) {
        $stmt = $this->db->prepare("
            UPDATE company_users SET
                status = 'accepted',
                join_date = NOW(),
                updated_at = NOW()
            WHERE id = :invitation_id
        ");
        $stmt->bindParam(':invitation_id', $invitation_id);

        return $stmt->execute();
    }

    /**
     * رفض دعوة للانضمام إلى شركة
     *
     * @param int $invitation_id معرف الدعوة
     * @return bool
     */
    public function rejectInvitation($invitation_id) {
        $stmt = $this->db->prepare("
            UPDATE company_users SET
                status = 'rejected',
                updated_at = NOW()
            WHERE id = :invitation_id
        ");
        $stmt->bindParam(':invitation_id', $invitation_id);

        return $stmt->execute();
    }

    /**
     * إرسال دعوة للانضمام إلى شركة
     *
     * @param int $company_id معرف الشركة
     * @param string $email البريد الإلكتروني للمستخدم
     * @param string $role دور المستخدم (Admin, User)
     * @return bool
     */
    public function sendInvitation($company_id, $email, $role = 'User') {
        // التحقق مما إذا كان المستخدم موجودًا
        $stmt = $this->db->prepare("SELECT UserID FROM users WHERE email = :email");
        $stmt->bindParam(':email', $email);
        $stmt->execute();
        $user = $stmt->fetch(\PDO::FETCH_ASSOC);

        if (!$user) {
            return false;
        }

        $user_id = $user['UserID'];

        // التحقق مما إذا كان المستخدم بالفعل عضوًا في الشركة
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as is_member
            FROM company_users
            WHERE company_id = :company_id AND user_id = :user_id AND status = 'accepted'
        ");
        $stmt->bindParam(':company_id', $company_id);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $is_member = $stmt->fetch(\PDO::FETCH_ASSOC)['is_member'] > 0;

        if ($is_member) {
            return false;
        }

        // التحقق مما إذا كانت هناك دعوة معلقة بالفعل
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as has_pending
            FROM company_users
            WHERE company_id = :company_id AND user_id = :user_id AND status = 'pending'
        ");
        $stmt->bindParam(':company_id', $company_id);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $has_pending = $stmt->fetch(\PDO::FETCH_ASSOC)['has_pending'] > 0;

        if ($has_pending) {
            return false;
        }

        // الحصول على معرف المنصب
        $stmt = $this->db->prepare("
            SELECT PositionID FROM positions WHERE PositionNameEN = :role LIMIT 1
        ");
        $stmt->bindParam(':role', $role);
        $stmt->execute();
        $position = $stmt->fetch(\PDO::FETCH_ASSOC);
        $position_id = $position ? $position['PositionID'] : 1; // استخدام المنصب الافتراضي إذا لم يتم العثور على المنصب

        // إنشاء دعوة جديدة
        $stmt = $this->db->prepare("
            INSERT INTO company_users (
                company_id, user_id, position_id, status, status_ar, user_status, request_date, added_by_user_id
            ) VALUES (
                :company_id, :user_id, :position_id, 'pending', 'معلق', 'active', NOW(), :added_by_user_id
            )
        ");
        $stmt->bindParam(':company_id', $company_id);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':position_id', $position_id);
        $stmt->bindParam(':added_by_user_id', current_user_id()); // المستخدم الحالي هو من أضاف الدعوة

        return $stmt->execute();
    }

    /**
     * الحصول على الدعوات المعلقة للشركة
     *
     * @param int $company_id معرف الشركة
     * @return array
     */
    public function getPendingInvitations($company_id) {
        $stmt = $this->db->prepare("
            SELECT cu.*, u.name, u.email, p.PositionNameAR, p.PositionNameEN
            FROM company_users cu
            JOIN users u ON cu.user_id = u.UserID
            LEFT JOIN positions p ON cu.position_id = p.PositionID
            WHERE cu.company_id = :company_id AND cu.status = 'pending'
            ORDER BY cu.request_date DESC
        ");
        $stmt->bindParam(':company_id', $company_id);
        $stmt->execute();

        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على مستخدمي الشركة
     *
     * @param int $company_id معرف الشركة
     * @return array
     */
    public function getCompanyUsers($company_id) {
        $stmt = $this->db->prepare("
            SELECT cu.*, u.name, u.email, u.phone, p.PositionNameAR, p.PositionNameEN
            FROM company_users cu
            JOIN users u ON cu.user_id = u.UserID
            LEFT JOIN positions p ON cu.position_id = p.PositionID
            WHERE cu.company_id = :company_id AND cu.status = 'accepted'
            ORDER BY p.PositionOrder ASC, u.name ASC
        ");
        $stmt->bindParam(':company_id', $company_id);
        $stmt->execute();

        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * إزالة مستخدم من الشركة
     *
     * @param int $company_id معرف الشركة
     * @param int $user_id معرف المستخدم
     * @return bool
     */
    public function removeUser($company_id, $user_id) {
        $stmt = $this->db->prepare("
            UPDATE company_users SET
                status = 'removed',
                status_ar = 'تم إزالته',
                user_status = 'inactive',
                updated_at = NOW()
            WHERE company_id = :company_id AND user_id = :user_id
        ");
        $stmt->bindParam(':company_id', $company_id);
        $stmt->bindParam(':user_id', $user_id);

        return $stmt->execute();
    }

    /**
     * تغيير دور مستخدم في الشركة
     *
     * @param int $company_id معرف الشركة
     * @param int $user_id معرف المستخدم
     * @param string $role الدور الجديد (Admin, User)
     * @return bool
     */
    public function changeUserRole($company_id, $user_id, $role) {
        // الحصول على معرف المنصب
        $stmt = $this->db->prepare("
            SELECT PositionID FROM positions WHERE PositionNameEN = :role LIMIT 1
        ");
        $stmt->bindParam(':role', $role);
        $stmt->execute();
        $position = $stmt->fetch(\PDO::FETCH_ASSOC);
        $position_id = $position ? $position['PositionID'] : 1; // استخدام المنصب الافتراضي إذا لم يتم العثور على المنصب

        $stmt = $this->db->prepare("
            UPDATE company_users SET
                position_id = :position_id,
                updated_at = NOW()
            WHERE company_id = :company_id AND user_id = :user_id
        ");
        $stmt->bindParam(':company_id', $company_id);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':position_id', $position_id);

        return $stmt->execute();
    }

    /**
     * حذف شركة
     *
     * @param int $company_id معرف الشركة
     * @return bool
     */
    public function delete($company_id) {
        try {
            // بدء المعاملة
            $this->db->beginTransaction();

            // حذف مستخدمي الشركة
            $stmt = $this->db->prepare("DELETE FROM company_users WHERE company_id = :company_id");
            $stmt->bindParam(':company_id', $company_id);
            $stmt->execute();

            // حذف برامج الشركة
            $stmt = $this->db->prepare("DELETE FROM company_all_programs WHERE company_id = :company_id");
            $stmt->bindParam(':company_id', $company_id);
            $stmt->execute();

            // الحصول على معلومات الشركة قبل الحذف (للشعار)
            $company = $this->getById($company_id);

            // حذف الشركة
            $stmt = $this->db->prepare("DELETE FROM companies WHERE CompanyID = :company_id");
            $stmt->bindParam(':company_id', $company_id);
            $stmt->execute();

            // إنهاء المعاملة
            $this->db->commit();

            // حذف شعار الشركة إذا كان موجوداً
            if (!empty($company['CompanyLogo']) && file_exists(ROOT_PATH . '/' . $company['CompanyLogo'])) {
                unlink(ROOT_PATH . '/' . $company['CompanyLogo']);
            }

            return true;
        } catch (\Exception $e) {
            // التراجع عن المعاملة في حالة حدوث خطأ
            $this->db->rollBack();
            return false;
        }
    }
}