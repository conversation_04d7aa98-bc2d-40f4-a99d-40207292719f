<?php
namespace Modules\Accounting\Inventory\Models;

/**
 * Product Model
 * نموذج المنتجات
 */
class Product {
    /**
     * Database connection
     *
     * @var \PDO
     */
    protected $db;

    /**
     * Constructor
     */
    public function __construct() {
        global $db;
        $this->db = $db;
    }

    /**
     * الحصول على جميع المنتجات للشركة
     *
     * @param int $company_id معرف الشركة
     * @param array $filters فلاتر البحث
     * @return array
     */
    public function getAll($company_id, $filters = []) {
        $where_conditions = ['p.company_id = :company_id'];
        $params = [':company_id' => $company_id];

        // فلتر حسب الفئة
        if (!empty($filters['category_id'])) {
            $where_conditions[] = 'p.category_id = :category_id';
            $params[':category_id'] = $filters['category_id'];
        }

        // فلتر حسب الحالة
        if (isset($filters['is_active'])) {
            $where_conditions[] = 'p.is_active = :is_active';
            $params[':is_active'] = $filters['is_active'];
        }

        // فلتر البحث النصي
        if (!empty($filters['search'])) {
            $where_conditions[] = '(p.product_name_ar LIKE :search OR p.product_name_en LIKE :search OR p.product_code LIKE :search OR p.barcode LIKE :search)';
            $params[':search'] = '%' . $filters['search'] . '%';
        }

        $where_clause = implode(' AND ', $where_conditions);

        $sql = "
            SELECT p.*, 
                   c.category_name_ar, c.category_name_en,
                   u.unit_name_ar, u.unit_name_en, u.unit_symbol_ar,
                   COALESCE(SUM(s.quantity_on_hand), 0) as total_stock,
                   COALESCE(SUM(s.quantity_available), 0) as available_stock
            FROM inventory_products p
            LEFT JOIN inventory_categories c ON p.category_id = c.category_id
            LEFT JOIN inventory_units u ON p.unit_id = u.unit_id
            LEFT JOIN inventory_stock s ON p.product_id = s.product_id
            WHERE {$where_clause}
            GROUP BY p.product_id
            ORDER BY p.product_name_ar ASC
        ";

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على منتج بواسطة المعرف
     *
     * @param int $product_id معرف المنتج
     * @param int $company_id معرف الشركة
     * @return array|false
     */
    public function getById($product_id, $company_id) {
        $sql = "
            SELECT p.*, 
                   c.category_name_ar, c.category_name_en,
                   u.unit_name_ar, u.unit_name_en, u.unit_symbol_ar,
                   creator.UserName as created_by_name,
                   updater.UserName as updated_by_name
            FROM inventory_products p
            LEFT JOIN inventory_categories c ON p.category_id = c.category_id
            LEFT JOIN inventory_units u ON p.unit_id = u.unit_id
            LEFT JOIN users creator ON p.created_by = creator.UserID
            LEFT JOIN users updater ON p.updated_by = updater.UserID
            WHERE p.product_id = :product_id AND p.company_id = :company_id
        ";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':product_id', $product_id);
        $stmt->bindParam(':company_id', $company_id);
        $stmt->execute();
        return $stmt->fetch(\PDO::FETCH_ASSOC);
    }

    /**
     * إنشاء منتج جديد
     *
     * @param array $data بيانات المنتج
     * @return int|false معرف المنتج الجديد أو false في حالة الفشل
     */
    public function create($data) {
        $sql = "
            INSERT INTO inventory_products (
                company_id, module_code, product_code, barcode, product_name_ar, product_name_en,
                description_ar, description_en, category_id, unit_id, product_type, track_inventory,
                cost_price, selling_price, min_stock_level, max_stock_level, reorder_point,
                weight, dimensions, image_url, tax_rate, is_active, created_by
            ) VALUES (
                :company_id, :module_code, :product_code, :barcode, :product_name_ar, :product_name_en,
                :description_ar, :description_en, :category_id, :unit_id, :product_type, :track_inventory,
                :cost_price, :selling_price, :min_stock_level, :max_stock_level, :reorder_point,
                :weight, :dimensions, :image_url, :tax_rate, :is_active, :created_by
            )
        ";

        $stmt = $this->db->prepare($sql);
        
        // تعيين القيم الافتراضية
        $data['module_code'] = $data['module_code'] ?? 'inventory';
        $data['product_type'] = $data['product_type'] ?? 'product';
        $data['track_inventory'] = $data['track_inventory'] ?? 1;
        $data['is_active'] = $data['is_active'] ?? 1;
        $data['cost_price'] = $data['cost_price'] ?? 0.00;
        $data['selling_price'] = $data['selling_price'] ?? 0.00;
        $data['min_stock_level'] = $data['min_stock_level'] ?? 0.00;
        $data['reorder_point'] = $data['reorder_point'] ?? 0.00;
        $data['tax_rate'] = $data['tax_rate'] ?? 0.00;

        $stmt->bindParam(':company_id', $data['company_id']);
        $stmt->bindParam(':module_code', $data['module_code']);
        $stmt->bindParam(':product_code', $data['product_code']);
        $stmt->bindParam(':barcode', $data['barcode']);
        $stmt->bindParam(':product_name_ar', $data['product_name_ar']);
        $stmt->bindParam(':product_name_en', $data['product_name_en']);
        $stmt->bindParam(':description_ar', $data['description_ar']);
        $stmt->bindParam(':description_en', $data['description_en']);
        $stmt->bindParam(':category_id', $data['category_id']);
        $stmt->bindParam(':unit_id', $data['unit_id']);
        $stmt->bindParam(':product_type', $data['product_type']);
        $stmt->bindParam(':track_inventory', $data['track_inventory']);
        $stmt->bindParam(':cost_price', $data['cost_price']);
        $stmt->bindParam(':selling_price', $data['selling_price']);
        $stmt->bindParam(':min_stock_level', $data['min_stock_level']);
        $stmt->bindParam(':max_stock_level', $data['max_stock_level']);
        $stmt->bindParam(':reorder_point', $data['reorder_point']);
        $stmt->bindParam(':weight', $data['weight']);
        $stmt->bindParam(':dimensions', $data['dimensions']);
        $stmt->bindParam(':image_url', $data['image_url']);
        $stmt->bindParam(':tax_rate', $data['tax_rate']);
        $stmt->bindParam(':is_active', $data['is_active']);
        $stmt->bindParam(':created_by', $data['created_by']);

        if ($stmt->execute()) {
            return $this->db->lastInsertId();
        }

        return false;
    }

    /**
     * تحديث منتج
     *
     * @param int $product_id معرف المنتج
     * @param array $data بيانات المنتج
     * @param int $company_id معرف الشركة
     * @return bool
     */
    public function update($product_id, $data, $company_id) {
        $sql = "
            UPDATE inventory_products SET
                product_code = :product_code,
                barcode = :barcode,
                product_name_ar = :product_name_ar,
                product_name_en = :product_name_en,
                description_ar = :description_ar,
                description_en = :description_en,
                category_id = :category_id,
                unit_id = :unit_id,
                product_type = :product_type,
                track_inventory = :track_inventory,
                cost_price = :cost_price,
                selling_price = :selling_price,
                min_stock_level = :min_stock_level,
                max_stock_level = :max_stock_level,
                reorder_point = :reorder_point,
                weight = :weight,
                dimensions = :dimensions,
                image_url = :image_url,
                tax_rate = :tax_rate,
                is_active = :is_active,
                updated_by = :updated_by,
                updated_at = NOW()
            WHERE product_id = :product_id AND company_id = :company_id
        ";

        $stmt = $this->db->prepare($sql);
        
        $stmt->bindParam(':product_id', $product_id);
        $stmt->bindParam(':company_id', $company_id);
        $stmt->bindParam(':product_code', $data['product_code']);
        $stmt->bindParam(':barcode', $data['barcode']);
        $stmt->bindParam(':product_name_ar', $data['product_name_ar']);
        $stmt->bindParam(':product_name_en', $data['product_name_en']);
        $stmt->bindParam(':description_ar', $data['description_ar']);
        $stmt->bindParam(':description_en', $data['description_en']);
        $stmt->bindParam(':category_id', $data['category_id']);
        $stmt->bindParam(':unit_id', $data['unit_id']);
        $stmt->bindParam(':product_type', $data['product_type']);
        $stmt->bindParam(':track_inventory', $data['track_inventory']);
        $stmt->bindParam(':cost_price', $data['cost_price']);
        $stmt->bindParam(':selling_price', $data['selling_price']);
        $stmt->bindParam(':min_stock_level', $data['min_stock_level']);
        $stmt->bindParam(':max_stock_level', $data['max_stock_level']);
        $stmt->bindParam(':reorder_point', $data['reorder_point']);
        $stmt->bindParam(':weight', $data['weight']);
        $stmt->bindParam(':dimensions', $data['dimensions']);
        $stmt->bindParam(':image_url', $data['image_url']);
        $stmt->bindParam(':tax_rate', $data['tax_rate']);
        $stmt->bindParam(':is_active', $data['is_active']);
        $stmt->bindParam(':updated_by', $data['updated_by']);

        return $stmt->execute();
    }

    /**
     * حذف منتج
     *
     * @param int $product_id معرف المنتج
     * @param int $company_id معرف الشركة
     * @return bool
     */
    public function delete($product_id, $company_id) {
        // التحقق من عدم وجود حركات مخزون للمنتج
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as movement_count 
            FROM inventory_movements 
            WHERE product_id = :product_id AND company_id = :company_id
        ");
        $stmt->bindParam(':product_id', $product_id);
        $stmt->bindParam(':company_id', $company_id);
        $stmt->execute();
        $result = $stmt->fetch(\PDO::FETCH_ASSOC);

        if ($result['movement_count'] > 0) {
            return false; // لا يمكن حذف منتج له حركات مخزون
        }

        $stmt = $this->db->prepare("
            DELETE FROM inventory_products 
            WHERE product_id = :product_id AND company_id = :company_id
        ");
        $stmt->bindParam(':product_id', $product_id);
        $stmt->bindParam(':company_id', $company_id);

        return $stmt->execute();
    }

    /**
     * التحقق من وجود كود المنتج
     *
     * @param string $product_code كود المنتج
     * @param int $company_id معرف الشركة
     * @param int|null $exclude_id معرف المنتج المستثنى (للتحديث)
     * @return bool
     */
    public function isProductCodeExists($product_code, $company_id, $exclude_id = null) {
        $sql = "SELECT COUNT(*) as count FROM inventory_products WHERE product_code = :product_code AND company_id = :company_id";
        $params = [':product_code' => $product_code, ':company_id' => $company_id];

        if ($exclude_id) {
            $sql .= " AND product_id != :exclude_id";
            $params[':exclude_id'] = $exclude_id;
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetch(\PDO::FETCH_ASSOC);

        return $result['count'] > 0;
    }

    /**
     * التحقق من وجود الباركود
     *
     * @param string $barcode الباركود
     * @param int $company_id معرف الشركة
     * @param int|null $exclude_id معرف المنتج المستثنى (للتحديث)
     * @return bool
     */
    public function isBarcodeExists($barcode, $company_id, $exclude_id = null) {
        if (empty($barcode)) {
            return false;
        }

        $sql = "SELECT COUNT(*) as count FROM inventory_products WHERE barcode = :barcode AND company_id = :company_id";
        $params = [':barcode' => $barcode, ':company_id' => $company_id];

        if ($exclude_id) {
            $sql .= " AND product_id != :exclude_id";
            $params[':exclude_id'] = $exclude_id;
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetch(\PDO::FETCH_ASSOC);

        return $result['count'] > 0;
    }

    /**
     * الحصول على المنتجات التي تحتاج إعادة طلب
     *
     * @param int $company_id معرف الشركة
     * @return array
     */
    public function getProductsNeedReorder($company_id) {
        $sql = "
            SELECT p.*, 
                   c.category_name_ar, c.category_name_en,
                   u.unit_name_ar, u.unit_symbol_ar,
                   COALESCE(SUM(s.quantity_available), 0) as available_stock
            FROM inventory_products p
            LEFT JOIN inventory_categories c ON p.category_id = c.category_id
            LEFT JOIN inventory_units u ON p.unit_id = u.unit_id
            LEFT JOIN inventory_stock s ON p.product_id = s.product_id
            WHERE p.company_id = :company_id 
            AND p.is_active = 1 
            AND p.track_inventory = 1
            GROUP BY p.product_id
            HAVING available_stock <= p.reorder_point
            ORDER BY p.product_name_ar ASC
        ";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':company_id', $company_id);
        $stmt->execute();
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }
}
