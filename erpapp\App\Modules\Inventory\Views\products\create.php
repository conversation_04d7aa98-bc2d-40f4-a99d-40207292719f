<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title">
                    <i class="fas fa-plus-circle"></i> <?= __('إضافة منتج جديد') ?>
                </h1>
                <a href="<?= base_url('inventory/products') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> <?= __('العودة إلى قائمة المنتجات') ?>
                </a>
            </div>
        </div>
    </div>

    <?php display_flash('product_error'); ?>

    <div class="row">
        <div class="col-lg-10 mx-auto">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-box me-2"></i> <?= __('إضافة منتج جديد') ?>
                    </h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('inventory/products/store') ?>" method="post" enctype="multipart/form-data" id="product-form">
                        <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">

                        <!-- تبويبات النموذج -->
                        <ul class="nav nav-tabs mb-4" id="productTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="basic-info-tab" data-bs-toggle="tab" data-bs-target="#basic-info" type="button" role="tab" aria-controls="basic-info" aria-selected="true">
                                    <i class="fas fa-info-circle me-1"></i> <?= __('المعلومات الأساسية') ?>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="pricing-tab" data-bs-toggle="tab" data-bs-target="#pricing" type="button" role="tab" aria-controls="pricing" aria-selected="false">
                                    <i class="fas fa-dollar-sign me-1"></i> <?= __('الأسعار') ?>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="inventory-tab" data-bs-toggle="tab" data-bs-target="#inventory" type="button" role="tab" aria-controls="inventory" aria-selected="false">
                                    <i class="fas fa-warehouse me-1"></i> <?= __('إعدادات المخزون') ?>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="additional-info-tab" data-bs-toggle="tab" data-bs-target="#additional-info" type="button" role="tab" aria-controls="additional-info" aria-selected="false">
                                    <i class="fas fa-cog me-1"></i> <?= __('معلومات إضافية') ?>
                                </button>
                            </li>
                        </ul>

                        <!-- محتوى التبويبات -->
                        <div class="tab-content" id="productTabsContent">
                            <!-- تبويب المعلومات الأساسية -->
                            <div class="tab-pane fade show active" id="basic-info" role="tabpanel" aria-labelledby="basic-info-tab">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="product_code" class="form-label"><?= __('كود المنتج') ?> <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="product_code" name="product_code" required>
                                        <div class="form-text"><?= __('كود فريد للمنتج (مثل: PROD001)') ?></div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="barcode" class="form-label"><?= __('الباركود') ?></label>
                                        <input type="text" class="form-control" id="barcode" name="barcode">
                                        <div class="form-text"><?= __('باركود المنتج (اختياري)') ?></div>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="product_name_ar" class="form-label"><?= __('اسم المنتج (عربي)') ?> <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="product_name_ar" name="product_name_ar" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="product_name_en" class="form-label"><?= __('اسم المنتج (إنجليزي)') ?></label>
                                        <input type="text" class="form-control" id="product_name_en" name="product_name_en">
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="description_ar" class="form-label"><?= __('الوصف (عربي)') ?></label>
                                        <textarea class="form-control" id="description_ar" name="description_ar" rows="3"></textarea>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="description_en" class="form-label"><?= __('الوصف (إنجليزي)') ?></label>
                                        <textarea class="form-control" id="description_en" name="description_en" rows="3"></textarea>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label for="category_id" class="form-label"><?= __('الفئة') ?> <span class="text-danger">*</span></label>
                                        <select class="form-select" id="category_id" name="category_id" required>
                                            <option value=""><?= __('اختر الفئة') ?></option>
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?= $category['category_id'] ?>">
                                                    <?= htmlspecialchars($category['category_name_ar']) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="unit_id" class="form-label"><?= __('وحدة القياس') ?> <span class="text-danger">*</span></label>
                                        <select class="form-select" id="unit_id" name="unit_id" required>
                                            <option value=""><?= __('اختر الوحدة') ?></option>
                                            <?php foreach ($units as $unit): ?>
                                                <option value="<?= $unit['unit_id'] ?>">
                                                    <?= htmlspecialchars($unit['unit_name_ar']) ?> (<?= htmlspecialchars($unit['unit_symbol_ar']) ?>)
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="product_type" class="form-label"><?= __('نوع المنتج') ?> <span class="text-danger">*</span></label>
                                        <select class="form-select" id="product_type" name="product_type" required>
                                            <option value="product"><?= __('منتج') ?></option>
                                            <option value="service"><?= __('خدمة') ?></option>
                                            <option value="digital"><?= __('رقمي') ?></option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- تبويب الأسعار -->
                            <div class="tab-pane fade" id="pricing" role="tabpanel" aria-labelledby="pricing-tab">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="cost_price" class="form-label"><?= __('سعر التكلفة') ?> <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="cost_price" name="cost_price" step="0.01" min="0" required>
                                            <span class="input-group-text"><?= __('ر.س') ?></span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="selling_price" class="form-label"><?= __('سعر البيع') ?> <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="selling_price" name="selling_price" step="0.01" min="0" required>
                                            <span class="input-group-text"><?= __('ر.س') ?></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="tax_rate" class="form-label"><?= __('معدل الضريبة (%)') ?></label>
                                        <input type="number" class="form-control" id="tax_rate" name="tax_rate" step="0.01" min="0" max="100" value="15">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label"><?= __('هامش الربح') ?></label>
                                        <div class="form-control-plaintext" id="profit_margin">0%</div>
                                    </div>
                                </div>
                            </div>

                            <!-- تبويب إعدادات المخزون -->
                            <div class="tab-pane fade" id="inventory" role="tabpanel" aria-labelledby="inventory-tab">
                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="track_inventory" name="track_inventory" value="1" checked>
                                            <label class="form-check-label" for="track_inventory">
                                                <?= __('تتبع المخزون') ?>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label for="min_stock_level" class="form-label"><?= __('الحد الأدنى للمخزون') ?></label>
                                        <input type="number" class="form-control" id="min_stock_level" name="min_stock_level" step="0.01" min="0" value="0">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="max_stock_level" class="form-label"><?= __('الحد الأقصى للمخزون') ?></label>
                                        <input type="number" class="form-control" id="max_stock_level" name="max_stock_level" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="reorder_point" class="form-label"><?= __('نقطة إعادة الطلب') ?></label>
                                        <input type="number" class="form-control" id="reorder_point" name="reorder_point" step="0.01" min="0" value="0">
                                    </div>
                                </div>
                            </div>

                            <!-- تبويب معلومات إضافية -->
                            <div class="tab-pane fade" id="additional-info" role="tabpanel" aria-labelledby="additional-info-tab">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="weight" class="form-label"><?= __('الوزن (كجم)') ?></label>
                                        <input type="number" class="form-control" id="weight" name="weight" step="0.001" min="0">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="dimensions" class="form-label"><?= __('الأبعاد') ?></label>
                                        <input type="text" class="form-control" id="dimensions" name="dimensions" placeholder="<?= __('الطول × العرض × الارتفاع') ?>">
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="image" class="form-label"><?= __('صورة المنتج') ?></label>
                                        <input type="file" class="form-control" id="image" name="image" accept="image/*">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label"><?= __('معاينة الصورة') ?></label>
                                        <div class="image-preview border rounded p-3 text-center" style="min-height: 100px; cursor: pointer;" onclick="document.getElementById('image').click()">
                                            <i class="fas fa-camera fa-2x text-muted"></i>
                                            <div class="text-muted mt-2"><?= __('انقر لاختيار صورة') ?></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" checked>
                                            <label class="form-check-label" for="is_active">
                                                <?= __('منتج نشط') ?>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <a href="<?= base_url('inventory/products') ?>" class="btn btn-light me-md-2">
                                <i class="fas fa-times me-1"></i> <?= __('إلغاء') ?>
                            </a>
                            <button type="submit" class="btn btn-success me-md-2">
                                <i class="fas fa-save me-1"></i> <?= __('حفظ المنتج') ?>
                            </button>
                            <button type="submit" name="save_and_new" value="1" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i> <?= __('حفظ وإضافة آخر') ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من صحة النموذج قبل الإرسال
    const form = document.getElementById('product-form');
    form.addEventListener('submit', function(event) {
        let isValid = true;

        // التحقق من كود المنتج
        const productCode = document.getElementById('product_code').value.trim();
        if (productCode === '') {
            isValid = false;
            document.getElementById('product_code').classList.add('is-invalid');
            // تبديل إلى التبويب المناسب
            document.getElementById('basic-info-tab').click();
        } else {
            document.getElementById('product_code').classList.remove('is-invalid');
        }

        // التحقق من اسم المنتج
        const productName = document.getElementById('product_name_ar').value.trim();
        if (productName === '') {
            isValid = false;
            document.getElementById('product_name_ar').classList.add('is-invalid');
            // تبديل إلى التبويب المناسب
            document.getElementById('basic-info-tab').click();
        } else {
            document.getElementById('product_name_ar').classList.remove('is-invalid');
        }

        // التحقق من الفئة
        const categoryId = document.getElementById('category_id').value;
        if (categoryId === '') {
            isValid = false;
            document.getElementById('category_id').classList.add('is-invalid');
            document.getElementById('basic-info-tab').click();
        } else {
            document.getElementById('category_id').classList.remove('is-invalid');
        }

        // التحقق من وحدة القياس
        const unitId = document.getElementById('unit_id').value;
        if (unitId === '') {
            isValid = false;
            document.getElementById('unit_id').classList.add('is-invalid');
            document.getElementById('basic-info-tab').click();
        } else {
            document.getElementById('unit_id').classList.remove('is-invalid');
        }

        // التحقق من الأسعار
        const costPrice = parseFloat(document.getElementById('cost_price').value) || 0;
        const sellingPrice = parseFloat(document.getElementById('selling_price').value) || 0;

        if (costPrice <= 0) {
            isValid = false;
            document.getElementById('cost_price').classList.add('is-invalid');
            document.getElementById('pricing-tab').click();
        } else {
            document.getElementById('cost_price').classList.remove('is-invalid');
        }

        if (sellingPrice <= 0) {
            isValid = false;
            document.getElementById('selling_price').classList.add('is-invalid');
            document.getElementById('pricing-tab').click();
        } else {
            document.getElementById('selling_price').classList.remove('is-invalid');
        }

        // تحذير إذا كان سعر البيع أقل من التكلفة
        if (sellingPrice > 0 && costPrice > 0 && sellingPrice < costPrice) {
            if (!confirm('<?= __('سعر البيع أقل من سعر التكلفة. هل تريد المتابعة؟') ?>')) {
                isValid = false;
            }
        }

        if (!isValid) {
            event.preventDefault();
        }
    });

    // حساب هامش الربح تلقائياً
    function calculateProfitMargin() {
        const costPrice = parseFloat(document.getElementById('cost_price').value) || 0;
        const sellingPrice = parseFloat(document.getElementById('selling_price').value) || 0;

        if (costPrice > 0) {
            const margin = ((sellingPrice - costPrice) / costPrice * 100).toFixed(2);
            document.getElementById('profit_margin').textContent = margin + '%';
        } else {
            document.getElementById('profit_margin').textContent = '0%';
        }
    }

    // ربط الأحداث
    document.getElementById('cost_price').addEventListener('input', calculateProfitMargin);
    document.getElementById('selling_price').addEventListener('input', calculateProfitMargin);

    // معاينة الصورة قبل الرفع
    const productImage = document.getElementById('image');
    const imagePreview = document.querySelector('.image-preview');

    productImage.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                imagePreview.innerHTML = '<img src="' + e.target.result + '" class="img-thumbnail" style="max-height: 150px;">';
            };
            reader.readAsDataURL(this.files[0]);
        } else {
            imagePreview.innerHTML = '<i class="fas fa-camera fa-2x text-muted"></i><div class="text-muted mt-2"><?= __('انقر لاختيار صورة') ?></div>';
        }
    });
});
</script>
