<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title">
                    <i class="fas fa-plus-circle"></i> <?= __('إضافة منتج جديد') ?>
                </h1>
                <a href="<?= base_url('inventory/products') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> <?= __('العودة إلى قائمة المنتجات') ?>
                </a>
            </div>
        </div>
    </div>

 <!-- نموذج إضافة المنتج -->
    <div class="form-container">
        <form id="productForm" method="POST" action="<?= base_url('inventory/products/store') ?>" enctype="multipart/form-data">
            
            <!-- المعلومات الأساسية -->
            <div class="form-section">
                <h5><i class="fas fa-info-circle me-2"></i>المعلومات الأساسية</h5>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="product_code" class="form-label">كود المنتج <span class="required">*</span></label>
                        <input type="text" class="form-control" id="product_code" name="product_code" required>
                        <div class="form-text">كود فريد للمنتج (مثل: PROD001)</div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="barcode" class="form-label">الباركود</label>
                        <input type="text" class="form-control" id="barcode" name="barcode">
                        <div class="form-text">باركود المنتج (اختياري)</div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="product_name_ar" class="form-label">اسم المنتج (عربي) <span class="required">*</span></label>
                        <input type="text" class="form-control" id="product_name_ar" name="product_name_ar" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="product_name_en" class="form-label">اسم المنتج (إنجليزي)</label>
                        <input type="text" class="form-control" id="product_name_en" name="product_name_en">
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="description_ar" class="form-label">الوصف (عربي)</label>
                        <textarea class="form-control" id="description_ar" name="description_ar" rows="3"></textarea>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="description_en" class="form-label">الوصف (إنجليزي)</label>
                        <textarea class="form-control" id="description_en" name="description_en" rows="3"></textarea>
                    </div>
                </div>
            </div>

            <!-- التصنيف والوحدة -->
            <div class="form-section">
                <h5><i class="fas fa-tags me-2"></i>التصنيف والوحدة</h5>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="category_id" class="form-label">الفئة <span class="required">*</span></label>
                        <select class="form-select" id="category_id" name="category_id" required>
                            <option value="">اختر الفئة</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?= $category['category_id'] ?>">
                                    <?= htmlspecialchars($category['category_name_ar']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="unit_id" class="form-label">وحدة القياس <span class="required">*</span></label>
                        <select class="form-select" id="unit_id" name="unit_id" required>
                            <option value="">اختر الوحدة</option>
                            <?php foreach ($units as $unit): ?>
                                <option value="<?= $unit['unit_id'] ?>">
                                    <?= htmlspecialchars($unit['unit_name_ar']) ?> (<?= htmlspecialchars($unit['unit_symbol_ar']) ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="product_type" class="form-label">نوع المنتج <span class="required">*</span></label>
                        <select class="form-select" id="product_type" name="product_type" required>
                            <option value="product">منتج</option>
                            <option value="service">خدمة</option>
                            <option value="digital">رقمي</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- الأسعار -->
            <div class="form-section">
                <h5><i class="fas fa-dollar-sign me-2"></i>الأسعار</h5>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="cost_price" class="form-label">سعر التكلفة <span class="required">*</span></label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="cost_price" name="cost_price" step="0.01" min="0" required>
                            <span class="input-group-text">ر.س</span>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="selling_price" class="form-label">سعر البيع <span class="required">*</span></label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="selling_price" name="selling_price" step="0.01" min="0" required>
                            <span class="input-group-text">ر.س</span>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="tax_rate" class="form-label">معدل الضريبة (%)</label>
                        <input type="number" class="form-control" id="tax_rate" name="tax_rate" step="0.01" min="0" max="100" value="15">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">هامش الربح</label>
                        <div class="form-control-plaintext" id="profit_margin">0%</div>
                    </div>
                </div>
            </div>

            <!-- إعدادات المخزون -->
            <div class="form-section">
                <h5><i class="fas fa-warehouse me-2"></i>إعدادات المخزون</h5>
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="track_inventory" name="track_inventory" value="1" checked>
                            <label class="form-check-label" for="track_inventory">
                                تتبع المخزون
                            </label>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="min_stock_level" class="form-label">الحد الأدنى للمخزون</label>
                        <input type="number" class="form-control" id="min_stock_level" name="min_stock_level" step="0.01" min="0" value="0">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="max_stock_level" class="form-label">الحد الأقصى للمخزون</label>
                        <input type="number" class="form-control" id="max_stock_level" name="max_stock_level" step="0.01" min="0">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="reorder_point" class="form-label">نقطة إعادة الطلب</label>
                        <input type="number" class="form-control" id="reorder_point" name="reorder_point" step="0.01" min="0" value="0">
                    </div>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="form-section">
                <h5><i class="fas fa-cog me-2"></i>معلومات إضافية</h5>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="weight" class="form-label">الوزن (كجم)</label>
                        <input type="number" class="form-control" id="weight" name="weight" step="0.001" min="0">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="dimensions" class="form-label">الأبعاد</label>
                        <input type="text" class="form-control" id="dimensions" name="dimensions" placeholder="الطول × العرض × الارتفاع">
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="image" class="form-label">صورة المنتج</label>
                        <input type="file" class="form-control" id="image" name="image" accept="image/*">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">معاينة الصورة</label>
                        <div class="image-preview" onclick="document.getElementById('image').click()">
                            <i class="fas fa-camera fa-2x text-muted"></i>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" checked>
                            <label class="form-check-label" for="is_active">
                                منتج نشط
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار الحفظ -->
            <div class="d-flex justify-content-between">
                <a href="<?= base_url('inventory/products') ?>" class="btn btn-secondary">
                    <i class="fas fa-times me-2"></i>
                    إلغاء
                </a>
                <div>
                    <button type="submit" class="btn btn-success me-2">
                        <i class="fas fa-save me-2"></i>
                        حفظ المنتج
                    </button>
                    <button type="submit" name="save_and_new" value="1" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        حفظ وإضافة آخر
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من صحة النموذج قبل الإرسال
    const form = document.getElementById('product-form');
    form.addEventListener('submit', function(event) {
        let isValid = true;

        // التحقق من كود المنتج
        const productCode = document.getElementById('product_code').value.trim();
        if (productCode === '') {
            isValid = false;
            document.getElementById('product_code').classList.add('is-invalid');
        } else {
            document.getElementById('product_code').classList.remove('is-invalid');
        }

        // التحقق من اسم المنتج
        const productName = document.getElementById('product_name').value.trim();
        if (productName === '') {
            isValid = false;
            document.getElementById('product_name').classList.add('is-invalid');
        } else {
            document.getElementById('product_name').classList.remove('is-invalid');
        }

        // التحقق من الأسعار
        const costPrice = parseFloat(document.getElementById('cost_price').value) || 0;
        const sellingPrice = parseFloat(document.getElementById('selling_price').value) || 0;
        
        if (costPrice <= 0) {
            isValid = false;
            document.getElementById('cost_price').classList.add('is-invalid');
        } else {
            document.getElementById('cost_price').classList.remove('is-invalid');
        }

        if (sellingPrice <= 0) {
            isValid = false;
            document.getElementById('selling_price').classList.add('is-invalid');
        } else {
            document.getElementById('selling_price').classList.remove('is-invalid');
        }

        // تحذير إذا كان سعر البيع أقل من التكلفة
        if (sellingPrice > 0 && costPrice > 0 && sellingPrice < costPrice) {
            if (!confirm('<?= __('سعر البيع أقل من سعر التكلفة. هل تريد المتابعة؟') ?>')) {
                isValid = false;
            }
        }

        if (!isValid) {
            event.preventDefault();
        }
    });

    // حساب هامش الربح تلقائياً
    function calculateProfitMargin() {
        const costPrice = parseFloat(document.getElementById('cost_price').value) || 0;
        const sellingPrice = parseFloat(document.getElementById('selling_price').value) || 0;

        if (costPrice > 0) {
            const margin = ((sellingPrice - costPrice) / costPrice * 100).toFixed(2);
            document.getElementById('profit_margin').value = margin;
        } else {
            document.getElementById('profit_margin').value = '0';
        }
    }

    // ربط الأحداث
    document.getElementById('cost_price').addEventListener('input', calculateProfitMargin);
    document.getElementById('selling_price').addEventListener('input', calculateProfitMargin);

    // معاينة الصورة قبل الرفع
    const productImage = document.getElementById('product_image');
    const imagePreview = document.getElementById('image-preview');

    productImage.addEventListener('change', function() {
        imagePreview.innerHTML = '';

        if (this.files && this.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = document.createElement('img');
                img.src = e.target.result;
                img.classList.add('img-thumbnail', 'mt-2');
                img.style.maxHeight = '150px';
                imagePreview.appendChild(img);
            };
            reader.readAsDataURL(this.files[0]);
        }
    });
});
</script>
