<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title">
                    <i class="fas fa-chart-line text-primary"></i> <?= $title ?? 'تقارير المخزون' ?>
                </h1>
                <a href="<?= base_url('inventory') ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i> العودة للوحة التحكم
                </a>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (isset($_SESSION['flash_message'])): ?>
        <div class="alert alert-<?= $_SESSION['flash_message']['type'] ?> alert-dismissible fade show" role="alert">
            <?= $_SESSION['flash_message']['message'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['flash_message']); ?>
    <?php endif; ?>

    <!-- Report Categories -->
    <div class="row">
        <!-- Stock Reports -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-boxes me-2"></i> تقارير المخزون
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('stock_summary')">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-chart-bar text-primary me-2"></i> ملخص أرصدة المخزون
                                </h6>
                                <small class="text-muted">عام</small>
                            </div>
                            <p class="mb-1 text-muted">تقرير شامل بأرصدة جميع المنتجات في المخازن</p>
                        </a>
                        
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('stock_valuation')">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-dollar-sign text-success me-2"></i> تقييم المخزون
                                </h6>
                                <small class="text-muted">مالي</small>
                            </div>
                            <p class="mb-1 text-muted">تقرير بقيمة المخزون حسب التكلفة والسعر</p>
                        </a>
                        
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('out_of_stock')">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-exclamation-circle text-danger me-2"></i> المنتجات النافدة
                                </h6>
                                <small class="text-muted">تنبيه</small>
                            </div>
                            <p class="mb-1 text-muted">قائمة بالمنتجات النافدة من المخزون</p>
                        </a>
                        
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('reorder_report')">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-exclamation-triangle text-warning me-2"></i> تقرير إعادة الطلب
                                </h6>
                                <small class="text-muted">تنبيه</small>
                            </div>
                            <p class="mb-1 text-muted">المنتجات التي تحتاج إعادة طلب</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Movement Reports -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exchange-alt me-2"></i> تقارير الحركات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('movement_summary')">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-list text-info me-2"></i> ملخص حركات المخزون
                                </h6>
                                <small class="text-muted">عام</small>
                            </div>
                            <p class="mb-1 text-muted">تقرير بجميع حركات المخزون خلال فترة محددة</p>
                        </a>
                        
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('in_movements')">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-arrow-down text-success me-2"></i> حركات الإدخال
                                </h6>
                                <small class="text-muted">إدخال</small>
                            </div>
                            <p class="mb-1 text-muted">تقرير بحركات إدخال المخزون فقط</p>
                        </a>
                        
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('out_movements')">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-arrow-up text-danger me-2"></i> حركات الإخراج
                                </h6>
                                <small class="text-muted">إخراج</small>
                            </div>
                            <p class="mb-1 text-muted">تقرير بحركات إخراج المخزون فقط</p>
                        </a>
                        
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('transfer_movements')">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-exchange-alt text-warning me-2"></i> حركات النقل
                                </h6>
                                <small class="text-muted">نقل</small>
                            </div>
                            <p class="mb-1 text-muted">تقرير بحركات نقل المخزون بين المخازن</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Category Reports -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tags me-2"></i> تقارير الفئات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('category_summary')">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-chart-pie text-success me-2"></i> ملخص الفئات
                                </h6>
                                <small class="text-muted">عام</small>
                            </div>
                            <p class="mb-1 text-muted">تقرير بإحصائيات المنتجات حسب الفئات</p>
                        </a>
                        
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('category_valuation')">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-dollar-sign text-success me-2"></i> تقييم الفئات
                                </h6>
                                <small class="text-muted">مالي</small>
                            </div>
                            <p class="mb-1 text-muted">تقرير بقيمة المخزون حسب الفئات</p>
                        </a>
                        
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('category_performance')">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-chart-line text-success me-2"></i> أداء الفئات
                                </h6>
                                <small class="text-muted">تحليلي</small>
                            </div>
                            <p class="mb-1 text-muted">تقرير بأداء الفئات من حيث الحركة والمبيعات</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Warehouse Reports -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-warehouse me-2"></i> تقارير المخازن
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('warehouse_summary')">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-chart-bar text-warning me-2"></i> ملخص المخازن
                                </h6>
                                <small class="text-muted">عام</small>
                            </div>
                            <p class="mb-1 text-muted">تقرير بإحصائيات المخزون في كل مخزن</p>
                        </a>
                        
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('warehouse_capacity')">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-expand-arrows-alt text-warning me-2"></i> سعة المخازن
                                </h6>
                                <small class="text-muted">سعة</small>
                            </div>
                            <p class="mb-1 text-muted">تقرير بسعة واستخدام المخازن</p>
                        </a>
                        
                        <a href="#" class="list-group-item list-group-item-action" onclick="generateReport('warehouse_valuation')">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <i class="fas fa-dollar-sign text-warning me-2"></i> تقييم المخازن
                                </h6>
                                <small class="text-muted">مالي</small>
                            </div>
                            <p class="mb-1 text-muted">تقرير بقيمة المخزون في كل مخزن</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Reports Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i> تقارير سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <button type="button" class="btn btn-outline-danger btn-block h-100" onclick="generateQuickReport('out_of_stock')">
                                <i class="fas fa-exclamation-circle fa-2x mb-2"></i>
                                <br>المنتجات النافدة
                                <br><small class="text-muted">فوري</small>
                            </button>
                        </div>
                        <div class="col-md-3 mb-3">
                            <button type="button" class="btn btn-outline-warning btn-block h-100" onclick="generateQuickReport('need_reorder')">
                                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                                <br>يحتاج إعادة طلب
                                <br><small class="text-muted">فوري</small>
                            </button>
                        </div>
                        <div class="col-md-3 mb-3">
                            <button type="button" class="btn btn-outline-success btn-block h-100" onclick="generateQuickReport('stock_valuation')">
                                <i class="fas fa-dollar-sign fa-2x mb-2"></i>
                                <br>قيمة المخزون
                                <br><small class="text-muted">فوري</small>
                            </button>
                        </div>
                        <div class="col-md-3 mb-3">
                            <button type="button" class="btn btn-outline-info btn-block h-100" onclick="generateQuickReport('stock_summary')">
                                <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                <br>ملخص المخزون
                                <br><small class="text-muted">فوري</small>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Report Parameters Modal -->
<div class="modal fade" id="reportModal" tabindex="-1" aria-labelledby="reportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="reportModalLabel">
                    <i class="fas fa-chart-line me-2"></i> إعدادات التقرير
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="reportForm" method="POST" target="_blank">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="report_date_from" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="report_date_from" name="date_from" 
                                   value="<?= date('Y-m-01') ?>">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="report_date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="report_date_to" name="date_to" 
                                   value="<?= date('Y-m-d') ?>">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="report_warehouse" class="form-label">المخزن</label>
                            <select class="form-select" id="report_warehouse" name="warehouse_id">
                                <option value="">جميع المخازن</option>
                                <?php if (isset($warehouses)): ?>
                                    <?php foreach ($warehouses as $warehouse): ?>
                                        <option value="<?= $warehouse['warehouse_id'] ?>">
                                            <?= htmlspecialchars($warehouse['warehouse_name_ar']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="report_category" class="form-label">الفئة</label>
                            <select class="form-select" id="report_category" name="category_id">
                                <option value="">جميع الفئات</option>
                                <?php if (isset($categories)): ?>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?= $category['category_id'] ?>">
                                            <?= htmlspecialchars($category['category_name_ar']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="report_format" class="form-label">تنسيق التقرير</label>
                            <select class="form-select" id="report_format" name="format">
                                <option value="html">HTML (عرض في المتصفح)</option>
                                <option value="pdf">PDF (ملف PDF)</option>
                                <option value="excel">Excel (ملف Excel)</option>
                                <option value="csv">CSV (ملف CSV)</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="report_language" class="form-label">لغة التقرير</label>
                            <select class="form-select" id="report_language" name="language">
                                <option value="ar">العربية</option>
                                <option value="en">English</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-chart-line me-1"></i> إنشاء التقرير
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function generateReport(reportType) {
    const reportForm = document.getElementById('reportForm');
    reportForm.action = `<?= base_url('inventory/reports/') ?>${reportType}`;
    
    const reportModal = new bootstrap.Modal(document.getElementById('reportModal'));
    reportModal.show();
}

function generateQuickReport(reportType) {
    // Generate report immediately without parameters
    const url = `<?= base_url('inventory/reports/') ?>${reportType}?quick=1`;
    window.open(url, '_blank');
}

// Set default dates
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    
    document.getElementById('report_date_from').value = firstDay.toISOString().split('T')[0];
    document.getElementById('report_date_to').value = today.toISOString().split('T')[0];
});
</script>

<style>
.btn-block {
    display: block;
    width: 100%;
}

.list-group-item-action:hover {
    background-color: #f8f9fa;
}

.card {
    transition: all 0.3s;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}
</style>
